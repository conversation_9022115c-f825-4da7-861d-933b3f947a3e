<template>
  <div class="personal">
    <KnowledgeBaseCard
      class="mt-lg"
      :knowledegBaseList="knowledegBaseList"
      @add="add"
      @edit="edit"
      @remove="remove"
    />
  </div>
  <div>
    <a-modal :okButtonProps="{ loading }" width="540px" v-model:visible="showForm" :title="title" @ok="submit"
      ok-text="确认" cancel-text="取消"
    >
      <a-form ref="form" :model="formData" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16, offset: 1 }">
        <a-form-item required name="namespace" label="名称"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input v-model:value="formData.namespace" placeholder="请输入名称" show-count :maxlength=100 />
        </a-form-item>
        <a-form-item required name="description" label="描述"
          :rules="[{ required: true, message: '请输入描述' }]"
        >
          <a-textarea v-model:value="formData.description" placeholder="请输入描述" show-count :maxlength=200 />
        </a-form-item>
        <a-form-item name="bg" label="封面">
          <a-upload list-type="picture-card" accept=".png,.jpg,.jpeg,.webp,.gif"
              v-model:fileList="image_urls"
              :show-upload-list="false"
              :customRequest="customRequest"
              :before-upload="beforeUpload"
              @change="handleChange"
            >
            <div>
              <img v-if="formData.bg" :src="formData.bg" alt="avatar" style="width: 100px;height: 100px;"/>
              <div v-else>
                <loading-outlined v-if="loading"></loading-outlined>
                <plus-outlined v-else></plus-outlined>
                <div class="ant-upload-text">上传</div>
              </div>
            </div>
          </a-upload>
        </a-form-item>
        <a-form-item required name="share_scope" label="共享范围">
          <a-radio-group v-model:value="formData.share_scope" @change="handleShareScopeChange">
            <a-radio value="0">仅自己</a-radio>
            <a-radio value="1">全员</a-radio>
            <a-radio value="2">课程</a-radio>
            <a-radio value="3">团队</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          required
          :wrapper-col="{ span: 16, offset: 6 }"
          name="account_column"
          :rules="[{required: true, validator: checkCourseSemester, trigger: ['change', 'blur']}]"
          v-if="formData.share_scope === '2'">
          <a-select
            style="width: 40%;"
            placeholder="请选择课程"
            v-model:value="formData.course"
            :options="accountInfoCourseColumnList.map(item => ({ label: item, value: item }))"
            @change="handleCourseChange"
          ></a-select>
          &nbsp;
          <a-select
            style="width: 40%;"
            placeholder="请选择学期"
            v-model:value="formData.semester"
            :options="accountInfoSemesterColumnList.map(item => ({ label: item, value: item }))"
          ></a-select>
        </a-form-item>
        <a-form-item
          required
          :wrapper-col="{ span: 16, offset: 6 }"
          name="team_id"
          :rules="[{required: true, validator: checkTeamSelection, trigger: ['change', 'blur']}]"
          v-if="formData.share_scope === '3'">
          <a-select
            mode="multiple"
            style="width: 80%;"
            placeholder="请选择团队"
            v-model:value="formData.team_id"
            :options="teamList.map(item => ({ label: item.name, value: item.id }))"
          ></a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import {computed, reactive, ref, onMounted, watch} from 'vue';
  import { FormInstance, UploadProps, UploadChangeParam } from 'ant-design-vue';
  import { useKnowledegBaseStore, KnowledegBaseProps } from '@/store/knowledge';
  import { getAccountInfo } from "@/api/account";
  import { getTeamList } from "@/api/team";
  import { storeToRefs } from 'pinia';
  import { useAuthStore } from '@/plugins';
  import KnowledgeBaseCard from './KnowledgeBaseCard.vue';
  import OSS from 'ali-oss';
  import http from '@/store/http';
  import { OssStsTokenProps } from '@/store/thirdparty';
  import { Response } from '@/types';
  import { v4 as uuidv4 } from 'uuid';
  import { message } from 'ant-design-vue';
  import {useBaseStore} from "@/store/base";

  const baseStore = useBaseStore();
  const { headerAddKnowledgeActive } = storeToRefs(baseStore);

  const { useAuth } = useAuthStore();

  const loading = ref(false);
  const showForm = ref(false);
  const title = ref("创建知识库");
  const baseFilePath = ref<string>("file/knowledge/base/cover");
  const image_urls = ref([]);
  let ossStsToken = ref<OssStsTokenProps>();
  let fileDict = ref({});

  const formData = reactive<any>({
    id: undefined,
    namespace: '',
    bg: '',
    share_scope: '0',
    description: undefined,
    create_time: undefined,
    semester: null,
    course: null,
    team_id: [],
  });

  const form = ref<FormInstance>();

  function handleShareScopeChange(value){
    formData.account_column = null;
    formData.team_id = [];
  }

  const checkCourseSemester = function(rule, value, callback){
    if (!formData.course) {
      return Promise.reject("请选择课程")
    } else if (!formData.semester) {
      return Promise.reject("请选择学期")
    }else{
      return  Promise.resolve();
    }
  }

  const checkTeamSelection = function(rule, value, callback){
    if (!formData.team_id || formData.team_id.length === 0) {
      return Promise.reject("请选择团队")
    }else{
      return  Promise.resolve();
    }
  }

  function handleCourseChange(){
    formData.semester = null;
    getAccountInfoList('semester');
  }

  function submit() {
    loading.value = true;
    form.value
      ?.validate()
      .then(() => {
        formData.id ? updateKnowledegBase(formData) : addKnowledegBase(formData);
        showForm.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const edit = useAuth('edit', function (record: any) {
    form.value?.resetFields();
    formData.id = record.id;
    formData.namespace = record.namespace;
    formData.description = record.description;
    formData.bg = record.bg;
    formData.share_scope = ""+record.share_scope;
    formData.semester = record.semester;
    formData.course = record.course;
    formData.team_id = Array.isArray(record.team_ids) ? record.team_ids : (record.team_ids ? [record.team_ids] : []);
    showForm.value = true;
    title.value = '编辑';
  });

  watch(headerAddKnowledgeActive, (val) => {
    if (val === true) {
      add();
      headerAddKnowledgeActive.value = false;
    }
  })

  function add() {
    form.value?.resetFields();
    formData.id = undefined;
    formData.namespace = '';
    formData.description = '';
    formData.bg = '';
    formData.title = undefined;
    formData.share_scope = '0',
    formData.semester = null;
    formData.course = null;
    formData.team_id = [];
    showForm.value = true;
    title.value = '创建知识库';
  }


  const remove = useAuth('delete', function (record: KnowledegBaseProps) {
    removeKnowledegBase(record.id!);
  });

  function getOssStsToken() {
    return http
      .request<OssStsTokenProps, Response<OssStsTokenProps>>('/thirdparty/oss/sts_token?public=1', 'GET')
      .then((res) => {
        console.log(res);
        const { data } = res;
        ossStsToken.value = data;
        return data;
      })
      .finally(() => (console.log("end")));
  }

  const beforeUpload: UploadProps['beforeUpload'] = async file => {
    const fileExtName = file.name.substring(file.name.lastIndexOf(".") + 1);
    const fileName = uuidv4() + "." + fileExtName;
    const oss_key = baseFilePath.value + '/' + fileName;

    const isLt1M = file.size / 1024 / 1024 < 1;
    if (!isLt1M) {
      message.error('单个图片最大1MB!');
      return false;
    }

    const fileData = {name: file.name, oss_key: oss_key, size: file.size, status: 0, url: ""};
    console.log(fileData);
    fileDict.value[file.uid] = fileData;
    return true;
  }

  const handleChange = async (info: UploadChangeParam) => {
    const status = info.file.status;
    console.log(info)
    console.log(fileDict.value)

    if (info.file.status === 'uploading') {
      loading.value = true;
      return;
    }
    if (info.file.status === 'done') {
      formData.bg = fileDict.value[info.file.uid]["url"];
    }
    if (info.file.status === 'error') {
      loading.value = false;
      message.error('upload error');
    }

    if(status === 'removed') {
      delete fileDict.value[info.file.uid];
    }
  };

  async function customRequest(info) {
    console.log(info);
    const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
    const client = new OSS({
      region: ossStsToken.value.region,
      accessKeyId: ossStsToken.value.access_key_id,
      accessKeySecret: ossStsToken.value.access_key_secret,
      stsToken: ossStsToken.value.security_token,
      bucket: ossStsToken.value.bucket_name,
      endpoint: ossStsToken.value.endpoint,
      useFetch: true,
      cname: use_cname,
      secure: true,
    });

    const oss_key = fileDict.value[info.file.uid]["oss_key"];
    if(info.file){
      console.log(oss_key)
      await client.multipartUpload(oss_key, info.file, {
        parallel: 4,
        partSize: 100 * 1024,
        progress: function (percent) {
          console.log("progress is: ", percent * 100);
          loading.value = true;
          if (percent === 1){
            loading.value = false;
          }
        },
      }).then(res => {
        console.log('结果:', res);
        fileDict.value[info.file.uid]["status"] = 1;
        fileDict.value[info.file.uid]["url"] = ossStsToken.value.endpoint + '/' + oss_key;
        fileDict.value = fileDict.value;
        formData.bg = fileDict.value[info.file.uid]["url"];
      }).catch((err) => {
        console.log(err);
      });
    }
  }

  const accountInfoSemesterColumnList = ref([]);
  const accountInfoCourseColumnList = ref([]);
  const teamList = ref([]);
  function getAccountInfoList(account_column){
    let params = {}
    if (account_column === 'courses'){
      params = {account_column, semester: formData.semester};
    }else{
      params = {account_column}
    }
    getAccountInfo(params).then((res) => {
      const { data } = res;
      console.log(data);
      if (account_column === 'semester'){
        accountInfoSemesterColumnList.value = data;
      }else{
        accountInfoCourseColumnList.value = data;
      }
      return data;
    });
  }

  function getTeamListData(){
    getTeamList({}).then((res) => {
      const { data } = res;
      teamList.value = data || [];
      return data;
    });
  }
  const knowledegBaseStore = useKnowledegBaseStore();

  const { getKnowledegBaseList, addKnowledegBase, updateKnowledegBase, removeKnowledegBase } = knowledegBaseStore;
  const { knowledegBaseList } = storeToRefs(knowledegBaseStore);

  onMounted(() => {
    getKnowledegBaseList();
    getOssStsToken();
    getAccountInfoList('semester');
    getAccountInfoList('courses');
    getTeamListData();
  });

</script>

<style lang="less" scoped>
  .personal {
    .banner {
      height: 240px;
      background-image: url('@/assets/personal-bg.png');
      background-position: 50% 10%;
      background-size: cover;
      position: relative;

      .profile {
        width: calc(~'100% - 48px');
      }

      :deep(.navi) {
        .ant-breadcrumb-link,
        .ant-breadcrumb-separator {
          color: rgba(255, 255, 255, 0.65);
        }

        & > span:last-child .ant-breadcrumb-link {
          @apply text-text-inverse;
        }
      }
    }
  }
</style>
