<template>
  <a-modal 
    :visible="visible" 
    title="字体设置" 
    @ok="handleClose"
    @cancel="handleClose"
    @close="handleClose"
    width="900px"
    :footer="null"
  >
    <a-tabs v-model:activeKey="fontSettingsActiveKey">
      <a-tab-pane key="fontSizes" tab="字体大小">
        <div style="padding: 20px;">
          <a-form layout="vertical">
            <a-form-item label="标题文字大小">
              <div class="font-size-slider">
                <span class="font-size-label">{{ fontSettings.titleSize }}px</span>
              </div>
              <a-slider 
                  v-model:value="fontSettings.titleSize" 
                  :min="12" 
                  :max="48"
                  style="flex: 1;"
                />
              <div class="font-size-preview" :style="{ fontSize: fontSettings.titleSize + 'px', fontWeight: 'bold' }">
                标题文字预览
              </div>
            </a-form-item>
            
            <a-form-item label="说明文字大小">
              <div class="font-size-slider">
                <span class="font-size-label">{{ fontSettings.descriptionSize }}px</span>
              </div>
              <a-slider 
                  v-model:value="fontSettings.descriptionSize" 
                  :min="12" 
                  :max="36"
                  style="flex: 1;"
                />
              <div class="font-size-preview" :style="{ fontSize: fontSettings.descriptionSize + 'px' }">
                说明文字预览
              </div>
            </a-form-item>
            
            <a-form-item label="用户消息大小">
              <div class="font-size-slider">
                <span class="font-size-label">{{ fontSettings.userMsgSize }}px</span>
              </div>
              <a-slider 
                  v-model:value="fontSettings.userMsgSize" 
                  :min="12" 
                  :max="36"
                  style="flex: 1;"
                />
              <div class="font-size-preview" :style="{ fontSize: fontSettings.userMsgSize + 'px' }">
                用户消息预览
              </div>
            </a-form-item>
            
            <a-form-item label="机器人消息大小">
              <div class="font-size-slider">
                <span class="font-size-label">{{ fontSettings.botMsgSize }}px</span>
              </div>
              <a-slider 
                  v-model:value="fontSettings.botMsgSize" 
                  :min="12" 
                  :max="36"
                  style="flex: 1;"
                />
              <div class="font-size-preview" :style="{ fontSize: fontSettings.botMsgSize + 'px' }">
                机器人消息预览
              </div>
            </a-form-item>
            
            <a-form-item label="按钮文字大小">
              <div class="font-size-slider">
                <span class="font-size-label">{{ fontSettings.buttonTextSize }}px</span>
              </div>
              <a-slider 
                  v-model:value="fontSettings.buttonTextSize" 
                  :min="10" 
                  :max="24"
                  style="flex: 1;"
                />
              <div class="font-size-preview button-preview" :style="{ fontSize: fontSettings.buttonTextSize + 'px' }">
                按钮文字预览
              </div>
            </a-form-item>
          </a-form>
        </div>
      </a-tab-pane>
      
      <a-tab-pane key="fontColors" tab="字体颜色">
        <div style="padding: 20px;">
          <a-form layout="vertical">
            <a-form-item label="标题文字颜色">
              <div class="color-picker-container">
                <div class="color-preview" :style="{ backgroundColor: fontSettings.titleColor }"></div>
                <span class="color-value">{{ fontSettings.titleColor }}</span>
                <a-button @click="showColorPicker('title')">选择颜色</a-button>
              </div>
              <div class="font-color-preview" :style="{ color: fontSettings.titleColor, fontWeight: 'bold', fontSize: '20px' }">
                标题文字颜色预览
              </div>
            </a-form-item>
            
            <a-form-item label="说明文字颜色">
              <div class="color-picker-container">
                <div class="color-preview" :style="{ backgroundColor: fontSettings.descriptionColor }"></div>
                <span class="color-value">{{ fontSettings.descriptionColor }}</span>
                <a-button @click="showColorPicker('description')">选择颜色</a-button>
              </div>
              <div class="font-color-preview" :style="{ color: fontSettings.descriptionColor, fontSize: '16px' }">
                说明文字颜色预览
              </div>
            </a-form-item>
            
            <a-form-item label="用户消息颜色">
              <div class="color-picker-container">
                <div class="color-preview" :style="{ backgroundColor: fontSettings.userMsgColor }"></div>
                <span class="color-value">{{ fontSettings.userMsgColor }}</span>
                <a-button @click="showColorPicker('userMsg')">选择颜色</a-button>
              </div>
              <div class="font-color-preview" :style="{ color: fontSettings.userMsgColor, fontSize: '16px' }">
                用户消息颜色预览
              </div>
            </a-form-item>
            
            <a-form-item label="机器人消息颜色">
              <div class="color-picker-container">
                <div class="color-preview" :style="{ backgroundColor: fontSettings.botMsgColor }"></div>
                <span class="color-value">{{ fontSettings.botMsgColor }}</span>
                <a-button @click="showColorPicker('botMsg')">选择颜色</a-button>
              </div>
              <div class="font-color-preview" :style="{ color: fontSettings.botMsgColor, fontSize: '16px' }">
                机器人消息颜色预览
              </div>
            </a-form-item>
            
            <a-form-item label="按钮文字颜色">
              <div class="color-picker-container">
                <div class="color-preview" :style="{ backgroundColor: fontSettings.buttonTextColor }"></div>
                <span class="color-value">{{ fontSettings.buttonTextColor }}</span>
                <a-button @click="showColorPicker('buttonText')">选择颜色</a-button>
              </div>
              <div class="font-color-preview button-text-preview" :style="{ color: fontSettings.buttonTextColor, fontSize: '14px' }">
                按钮文字颜色预览
              </div>
            </a-form-item>
          </a-form>
        </div>
      </a-tab-pane>
    </a-tabs>
    
    <div style="margin-top: 20px; text-align: right;">
      <a-button style="margin-right: 8px;" @click="resetFontSettings">重置</a-button>
      <a-button type="primary" @click="confirm">确定</a-button>
    </div>
  </a-modal>
  
  <!-- 颜色选择器弹窗 -->
  <a-modal
    v-model:visible="colorPickerVisible"
    :title="'选择' + colorPickerTitle + '颜色'"
    :footer="null"
    @close="handleCloseColorPicker"
    width="400px"
  >
    <div class="color-picker-modal">
      <!-- 颜色选择区域 -->
      <div class="color-gradient" ref="colorGradient" @mousedown="startPickingColor">
        <div class="color-picker-indicator" :style="{ left: colorIndicatorPos.x + 'px', top: colorIndicatorPos.y + 'px' }"></div>
      </div>
      
      <!-- 色相选择条 -->
      <div class="hue-slider">
        <input type="range" min="0" max="360" v-model="hue" @input="updateColor" />
      </div>
      
      <!-- 颜色样本 -->
      <div class="color-sample" :style="{ backgroundColor: pickedColor }">
        <div class="color-value">{{ pickedColor }}</div>
      </div>
      
      <!-- RGB输入 -->
      <div class="rgb-inputs">
        <div class="rgb-input">
          <label>R</label>
          <a-input-number v-model:value="rgb.r" :min="0" :max="255" @change="updateColorFromRGB" />
        </div>
        <div class="rgb-input">
          <label>G</label>
          <a-input-number v-model:value="rgb.g" :min="0" :max="255" @change="updateColorFromRGB" />
        </div>
        <div class="rgb-input">
          <label>B</label>
          <a-input-number v-model:value="rgb.b" :min="0" :max="255" @change="updateColorFromRGB" />
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="color-picker-actions">
        <a-button @click="colorPickerVisible = false">取消</a-button>
        <a-button type="primary" @click="applySelectedColor">确定</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch, nextTick, onMounted } from 'vue';

export default defineComponent({
  name: 'FontSettingsModal',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    fontSettings: {
      type: Object,
      default: () => ({
        titleSize: 24,
        descriptionSize: 16,
        userMsgSize: 16,
        botMsgSize: 16,
        buttonTextSize: 14,
        titleColor: '#1e293b',
        descriptionColor: '#475569',
        userMsgColor: '#ffffff',
        botMsgColor: '#626e79',
        buttonTextColor: '#ffffff'
      })
    }
  },
  
  emits: ['update:visible', 'update:fontSettings', 'confirm'],
  
  setup(props, { emit }) {
    const fontSettingsActiveKey = ref('fontSizes');
    const colorPickerVisible = ref(false);
    const currentColorPickerType = ref('');
    const colorPickerTitle = ref('');
    const pickedColor = ref('#1e293b');
    const hue = ref(210);
    const rgb = ref({ r: 30, g: 41, b: 59 });
    const colorIndicatorPos = ref({ x: 100, y: 100 });
    const colorGradient = ref(null);
    
    // When the modal opens, make sure the tabs render correctly
    watch(() => props.visible, (isVisible) => {
      if (isVisible) {
        nextTick(() => {
          fontSettingsActiveKey.value = 'fontColors';
          setTimeout(() => {
            fontSettingsActiveKey.value = 'fontSizes';
          }, 50);
        });
      }
    });
    
    // Show color picker
    const showColorPicker = (type) => {
      currentColorPickerType.value = type;
      switch(type) {
        case 'title':
          colorPickerTitle.value = '标题文字';
          pickedColor.value = props.fontSettings.titleColor;
          break;
        case 'description':
          colorPickerTitle.value = '说明文字';
          pickedColor.value = props.fontSettings.descriptionColor;
          break;
        case 'userMsg':
          colorPickerTitle.value = '用户消息';
          pickedColor.value = props.fontSettings.userMsgColor;
          break;
        case 'botMsg':
          colorPickerTitle.value = '机器人消息';
          pickedColor.value = props.fontSettings.botMsgColor;
          break;
        case 'buttonText':
          colorPickerTitle.value = '按钮文字';
          pickedColor.value = props.fontSettings.buttonTextColor;
          break;
      }
      
      // Convert hex to RGB
      const hexToRgb = (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16)
        } : { r: 0, g: 0, b: 0 };
      };
      
      rgb.value = hexToRgb(pickedColor.value);
      
      colorPickerVisible.value = true;
      
      // Calculate indicator position
      nextTick(() => {
        if (colorGradient.value) {
          colorGradient.value.style.setProperty('--hue', `${hue.value}deg`);
        }
      });
    };
    
    // Start picking color
    const startPickingColor = (e) => {
      pickColor(e);
      
      // Add mousemove and mouseup event listeners
      const moveHandler = (e) => pickColor(e);
      const upHandler = () => {
        document.removeEventListener('mousemove', moveHandler);
        document.removeEventListener('mouseup', upHandler);
      };
      
      document.addEventListener('mousemove', moveHandler);
      document.addEventListener('mouseup', upHandler);
    };
    
    // Pick color
    const pickColor = (e) => {
      if (!colorGradient.value) return;
      
      const rect = colorGradient.value.getBoundingClientRect();
      let x = e.clientX - rect.left;
      let y = e.clientY - rect.top;
      
      // Ensure within boundaries
      x = Math.max(0, Math.min(rect.width, x));
      y = Math.max(0, Math.min(rect.height, y));
      
      colorIndicatorPos.value = { x, y };
      
      // Calculate color from HSV
      const saturation = x / rect.width;
      const value = 1 - y / rect.height;
      
      // Convert HSV to RGB
      const h = hue.value / 360;
      const s = saturation;
      const v = value;
      
      let r, g, b;
      
      const i = Math.floor(h * 6);
      const f = h * 6 - i;
      const p = v * (1 - s);
      const q = v * (1 - f * s);
      const t = v * (1 - (1 - f) * s);
      
      switch (i % 6) {
        case 0: r = v, g = t, b = p; break;
        case 1: r = q, g = v, b = p; break;
        case 2: r = p, g = v, b = t; break;
        case 3: r = p, g = q, b = v; break;
        case 4: r = t, g = p, b = v; break;
        case 5: r = v, g = p, b = q; break;
      }
      
      rgb.value = {
        r: Math.round(r * 255),
        g: Math.round(g * 255),
        b: Math.round(b * 255)
      };
      
      // Update picked color
      pickedColor.value = `#${rgb.value.r.toString(16).padStart(2, '0')}${rgb.value.g.toString(16).padStart(2, '0')}${rgb.value.b.toString(16).padStart(2, '0')}`;
    };
    
    // Update color based on hue
    const updateColor = () => {
      if (colorGradient.value) {
        // Update CSS variable to change color picker's hue
        colorGradient.value.style.setProperty('--hue', `${hue.value}deg`);
        
        // Re-calculate color based on current position
        const rect = colorGradient.value.getBoundingClientRect();
        const saturation = colorIndicatorPos.value.x / rect.width;
        const value = 1 - colorIndicatorPos.value.y / rect.height;
        
        // Convert HSV to RGB
        const h = hue.value / 360;
        const s = saturation;
        const v = value;
        
        let r, g, b;
        
        const i = Math.floor(h * 6);
        const f = h * 6 - i;
        const p = v * (1 - s);
        const q = v * (1 - f * s);
        const t = v * (1 - (1 - f) * s);
        
        switch (i % 6) {
          case 0: r = v, g = t, b = p; break;
          case 1: r = q, g = v, b = p; break;
          case 2: r = p, g = v, b = t; break;
          case 3: r = p, g = q, b = v; break;
          case 4: r = t, g = p, b = v; break;
          case 5: r = v, g = p, b = q; break;
        }
        
        rgb.value = {
          r: Math.round(r * 255),
          g: Math.round(g * 255),
          b: Math.round(b * 255)
        };
        
        // Update picked color
        pickedColor.value = `#${rgb.value.r.toString(16).padStart(2, '0')}${rgb.value.g.toString(16).padStart(2, '0')}${rgb.value.b.toString(16).padStart(2, '0')}`;
      }
    };
    
    // Update color from RGB values
    const updateColorFromRGB = () => {
      pickedColor.value = `#${rgb.value.r.toString(16).padStart(2, '0')}${rgb.value.g.toString(16).padStart(2, '0')}${rgb.value.b.toString(16).padStart(2, '0')}`;
    };
    
    // Apply selected color
    const applySelectedColor = () => {
      const updatedSettings = { ...props.fontSettings };
      
      switch(currentColorPickerType.value) {
        case 'title':
          updatedSettings.titleColor = pickedColor.value;
          break;
        case 'description':
          updatedSettings.descriptionColor = pickedColor.value;
          break;
        case 'userMsg':
          updatedSettings.userMsgColor = pickedColor.value;
          break;
        case 'botMsg':
          updatedSettings.botMsgColor = pickedColor.value;
          break;
        case 'buttonText':
          updatedSettings.buttonTextColor = pickedColor.value;
          break;
      }
      
      emit('update:fontSettings', updatedSettings);
      colorPickerVisible.value = false;  
    };
    
    // Reset font settings
    const resetFontSettings = () => {
      emit('update:fontSettings', {
        titleSize: 24,
        descriptionSize: 16,
        userMsgSize: 16,
        botMsgSize: 16,
        buttonTextSize: 14,
        titleColor: '#1e293b',
        descriptionColor: '#475569',
        userMsgColor: '#ffffff',
        botMsgColor: '#1e293b',
        buttonTextColor: '#ffffff'
      });
    };
    
    // Confirm settings
    const confirm = () => {
      emit('confirm');
      emit('update:visible', false);
    };
    
    // Handle close
    const handleClose = () => {
      emit('update:visible', false);
    };
    
    // Handle close color picker
    const handleCloseColorPicker = () => {
      colorPickerVisible.value = false;
    };
    
    return {
      fontSettingsActiveKey,
      colorPickerVisible,
      colorPickerTitle,
      pickedColor,
      hue,
      rgb,
      colorIndicatorPos,
      colorGradient,
      showColorPicker,
      startPickingColor,
      updateColor,
      updateColorFromRGB,
      applySelectedColor,
      resetFontSettings,
      confirm,
      handleClose,
      handleCloseColorPicker
    };
  }
});
</script>

<style scoped>
.font-size-slider {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  width: 400px;
}

.font-size-label {
  min-width: 50px;
  text-align: center;
  margin-right: 10px;
  font-weight: 500;
  user-select: none;
}

:deep(.ant-slider) {
  width: 100%;
  margin: 0;
}

:deep(.ant-slider-rail), :deep(.ant-slider-track) {
  height: 8px;
}

:deep(.ant-slider-handle) {
  height: 16px;
  width: 16px;
  margin-top: -4px;
}

.font-size-preview {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.button-preview {
  background-color: #3b82f6;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.color-picker-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.color-preview {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  margin-right: 10px;
}

.color-value {
  margin-right: 10px;
  font-family: monospace;
}

.font-color-preview {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.button-text-preview {
  background-color: #3b82f6;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.color-picker-modal {
  padding: 10px;
}

.color-gradient {
  width: 100%;
  height: 200px;
  position: relative;
  background: linear-gradient(to right, #fff, hsl(var(--hue), 100%, 50%)),
              linear-gradient(to top, #000, rgba(0, 0, 0, 0));
  background-blend-mode: multiply;
  margin-bottom: 15px;
  cursor: crosshair;
  border-radius: 4px;
  --hue: 210deg;
}

.color-picker-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  position: absolute;
  transform: translate(-50%, -50%);
  pointer-events: none;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.hue-slider {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.hue-slider input {
  width: 100%;
  margin: 0;
  background: linear-gradient(
    to right,
    #f00 0%,
    #ff0 17%,
    #0f0 33%,
    #0ff 50%,
    #00f 67%,
    #f0f 83%,
    #f00 100%
  );
  height: 20px;
  -webkit-appearance: none;
  appearance: none;
  outline: none;
  border-radius: 4px;
}

.hue-slider input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 20px;
  border-radius: 2px;
  background: white;
  cursor: pointer;
  border: 1px solid #d9d9d9;
}

.color-sample {
  width: 100%;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-sample .color-value {
  color: white;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7);
  font-weight: bold;
  margin: 0;
}

.rgb-inputs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.rgb-input {
  display: flex;
  flex-direction: column;
  width: 30%;
}

.rgb-input label {
  margin-bottom: 5px;
  text-align: center;
}

.color-picker-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 