import { Response } from '@/types';
import http from "@/store/http";
import axios from 'axios';

export const getReleaseList = (params) => 
  http.request<any, Response<any>>("/app/release", 'GET', params)

export const getAllReleaseList = (params) => 
  http.request<any, Response<any>>("/app/release/all", 'GET', params)

export const saveRelease = (params) => 
  http.request<any, Response<any>>("/app/release", 'POST_JSON', params)

export const updateReleaseConf = (params) => 
  http.request<any, Response<any>>("/app/release", 'PUT_JSON', params)

export const deleteRelease = (params) => 
  http.request<any, Response<any>>("/app/release", 'DELETE', params)

export const releaseEnterQaPage = (params) =>
  http.request<any, Response<any>>("/app/release/enter/qa", 'GET', params)

export const releaseEnterQa = (params) =>
  http.request<any, Response<any>>("/app/release/enter/qa", 'POST_JSON', params)

export const releaseBatchUploadQa = (params) =>
  http.request<any, Response<any>>("/app/release/batch/upload/qa", 'POST_JSON', params)

export const deleteEnterQa = (params) => 
  http.request<any, Response<any>>("/app/release/enter/qa", 'DELETE', params)

export const getReleaseMenu = (params) =>
  http.request<any, Response<any>>("/app/release/menu", 'GET', params)

export const saveReleaseMenu = (params) => 
  http.request<any, Response<any>>("/app/release/menu", 'POST_JSON', params)

export const deleteReleaseMenu = (params) => 
  http.request<any, Response<any>>("/app/release/menu", 'DELETE', params)

export const getReleaseAgent = (params) =>
  http.request<any, Response<any>>("/app/release/agent", 'GET', params)

export const saveReleaseAgent = (params) => 
  http.request<any, Response<any>>("/app/release/agent", 'POST_JSON', params)

export const deleteReleaseAgent = (params) => 
  http.request<any, Response<any>>("/app/release/agent", 'DELETE', params)

export const getReleaseAgentFunction = (params) =>
  http.request<any, Response<any>>("/app/release/agent/function", 'GET', params)

export const saveReleaseAgentFunction = (params) => 
  http.request<any, Response<any>>("/app/release/agent/function", 'POST_JSON', params)

export const updateReleaseAgentFunctionConfig = (params) => 
  http.request<any, Response<any>>("/app/release/agent/function", 'PUT_JSON', params)

export const deleteReleaseAgentFunction = (params) => 
  http.request<any, Response<any>>("/app/release/agent/function", 'DELETE', params)

export const getFollowQuestionPage = (params) =>
  http.request<any, Response<any>>("/app/follow/question", 'GET', params)

export const saveFollowQuestion = (params) => 
  http.request<any, Response<any>>("/app/follow/question", 'POST_JSON', params)

export const deleteFollowQuestion = (params) => 
  http.request<any, Response<any>>("/app/follow/question", 'DELETE', params)

export const deleteFollowQuestionAnswer = (params) => 
  http.request<any, Response<any>>("/app/follow/question/answer", 'DELETE', params)

export const runTestFollowQuestion = (params) => 
  http.request<any, Response<any>>("/app/follow/question/run/test", 'POST_JSON', params)

export const importFollowQuestion = (params) => 
  http.request<any, Response<any>>("/app/follow/question/import", 'POST_JSON', params)

export const copyReleaseApp = (params) => 
  http.request<any, Response<any>>("/app/release/copy", 'POST_JSON', params)

export const assignReleaseApp = (params) => 
  http.request<any, Response<any>>("/app/release/assign", 'POST_JSON', params)

export const importReleaseWorkflow = (params) => 
  http.request<any, Response<any>>("/app/release/imports", 'POST_JSON', params)

export const exportReleaseWorkflow = (params) => 
  http.request<any, Response<any>>("/app/release/export", 'POST_JSON', params)

export const releaseLockInfo = (params) =>
  http.request<any, Response<any>>("/app/release/lock/info", 'GET', params, { headers: { 'X-No-Progress': 'true' } })

export const postReleaseLock = (params) =>
  http.request<any, Response<any>>("/app/release/lock/info", 'POST_JSON', {}, { params })

export const getReleaseChatPageSetting = (params) =>
  http.request<any, Response<any>>("/app/release/chat/page/setting", 'GET', params)

export const setReleaseChatPageSetting = (params) =>
  http.request<any, Response<any>>("/app/release/chat/page/setting", 'POST_JSON', params)

export const publishToQingxiaoda = (params) =>
  http.request<any, Response<any>>("/app/release/qingxiaoda/publish", 'POST_JSON', params)

export const updatePublishToQingxiaoda = (params) =>
  http.request<any, Response<any>>("/app/release/qingxiaoda/publish", 'PUT_JSON', params)

export const getQingxiaodaPublish = (params) =>
  http.request<any, Response<any>>("/app/release/qingxiaoda/publish", 'GET', params)
