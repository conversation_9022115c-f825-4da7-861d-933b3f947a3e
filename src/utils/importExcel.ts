import * as XLSX from 'xlsx';


// Excel 数据转为 json 数据
export function importExcelData(file: any) {
  //使用promise导入
  return new Promise((resolve, reject) => {
    // 通过FileReader对象读取文件
    const fileReader = new FileReader();
    //异步操作  excel文件加载完成以后触发
    fileReader.onload = (event) => {
      try {
        const { result } = event.target as any;
        // 以二进制流方式读取得到整份excel表格对象
        const workbook = XLSX.read(result, { type: "binary" });
        // 存储获取到的数据
        let data: any = []; 
        // 遍历每张工作表进行读取
        // for (const sheet in workbook.Sheets) {
        //   if (Object.prototype.hasOwnProperty.call(workbook.Sheets, sheet)) {
        //     data = data.concat(
        //       // 将工作表转换为json数据
        //       XLSX.utils.sheet_to_json(workbook.Sheets[sheet])
        //     );
        //   }
        // }
        const sheetName = Object.keys(workbook.Sheets)[0]
        // 如果Excel文件里只有一张数据工作表（比如：data），也可以不遍历，直接获取数据
        if (Object.prototype.hasOwnProperty.call(workbook.Sheets, sheetName)) {
           // 将工作表转换为json数据            
           data = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName])
        }
        resolve(data); //导出数据
      } catch (e) {
        // 这里可以抛出文件类型错误不正确的相关提示
        reject("导入失败");
      }
    };
    // 以二进制方式打开文件
    fileReader.readAsBinaryString(file);
  });
}