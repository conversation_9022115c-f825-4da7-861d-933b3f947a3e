import { Response } from '@/types';
import http from "@/store/http";

export const getDocumentProcessingProgress = (knowledge_base_id) =>
  http.get(`/resource/knowledge_base/document/processing/progress?knowledge_base_id=${knowledge_base_id}`);

export const getDocumentPreviewUrl = (document_id) =>
  http.get(`/resource/knowledge_base/document/preview?document_id=${document_id}`);

export const documentExist = (knowledge_base_id, md5) => 
  http.get(`/resource/knowledge_base/document/exist?knowledge_base_id=${knowledge_base_id}&md5=${md5}`)

export const getDatasetPage = (params) => 
  http.request<any, Response<any>>("/resource/knowledge_base/dataset", 'GET', params)

export const saveDataset = (params) =>
  http.request<any, Response<any>>("/resource/knowledge_base/dataset", 'POST_JSON', params)

export const deleteDataset = (params) => 
  http.request<any, Response<any>>("/resource/knowledge_base/dataset", 'DELETE', params)
 