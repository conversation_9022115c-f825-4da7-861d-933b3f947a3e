<template>
  <a-modal
    width="100%"
    height="100%"
    wrap-class-name="full-modal"
    :visible=showFormModalVisible
    :title="null"
    @cancel="setModalVisible(false, true)"
    :footer="null"
    :destroyOnClose="true"
  >
      <splitpanes class="default-theme"
        @resize="maskShow = true"
        @resized="maskShow = false"
        :style="{
                height: '100%',
                overflow: 'hidden',
                position: 'relative',
                border: '1px solid #ebedf0',
                padding: '2px',
                textAlign: 'left',
                background: '#fafafa',
              }">
        <pane :key="1" min-size="30">
            <a-tabs v-model:activeKey="activeKey" type="card">
              <template #rightExtra>
                <span style="display: flex; align-items: center;">
                  <span style="margin-right: 10px; font-weight: bold; display: flex; align-items: center;">
                    <span v-if="props.is_team_workflow" :class="['lock-status', lockStatus ? 'locked' : 'unlocked']">
                      <lock-outlined v-if="lockStatus" /> 
                      <unlock-outlined v-else /> 
                      {{ lockStatus ? `已锁定 - 当前编辑: ${editorName}` : '未锁定' }}
                    </span>
                    <span>{{ modelTitle }}</span>
                  </span>
                  <a-button type="link" @click="switchChatBotVisible" style="margin-right: 40px;">{{ chatBotVisible?">>":"<<"}}</a-button>
                </span>
              </template>
              <a-tab-pane key="1" tab="基本信息">
                <!-- <p style="padding-bottom: 10px;">选择模型及知识库生成应用项目标识数据，供应用项目调用时使用。</p> -->
                <a-form
                  ref="form"
                  :model="formData"
                  :labelCol="{ span: 5 }"
                  :wrapperCol="{ span: 16, offset: 1 }"
                >
                  <a-form-item required name="name" label="应用名称"
                    :rules="[{ required: true, message: '请输入应用名称' }]"
                  >
                    <a-input v-model:value="formData.name" placeholder="应用名称" show-count :maxlength="50"/>
                  </a-form-item>
                  <a-form-item required name="apikey_id" label="选择Apikey"
                    :rules="[{ required: true, message: '请选择要使用的Apikey' }]"
                  >
                    <a-select
                      placeholder="选择要使用的Apikey"
                      v-model:value="formData.apikey_id"
                      :options="apikeyList.map(item => {
                          return {
                            label: `${item.name}`,
                            value: `${item.id}`,
                          }
                        })"
                    ></a-select>
                  </a-form-item>
                  <a-form-item name="welcome" label="欢迎语" v-if="!props.isWorkflowMode">
                    <a-textarea v-model:value="formData.welcome" placeholder="进入聊天页面后会提示给访问者，可以使用双井号添加快捷提问示例使用，例如：#xxx的问题#" :rows="4" :maxlength=2000 show-count/>
                    <br/>
                    <span class="text-sm text-subtext">进入聊天页面后会提示给访问者，可以使用双井号添加快捷提问示例使用，例如：#xxx的问题#</span>
                  </a-form-item>
                  <a-form-item name="opening_statement" label="开场白" v-if="!props.isWorkflowMode">
                    <a-textarea v-model:value="formData.opening_statement" placeholder="进入聊天页面后Bot主动开场白" :rows="4" :maxlength=2000 show-count/>
                  </a-form-item>
                  <a-form-item :wrapper-col="{ span: 21, offset: 12 }">
                    <!-- <a-button @click="setModalVisible(false, true)">关闭</a-button> -->
                    <a-button style="margin-left: 10px" type="primary" @click="submit">保存设定</a-button>
                  </a-form-item>
                </a-form>
              </a-tab-pane>
              <a-tab-pane key="4" :disabled="!(formData.id > 0)" v-if="formData.mode === 1 && !props.isWorkflowMode">
                <template #tab>
                  <span>
                    Agent助手
                    <lock-outlined v-if="!(formData.id > 0)"/>
                  </span>
                </template>
                <div style="padding-left: 20px; padding-right: 20px;"
                >
                  <div style="margin-bottom: 15px;text-align: right;">
                    <a-button type="primary" @click="addAgent">
                      <template #icon>
                        <PlusOutlined />
                      </template>
                      Agent助手
                    </a-button>
                  </div>
                  <a-table :columns="agentColumns" :data-source="releaseAgents"
                    class="components-table-demo-nested"
                    :pagination="false" :scroll="{x: 1200, y: 700}">
                    <template #bodyCell="{ text, record, index, column }">
                      <template v-if="column.key === 'operation'">
                        <a style="font-weight: normal;" @click="addAgentFunction(record.id)">添加函数</a>
                        <a-divider type="vertical"/>
                        <a style="font-weight: normal;" @click="editAgent(record)">编辑</a>
                        <a-divider type="vertical"/>
                        <a-dropdown>
                          <template #overlay>
                            <a-menu>
                              <a-menu-item>
                                <a-popconfirm placement="topLeft" title="确认要再次启用吗？" @confirm="disableAgent(record, false, 'disable')" v-if="record.disable_flag" ok-text="确认" cancel-text="取消">
                                  <a>启用</a>
                                </a-popconfirm>
                                <a-popconfirm placement="topLeft" title="确认禁用吗？" @confirm="disableAgent(record, true, 'disable')" v-if="!record.disable_flag" ok-text="确认" cancel-text="取消">
                                  <a style="color: red;">禁用</a>
                                </a-popconfirm>
                              </a-menu-item>
                              <a-menu-item>
                                <a-popconfirm placement="topLeft" title="确认删除吗？" @confirm="disableAgent(record, true, 'delete')" ok-text="确认" cancel-text="取消">
                                  <a style="color: red;">删除</a>
                                </a-popconfirm>
                              </a-menu-item>
                            </a-menu>
                          </template>
                          <a>
                            更多
                            <down-outlined />
                          </a>
                        </a-dropdown>
                      </template>
                      <template v-else-if="column.dataIndex === 'update_time'">
                        {{ text?dayjs(text).format("YYYY-MM-DD HH:mm"):'-' }}
                      </template>
                      <template v-else-if="column.dataIndex === 'status'">
                        <span v-if="record.disable_flag" style="color:red;">已禁用</span>
                        <span v-else style="color: green;">正在使用</span>
                      </template>
                      <template v-else-if="column.dataIndex === 'system_prompt'">
                        <a-popover placement="top">
                          <template #content>
                            <div v-html="text"/>
                          </template>
                          {{ text }}
                        </a-popover>
                      </template>
                      <template v-else-if="column.dataIndex === 'model_name'">
                        {{ text }}
                        <a-popover placement="top" v-if="record.no_answer_reply">
                          <template #content>
                            <div v-html="record.no_answer_reply"/>
                          </template>
                          <br/><span style="color: #36bff5;">{{ record.no_answer_reply.slice(0, 6) + "..." }}</span>
                        </a-popover>
                      </template>
                    </template>
                    <template #expandedRowRender="{ record }">
                      <div style="margin-left: 50px;text-align: left;" v-if="record.functions.length === 0">
                        暂未添加函数
                      </div>
                      <a-table :columns="agentFuncColumns" :data-source="record.functions" :pagination="false"
                        v-if="record.functions.length > 0"
                      >
                        <template #bodyCell="{ text, record, index, column }">
                          <template v-if="column.key === 'operation'">
                            <span class="table-operation">
                              <a style="font-weight: normal;" @click="editAgentFunction(record)">编辑</a>
                              <a-divider type="vertical"/>
                              <a-popconfirm title="确认删除吗?" @confirm="deleteAgentFunction(record)" ok-text="确认" cancel-text="取消">
                                <a style="font-weight: normal;color: red;">删除</a>
                              </a-popconfirm>
                              <a-divider type="vertical"/>
                            </span>
                          </template>
                          <template v-else-if="column.dataIndex === 'name'">
                            {{ text }}
                          </template>
                          <template v-else-if="column.dataIndex === 'update_time'">
                            {{ text?dayjs(text).format("YYYY-MM-DD HH:mm"):'-' }}
                          </template>
                          <template v-else-if="column.dataIndex === 'description'">
                            <a-popover placement="top">
                              <template #content>
                                <div v-html="text"/>
                              </template>
                              {{ text }}
                            </a-popover>
                          </template>
                        </template>
                      </a-table>
                    </template>
                  </a-table>
                </div>
                <a-modal :okButtonProps="{ loading }" width="540px" v-model:visible="showAgentModal" :title="agentModalTitle"
                  @ok="saveAgent" ok-text="确认" cancel-text="取消"
                  >
                    <a-form layout="vertical" ref="formAgent" :model="formAgentData" :labelCol="{ span: 24 }" :wrapperCol="{ span: 24, offset: 0 }">
                      <a-form-item required name="name" label="名称"
                        :rules="[{ required: true, message: '请输入名称' }]"
                      >
                        <a-input v-model:value="formAgentData.name" placeholder="请输入名称" show-count :maxlength="50" />
                      </a-form-item>
                      <a-form-item required name="system_prompt" label="角色职责"
                        :rules="[{ required: true, message: '请输入角色职责' }]"
                      >
                        <a-textarea v-model:value="formAgentData.system_prompt" placeholder="塑造Agent助手的角色性格来明确具体职责，请尽量输入重要且精准的要求。" :rows="4" :maxlength=500 show-count/>
                      </a-form-item>
                      <a-form-item required name="provider_model_id" label="选择模型"
                        :rules="[{ required: true, message: '请选择模型' }]"
                      >
                        <a-select
                          placeholder="选择要使用的模型"
                          v-model:value="formAgentData.provider_model_id"
                          :options="modelList.map(item => {
                              return {
                                label: `${item.name}`,
                                value: `${item.id}`,
                              }
                            })"
                        ></a-select>
                      </a-form-item>
                      <a-form-item name="knowledge_base_ids" label="选择知识库">
                        <a-select
                          mode="multiple"
                          placeholder="选择问答的知识库, 可选多个"
                          v-model:value="formAgentData.knowledge_base_ids"
                          :max-tag-count="5"
                          :options="knowledegBaseList.map(item => {
                              return {
                                label: `${item.share_scope_view === 1?item.namespace + ' (共享)':item.namespace}`,
                                value: `${item.id}`,
                              }
                            })"
                          :allowClear=false
                        ></a-select>
                      </a-form-item>
                      <a-form-item name="noanswer" label="找不到答案">
                        <a-textarea v-model:value="formAgentData.no_answer_reply"
                        placeholder="当前Agent助手找不到答案时会回复此处的消息"
                        :rows="4" :maxlength=2000 show-count/>
                      </a-form-item>
                    </a-form>
                  </a-modal>
                  <a-modal width="1400px" style="top: 0px;"
                    :okButtonProps="{ loading }"
                    v-model:visible="showAgentFunctionModal"
                    :title="agentFunctionModalTitle"
                    @cancel="cancelAgentFunctionForm"
                    @ok="saveAgentFunction" ok-text="保存" cancel-text="取消"
                  >
                    <a-form layout="vertical" ref="formAgentFunction" :model="formAgentFunctionData" :labelCol="{ span: 24 }" :wrapperCol="{ span: 24, offset: 0 }">
                      <splitpanes class="default-theme">
                        <pane key="paneExtract" min-size="30" style="margin-right: 2px;background: white;">
                            <a-form-item required name="name" label="名称"
                              :rules="[{ required: true, message: '请输入名称' }]"
                            >
                              <a-input v-model:value="formAgentFunctionData.name" placeholder="请输入名称" show-count :maxlength="50" />
                            </a-form-item>
                            <a-form-item required name="description" label="功能描述"
                              :rules="[{ required: true, message: '请输入功能描述' }]"
                            >
                              <a-textarea v-model:value="formAgentFunctionData.description" placeholder="请输入功能描述" :rows="4" :maxlength=300 show-count/>
                            </a-form-item>
                        </pane>
                        <pane key="paneHttp" min-size="30">
                          <a-card :bordered="false" style="margin-left: 2px;">
                            <template #title>
                              HTTP接口
                              <a-popover placement="bottom">
                                <template #content>
                                  可以发出一个 HTTP POST请求，实现更为复杂的操作（联网搜索、数据库查询等）
                                </template>
                                <question-circle-outlined />
                              </a-popover>
                            </template>
                            <a-space direction="vertical">
                              <a-form-item required :name="['http_config', 'url']" label="请求地址"
                                :rules="[{ required: true, message: '请输入请求的目标地址' }]"
                              >
                                <a-input v-model:value="formAgentFunctionData.http_config.url" placeholder="请输入请求的目标地址" show-count :maxlength="500" />
                              </a-form-item>
                              <div class="flex justify-between">
                                <span style="margin-left: 10px;">Header 参数
                                  <a-popover placement="bottom">
                                    <template #content>
                                      HTTP Header 参数信息，参数以key-value形式传入。
                                    </template>
                                    <question-circle-outlined />
                                  </a-popover>
                                </span>
                                <a-button type="default" @click="addHttpHeaderParamColumn" size="small">
                                  <template #icon>
                                    <PlusOutlined />
                                  </template>
                                  添加参数
                                </a-button>
                              </div>
                              <div style="margin-top: 15px;margin-bottom: 15px;">
                                <a-table :columns="httpHeaderConfColumns" :data-source="formAgentFunctionData.http_config.headers"
                                  :pagination="false"
                                  size="small"
                                  style="margin-left: 0px;margin-right: 0px;"
                                >
                                  <template #bodyCell="{ text, record, index, column }">
                                    <template v-if="['name', 'value'].includes(column.dataIndex)">
                                      <a-input
                                        :placeholder="`请输入${column.title}`"
                                        v-model:value="optFunctionHttpHeaderEditableData[record.key][column.dataIndex]"
                                        style="margin: -5px 0"
                                        @change="saveOptFunctionHeader(record.key)"
                                      />
                                    </template>
                                    <template v-else-if="column.key === 'operation'">
                                      <div class="editable-row-operations">
                                        <a-popconfirm title="确认删除吗?" @confirm="deleteOptFunctionHeader(record.key)" ok-text="确认" cancel-text="取消">
                                          <a-button
                                            type="link"
                                            style="color:rgba(255, 77, 0, 0.622);padding: 0 5px 0 0;">
                                            <DeleteOutlined class="text-lg"/>
                                          </a-button>
                                        </a-popconfirm>
                                      </div>
                                    </template>
                                  </template>
                                </a-table>
                              </div>
                              <div class="flex justify-between">
                                <span><span class="label-required">*</span>Body 参数
                                  <a-popover placement="bottom">
                                    <template #content>
                                      POST请求使用json格式，参数以key-value形式传入
                                    </template>
                                    <question-circle-outlined />
                                  </a-popover>
                                </span>
                                <a-button type="default" @click="addHttpBodyParamColumn" size="small">
                                  <template #icon>
                                    <PlusOutlined />
                                  </template>
                                  添加参数
                                </a-button>
                              </div>
                              <div style="margin-top: 15px;margin-bottom: 15px;">
                                <a-table :columns="httpConfColumns" :data-source="formAgentFunctionData.http_config.columns"
                                  :pagination="false"
                                  size="small"
                                  style="margin-left: 0px;margin-right: 0px;"
                                >
                                  <template #headerCell="{ column }">
                                    <template v-if="column.key === 'source'">
                                      获取来源
                                      <a-popover title="" trigger="hover">
                                        <template #content>
                                          获取来源：<br>
                                          "AI提取"是使用AI根据输入的prompt进行提取<br>
                                          "当前生成问题"是使用AI将用户当前对话记录生成为一个新的独立问题
                                        </template>
                                        <QuestionCircleOutlined />
                                      </a-popover>
                                    </template>
                                  </template>
                                  <template #bodyCell="{ text, record, index, column }">
                                    <template v-if="['name', 'type', 'description'].includes(column.dataIndex)">
                                      <a-input
                                        :placeholder="`请输入${column.title}`"
                                        v-model:value="optFunctionHttpEditableData[record.key][column.dataIndex]"
                                        style="margin: -5px 0"
                                        @change="saveOptFunctionColumn(record.key)"
                                      />
                                    </template>
                                    <template v-else-if="['new_question', 'required'].includes(column.dataIndex)">
                                      <div>
                                        <a-switch
                                          v-model:checked="optFunctionHttpEditableData[record.key][column.dataIndex]"
                                          checked-children="是"
                                          un-checked-children="否"
                                        />
                                      </div>
                                    </template>
                                    <template v-else-if="['source'].includes(column.dataIndex)">
                                      <div>
                                        <a-select
                                          v-model:value="optFunctionHttpEditableData[record.key][column.dataIndex]"
                                          placeholder="请选择"
                                          :options="paramsSourceOptions"
                                        ></a-select>
                                      </div>
                                    </template>
                                    <template v-else-if="column.key === 'operation'">
                                      <div class="editable-row-operations">
                                        <a-popconfirm title="确认删除吗?" @confirm="deleteOptFunctionColumn(record.key)" ok-text="确认" cancel-text="取消">
                                          <a-button
                                            type="link"
                                            style="color:rgba(255, 77, 0, 0.622);padding: 0 5px 0 0;">
                                            <DeleteOutlined class="text-lg"/>
                                          </a-button>
                                        </a-popconfirm>
                                      </div>
                                    </template>
                                  </template>
                                </a-table>
                              </div>
                              <!-- <a-checkbox v-model:checked="formAgentFunctionData.http_config.use_ai_extract_params">
                                使用AI从用户当前对话中提取参数
                              </a-checkbox> -->
                              <a-textarea v-model:value="formAgentFunctionData.http_config.use_ai_extract_description" placeholder="写一段提取要求，告诉 AI 需要提取哪些参数内容, 可以提取多个参数" :rows="4" :maxlength=500 show-count
                                v-if="formAgentFunctionData.http_config.columns.filter(item => item.source === '1').length > 0"
                              />
                              <a-form-item required :name="['http_config', 'out_column_key']" label="用于响应的出参字段key"
                                :rules="[{ required: true, message: '请输入响应的出参字段key' }]"
                              >
                                <a-input v-model:value="formAgentFunctionData.http_config.out_column_key" placeholder="eg:data.city" show-count :maxlength="100" />
                              </a-form-item>
                              <a-checkbox v-model:checked="formAgentFunctionData.http_config.use_ai_extract_answer">使用AI从输出的内容中总结有用的答案</a-checkbox>
                            </a-space>
                          </a-card>
                        </pane>
                      </splitpanes>
                    </a-form>
                  </a-modal>
              </a-tab-pane>
              <a-tab-pane key="8" :disabled="!(formData.id > 0)" v-if="formData.mode === 2 || props.isWorkflowMode">
                <template #tab>
                  <span>
                    工作流编排
                    <lock-outlined v-if="!(formData.id > 0)"/>
                  </span>
                </template>
                <div v-if="chatWorkflowUrl"
                  :class="{
                    mask: maskShow
                  }"
                  :style="{
                    height: workflowIframeHeight + 'px',
                    background: '#fafafa',
                  }">
                  <iframe
                    id="chatWorkflow"
                    :src="chatWorkflowUrl"
                    frameborder="0"
                    allow="clipboard-read; clipboard-write; microphone *; autoplay;"
                    style="width: 100%; height: 100%;"
                  ></iframe>
                </div>
              </a-tab-pane>
              <a-tab-pane key="3" :disabled="!(formData.id > 0)" v-if="!props.isWorkflowMode">
                <template #tab>
                  <span>
                    界面设置
                    <lock-outlined v-if="!(formData.id > 0)"/>
                  </span>
                </template>
                <div style="padding-left: 20px; padding-right: 20px;"
                >
                  <a-table bordered :columns="chatMenuColumns" :dataSource="releaseMenus" :pagination="false">
                    <template #title>
                      <div class="flex justify-between">
                        <h3>菜单栏</h3>
                      </div>
                    </template>
                    <template #bodyCell="{ text, record, index, column }">
                      <template v-if="column.dataIndex === 'operation'">
                        <!-- <a-button class="text-xs" type="link" size="small" @click="run(record)">体验</a-button> -->
                        <!-- <a-button class="text-xs ml-base" type="primary" size="small" @click="edit(record)">编辑</a-button> -->
                        <a @click="editMenu(record)">编辑</a>
                        <a-divider type="vertical" />
                        <a-popconfirm placement="topLeft" title="确认删除？" @confirm="removeMenu(record)" ok-text="确认" cancel-text="取消">
                          <!-- <a-button class="text-xs ml-base" v-auth:delete danger size="small">删除</a-button> -->
                          <a style="color: red;">删除</a>
                        </a-popconfirm>
                      </template>
                    <template v-else-if="column.dataIndex === 'id'">
                      {{ index + 1 }}
                    </template>
                    </template>
                  </a-table>
                  <div style="margin-top: 15px;text-align: left;" v-if="releaseMenus.length < 3">
                    <a-button type="link" @click="addChatMenu">
                      <template #icon>
                        <PlusOutlined />
                      </template>
                      添加菜单栏
                    </a-button>
                  </div>
                  <a-modal :okButtonProps="{ loading }" width="540px" v-model:visible="showChatMenuModal" :title="chatMenuModalTitle" @ok="saveMenu"
                    ok-text="确认" cancel-text="取消"
                  >
                    <a-form layout="vertical" ref="formMenu" :model="formMenuData" :labelCol="{ span: 24 }" :wrapperCol="{ span: 24, offset: 0 }">
                      <a-form-item required name="name" label="名称"
                        :rules="[{ required: true, message: '请输入名称' }]"
                      >
                        <a-input v-model:value="formMenuData.name" placeholder="请输入名称" show-count :maxlength="15" />
                      </a-form-item>
                      <a-form-item required name="content" label="回复内容"
                        :rules="[{ required: true, message: '请输入回复内容' }]"
                      >
                        <a-textarea v-model:value="formMenuData.content" placeholder="请输入回复内容" :rows="4" :maxlength=3000 show-count/>
                      </a-form-item>
                      <!-- <a-form-item label="图片" help="(最多上传9张图片)">
                        <a-upload action="/upload.do" list-type="picture-card">
                          <div>
                            <PlusOutlined />
                          </div>
                        </a-upload>
                      </a-form-item> -->
                    </a-form>
                  </a-modal>

                  <style-settings 
                    :model-value="formData"
                    @update:model-value="updateStyleSettings"
                    :save-settings="doReleaseChatPageSetting"
                  />
                
                </div>
              </a-tab-pane>
              <a-tab-pane key="5" :disabled="!(formData.id > 0)" v-if="!props.isWorkflowMode">
                <template #tab>
                  <span>
                    标准问答
                    <lock-outlined v-if="!(formData.id > 0)"/>
                  </span>
                </template>
                <EnterQaModal
                  :initQaEditRecord="qaEditRecord"
                  :qaModalVisible="qaModalVisible"
                  @setQaModalVisible="setQaModalVisible"
                />
                <div style="padding-left: 20px; padding-right: 20px;">
                  <div style="margin-bottom: 15px;" class="flex justify-between">
                    <a-typography-text type="secondary">如果用户的提问是问答列表中的标准问题，会使用对应的标准回答进行快速响应，标准问题的问法尽可能独立，能增大命中率。</a-typography-text>
                    <a-button type="primary" @click="openQaModalVisible">
                      <template #icon>
                        <PlusOutlined />
                      </template>
                      标准问答
                    </a-button>
                  </div>
                  <a-table
                    bordered
                    size="small"
                    :columns="qaColumns"
                    :dataSource="qaTableData.results"
                    :pagination="{ pageSize: qaTableData.page_size, current: qaTableData.page_number, total: qaTableData.count }"
                    :scroll="{ x: 'calc(600px + 50%)', y: 660 }"
                    @change="handleQaTableChange"
                  >
                    <template #bodyCell="{ text, record, index, column }">
                      <template v-if="column.dataIndex === 'operation'">
                        <a-button
                          type="link"
                          size="small"
                          @click="editQaRecord(record, 'copy_add')"
                        >复制添加</a-button>
                        <a-divider type="vertical" />
                        <a-button
                          type="link"
                          size="small"
                          @click="editQaRecord(record, 'edit')"
                        >修改</a-button>
                        <a-divider type="vertical" />
                        <a-popconfirm placement="topLeft" title="确认删除吗？" @confirm="removeQaRecord(record)">
                          <a-button type="link" danger size="small">删除</a-button>
                        </a-popconfirm>
                      </template>
                      <template v-if="column.dataIndex === 'id'">
                        {{qaTableData.count - ((qaTableData.page_number - 1) * qaTableData.page_size ) - index }}
                      </template>
                      <template v-else-if="column.dataIndex === 'update_time'">
                        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
                      </template>
                      <template v-else-if="column.dataIndex === 'sample_answer'">
                        <PictureTwoTone v-if="record.image_urls.length > 0" style="margin-right: 2px;"/> {{ text }}
                      </template>
                    </template>
                  </a-table>
                  </div>
              </a-tab-pane>
              <a-tab-pane key="2" :disabled="!(formData.id > 0)" v-if="!props.isWorkflowMode">
                <template #tab>
                  <span>
                    高级设置
                    <lock-outlined v-if="!(formData.id > 0)"/>
                  </span>
                </template>
                <a-form
                  ref="form"
                  :model="formData"
                  :labelCol="{ span: 5 }"
                  :wrapperCol="{ span: 16, offset: 1 }"
                >
                  <a-form-item name="suggest_question_switch" label="生成建议提问">
                    <a-switch v-model:checked="formData.suggest_question_switch" />
                    <br/>
                    <span class="text-sm text-subtext">针对用户的提问及回答预测3个建议提问供用户快捷使用</span>
                  </a-form-item>
                  <a-form-item name="tts_voice_name" label="TTS发音人">
                    <a-select
                      style="width: 200px;"
                      placeholder="请选择发音人"
                      v-model:value="formData.tts_voice_name"
                      :options="ttsVoiceNameList"
                    ></a-select>
                  </a-form-item>
                  <!-- <a-form-item name="reply_tts_switch" label="自动朗读回答">
                    <a-switch v-model:checked="formData.reply_tts_switch" />
                  </a-form-item> -->
                  <a-form-item name="default" label="默认回复">
                    <a-textarea v-model:value="formData.default_reply"
                    placeholder="碰到超纲提问，没有Agent助手能够回答时会回复此处消息"
                    :rows="4" :maxlength=2000 show-count/>
                  </a-form-item>
                  <a-form-item name="answer_image_display_ratio" label="图片显示比例">
                    <a-input-number min="20" max="100" v-model:value="formData.answer_image_display_ratio" placeholder="显示比例" addon-after="%"></a-input-number>
                    <br/>
                    <span class="text-sm text-subtext">设置对话回答中图片的显示比例，未设置默认按30%显示</span>
                  </a-form-item>
                  <a-form-item name="chatbot_quiescent_page_refresh_interval" label="对话页面刷新间隔">
                    <a-input-number min="0" max="120" v-model:value="formData.chatbot_quiescent_page_refresh_interval" placeholder="设置刷新间隔时间" addon-after="分钟"></a-input-number>
                    <br/>
                    <span class="text-sm text-subtext">对话页面处于无人对话静止状态时，会按照设置的刷新间隔时间后恢复到页面初始状态。</span>
                  </a-form-item>
                  <!-- <a-form-item name="noanswer" label="找不到答案">
                    <a-textarea v-model:value="formData.no_answer_reply" placeholder="找不到答案" :rows="4" :maxlength=2000 show-count/>
                  </a-form-item> -->
                  <a-form-item :wrapper-col="{ span: 21, offset: 12 }">
                    <!-- <a-button @click="setModalVisible(false, true)">关闭</a-button> -->
                    <a-button style="margin-left: 10px" type="primary" @click="saveConf">保存设定</a-button>
                  </a-form-item>
                </a-form>
              </a-tab-pane>
              <a-tab-pane key="6" :disabled="!(formData.id > 0)" v-if="!props.isWorkflowMode">
                <template #tab>
                  <span>
                    回归测试
                    <lock-outlined v-if="!(formData.id > 0)"/>
                  </span>
                </template>
                <FollowQuestionManage
                  :key="Math.random() * 100000000000000000"
                  :source="1"
                  :biz_id="formData.id"
                />
              </a-tab-pane>
            </a-tabs>
        </pane>
        <pane :key="2" min-size="23" :size="26" v-if="chatBotVisible">
          <div v-if="chatIframeUrl"
            :class="{
              mask: maskShow
            }"
              :style="{
                height: chatIframeHeight + 'px',
                background: '#fafafa',
                paddingLeft: '2px',
              }">
          <iframe
            id="chatReleaseDebug"
            :src="chatIframeUrl"
            frameborder="0"
            allow="clipboard-read; clipboard-write; microphone *; autoplay;"
            style="width: 100%; height: 100%;"
          ></iframe>
          </div>
          <div v-else><p style="position: absolute; top: 48%;transform: translateX(20%);">保存设定后可调式当前应用项目的机器人</p></div>
        </pane>
      </splitpanes>
  </a-modal>
</template>
<script lang="ts" setup>
import { reactive, watch, computed, ref, onMounted, onUnmounted, nextTick, UnwrapRef } from 'vue';
import { storeToRefs } from 'pinia';
import { useApikeyStore } from '@/store/apikey';
import { useKnowledegBaseStore } from '@/store/knowledge';
import type { SelectProps } from 'ant-design-vue';

// Define DataItem interface for HTTP config
interface DataItem {
  key: string;
  [key: string]: any;
}
import {
  deleteReleaseMenu,
  getReleaseMenu,
  saveReleaseMenu,
  updateReleaseConf,
  saveRelease,
  getReleaseAgent,
  saveReleaseAgent,
  deleteReleaseAgent,
  saveReleaseAgentFunction,
  deleteReleaseAgentFunction,
  releaseEnterQaPage,
  deleteEnterQa,
  releaseLockInfo,
  getReleaseChatPageSetting,
  setReleaseChatPageSetting,
} from "@/api/release";
import { message, FormInstance } from 'ant-design-vue';
import { useWindowSize } from '@vueuse/core'
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import EnterQaModal from './EnterQaModal.vue';
import FollowQuestionManage from './FollowQuestionManage.vue';
import { LockOutlined, UnlockOutlined } from '@ant-design/icons-vue';
import { PlusOutlined, QuestionCircleOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import StyleSettings from '@/components/setting/StyleSettings.vue';

//https://antoniandre.github.io/splitpanes/
const props = defineProps({
  showFormModalVisible: Boolean,
  formData: Object,
  fabuRadioValue: String,
  isWorkflowMode: {
    type: Boolean,
    default: false
  },
  is_team_workflow: {
    type: Boolean,
    default: false
  }
});

const maskShow = ref(false);

const loading = ref(false);
const chatBotVisible = ref(false);
const activeKey = ref("1"); // Default to "基本信息" tab
const { height } = useWindowSize();

// Update activeKey when formData or isWorkflowMode changes
watch([() => props.formData, () => props.isWorkflowMode], () => {
  if (props.formData?.id > 0) {
    if (props.isWorkflowMode) {
      activeKey.value = "8"; // Set to "工作流编排" tab for workflow mode
    } else if (props.formData?.mode === 1) {
      activeKey.value = "4"; // Set to "Agent助手" tab for mode 1
    } else if (props.formData?.mode === 2) {
      activeKey.value = "8"; // Set to "工作流编排" tab for mode 2
    } else {
      activeKey.value = "1"; // Default to "基本信息" tab
    }
    
    // 确保 apikey_id 是字符串类型
    if (props.formData.apikey_id !== null && props.formData.apikey_id !== undefined 
        && typeof props.formData.apikey_id !== 'string') {
      props.formData.apikey_id = String(props.formData.apikey_id);
    }
  } else {
    activeKey.value = "1"; // Default to "基本信息" tab for new items
  }
}, { immediate: true });

const apikeyStore = useApikeyStore();
const knowledegBaseStore = useKnowledegBaseStore()

const { getApikeyList, getModelList } = apikeyStore;
const { getKnowledegBaseList } = knowledegBaseStore;

const { apikeyList, modelList } = storeToRefs(apikeyStore);
const { knowledegBaseList } = storeToRefs(knowledegBaseStore);
const releaseMenus = ref([]);
const releaseAgents = ref([]);
const qaTableData = ref([]);
const qaModalVisible = ref(false);
const qaEditRecord = ref({});

const ttsVoiceNameList = [
  {
    label: "小萍", value: "aisxping"
  },
  {
    label: "小燕", value: "xiaoyan"
  },
  {
    label: "许久", value: "aisjiuxu"
  },
  {
    label: "小婧", value: "aisjinger"
  },
  {
    label: "许小宝", value: "aisbabyxu"
  },
]

const agentColumns = [
  { title: '名称', dataIndex: 'name', key: 'name' },
  { title: '角色职责', dataIndex: 'system_prompt', key:'system_prompt', ellipsis: true, },
  { title: '使用模型', dataIndex: 'model_name', key: 'model_name' },
  { title: '使用知识库', dataIndex: 'knowledge_base_namespace', key: 'knowledge_base_namespace' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '更新时间', dataIndex: 'update_time', key: 'update_time' },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 210,
    fixed: 'right',
  },
];

const agentFuncColumns = [
  { title: '函数名称', dataIndex: 'name', key: 'name', ellipsis: true, },
  { title: '功能描述', dataIndex: 'description', key: 'description', ellipsis: true, },
  { title: '更新时间', dataIndex: 'update_time', key: 'update_time' },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
  },
];

const httpHeaderConfColumns = [
  { title: '参数名', dataIndex: 'name', key: 'name'},
  { title: '参数值', dataIndex: 'value', key: 'value', ellipsis: true,},
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 120,
  },
];


const httpConfColumns = [
  { title: '参数名', dataIndex: 'name', key: 'name'},
  { title: '参数描述', dataIndex: 'description', key: 'description', ellipsis: true,},
  { title: '是否必填', dataIndex: 'required', key: 'required' },
  // { title: '用户问题', dataIndex: 'new_question', key: 'new_question' },
  { title: '获取来源', dataIndex: 'source', key: 'source',  width: 140, },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 120,
  },
];

const form = ref<FormInstance>();

const formMenu = ref<FormInstance>();
const formAgent = ref<FormInstance>();
const formAgentFunction = ref<FormInstance>();

const releaseIdentifier = ref(null);
const lockStatus = ref(false);
const editorName = ref('');
const lockPollingInterval = ref<number | null>(null);

const iframeUrl = import.meta.env.VITE_CHATBOT;

const chatIframeUrl = computed({
  get() {
    console.log(props.formData);
    console.log(Object.keys(props.formData).length > 0 && props.formData.identifier && props.formData.identifier.length > 0);
    if (Object.keys(props.formData).length > 0 && props.formData.identifier && props.formData.identifier.length > 0){
      return iframeUrl + "/" + props.formData.identifier + "?rdebug=1";
    }else if (releaseIdentifier.value && releaseIdentifier.value.length > 0){
      return iframeUrl + "/" + releaseIdentifier.value + "?rdebug=1";
    }else{
      return null;
    }
  },
  set(value) {
    releaseIdentifier.value = value
  }
})

const modelTitle = computed(() => {
  let name = `${props.formData.name} - 应用项目编排`
  if (props.fabuRadioValue === '2'){
    name = name + ` - [创建账号：${props.formData.account_name}]`;
  }
  return name
});

const chatWorkflowUrl = computed(() => {
  if (Object.keys(props.formData).length > 0 && props.formData.mode === 2){
    const base = iframeUrl.replaceAll("/q/chat", "")
    return base + `/f/app/${props.formData.d_app_id}/workflow`;
  }
  else if (Object.keys(props.formData).length > 0 && props.formData.mode === 3){
    const base = iframeUrl.replaceAll("/q/chat", "")
    return base + `/f/app/${props.formData.d_app_id}/workflow`;
  }
  else{
    return null;
  }
});

const workflowIframeHeight = computed(() => {
  // Leave some space for the tab header and padding
  return height.value - 75;
});

const chatIframeHeight = computed(() => {
  // Leave some space for the tab header and padding
  return height.value - 20;
});

const paramsSourceOptions = ref<SelectProps['options']>([
  { value: '1', label: 'AI提取' },
  { value: '2', label: '当前生成问题' },
]);

function switchChatBotVisible(){
  chatBotVisible.value = !chatBotVisible.value;
}

const emit = defineEmits(['setShowFormModalVisible', 'setEditRecord', 'refreshReleaseList'])

const setModalVisible = (visible: boolean, refresh: boolean) => {
  console.log(visible);
  emit('setShowFormModalVisible', visible, refresh);
  form.value?.resetFields();
  activeKey.value = '1';
};

function saveConf(){
  if(!(props.formData.id > 0)){
    message.success(`非法数据`);
    return;
  }
  loading.value = true;
  form.value
    ?.validate()
    .then(() => {
      const opt = props.formData.id;
      console.log("Saving configuration with style:", props.formData.style_preset, props.formData.style);
      console.log("Saving background with:", props.formData.background_preset, props.formData.background_css, props.formData.background_type);
      updateReleaseConf(props.formData).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          // emit('refreshReleaseList');
          // emit('setEditRecord', data);
          message.success(`保存成功`);
          botMenuEventNotice(
            `updateRelease`, data
          );
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

function submit() {
  loading.value = true;
  form.value
    ?.validate()
    .then(() => {
      // 确保 apikey_id 是字符串类型
      if (props.formData.apikey_id !== null && props.formData.apikey_id !== undefined) {
        props.formData.apikey_id = String(props.formData.apikey_id);
      }
      
      // const opt = props.formData.id?"更改":"创建";
      saveRelease(props.formData).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          // emit('refreshReleaseList');
          message.success(`保存成功`);
          // setModalVisible(false, true);
          // releaseIdentifier.value = data.identifier;
          // chatIframeUrl.value = data.identifier;
          if(!props.formData.id){
            setModalVisible(true, true);
            emit('setEditRecord', data);
          }
          botMenuEventNotice(
            `updateRelease`, data
          );
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

const formMenuData = ref({
  id: 0,
  name: "",
  content: "",
  release_id: 0,
});

const formAgentData = ref({
  id: 0,
  name: "",
  system_prompt: "",
  provider_model_id: null,
  knowledge_base_ids: [],
  no_answer_reply: "",
  release_id: 0,
});

const formAgentFunctionData = ref({
  id: 0,
  agent_id: 0,
  name: "",
  description: "",
  http_config: {
    url: "",
    out_column_key: "",
    use_ai_extract_description: "",
    headers: [],
    columns: [],
    use_ai_extract_answer: false,
  },
});

const chatMenuColumns = [
  {
    title: '序号',
    dataIndex: 'id',
  },
  {
    title: '名称',
    dataIndex: 'name',
  },
  {
    title: '回复内容',
    dataIndex: 'content',
  },
  {
    width:120,
    title: '操作',
    align: 'center',
    dataIndex: 'operation',
    fixed: "right",
  },
];

function resetMenuForm(){
  showChatMenuModal.value = false;
  formMenuData.value.id = 0;
  formMenuData.value.name = "";
  formMenuData.value.content = "";
  formMenu.value?.resetFields();
}

function resetAgentForm(){
  showAgentModal.value = false;
  formAgentData.value.id = 0;
  formAgentData.value.name = "";
  formAgentData.value.system_prompt = "";
  formAgentData.value.provider_model_id = null;
  formAgentData.value.knowledge_base_ids = [];
  formAgentData.value.no_answer_reply = "";
  formAgent.value?.resetFields();
}

function cancelAgentFunctionForm(){
  showAgentFunctionModal.value = false;
  resetAgentFunctionForm();
}

function resetAgentFunctionForm(){
  formAgentFunctionData.value = {
    id: 0,
    agent_id: 0,
    name: "",
    description: "",
    http_config: {
      url: "",
      out_column_key: "",
      use_ai_extract_description: "",
      headers: [],
      columns: [],
      use_ai_extract_answer: false,
    },
  }
  formAgentFunction.value?.resetFields();
}

function editMenu(recored){
  formMenuData.value.id = recored.id;
  formMenuData.value.name = recored.name;
  formMenuData.value.content = recored.content;
  showChatMenuModal.value = true;
  chatMenuModalTitle.value = "编辑菜单";
}

function editAgent(recored){
  console.log(recored);
  formAgentData.value.id = recored.id;
  formAgentData.value.name = recored.name;
  formAgentData.value.system_prompt = recored.system_prompt;
  formAgentData.value.provider_model_id = String(recored.provider_model_id);
  formAgentData.value.knowledge_base_ids = recored.knowledge_base_ids;
  if (recored.knowledge_base_ids && recored.knowledge_base_ids.length > 0){
    formAgentData.value.knowledge_base_ids = recored.knowledge_base_ids.map(num => String(num));
  }else{
    formAgentData.value.knowledge_base_ids = [];
  }
  formAgentData.value.no_answer_reply = recored.no_answer_reply;
  showAgentModal.value = true;
  agentModalTitle.value = "编辑Agent助手";
}

function editAgentFunction(recored){
  formAgentFunctionData.value.id = recored.id;
  formAgentFunctionData.value.name = recored.name;
  formAgentFunctionData.value.description = recored.description;
  formAgentFunctionData.value.http_config = recored.http_config;
  showAgentFunctionModal.value = true;
  agentFunctionModalTitle.value = "编辑函数";

  recored.http_config.headers.forEach((item) => {
    optFunctionHttpHeaderEditableData[item.key] = item;
  });

  recored.http_config.columns.forEach((item) => {
    optFunctionHttpEditableData[item.key] = item;
  });

}

function deleteAgentFunction(record){
  deleteReleaseAgentFunction({id: record.id}).then((res) => {
    const { code } = res;
    if (code === 0){
      message.success(`删除成功`);
      refreshAgentData();
    }
  });
}

function removeMenu(record){
  deleteReleaseMenu({id: record.id}).then((res) => {
    const { code } = res;
    if (code === 0){
      message.success(`删除成功`);
      refreshMenuData();
      botMenuEventNotice(
        `deleteMenu`,
        {name: formMenuData.value.name, content: formMenuData.value.content}
      );
    }
  });
}

function saveMenu() {
  loading.value = true;
  formMenu.value
    ?.validate()
    .then(() => {
      formMenuData.value.release_id = props.formData.id;
      const opt = formMenuData.value.id > 0?"更改":"创建";
      saveReleaseMenu(formMenuData.value).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          // message.success(`${opt}成功`);
          const opt = formMenuData.value.id > 0?"update":"add";
          botMenuEventNotice(`${opt}Menu`, data);
          refreshMenuData();
          resetMenuForm();
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

function disableAgent(record, disable, action){
  deleteReleaseAgent({id: record.id, disable: disable, action: action}).then((res) => {
    const { code } = res;
    if (code === 0){
      let opt = '删除';
      if (action === 'disable'){
        opt = disable?"禁用":"启用";
      }
      message.success(`${opt}成功`);
      refreshAgentData();
    }
  });
}

function saveAgent() {
  loading.value = true;
  formAgent.value
    ?.validate()
    .then(() => {
      formAgentData.value.release_id = props.formData.id;
      const opt = formAgentData.value.id > 0?"更改":"创建";
      saveReleaseAgent(formAgentData.value).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          // message.success(`${opt}成功`);
          const opt = formAgentData.value.id > 0?"update":"add";
          refreshAgentData();
          resetAgentForm();
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

function saveAgentFunction() {
  loading.value = true;
  formAgentFunction.value
    ?.validate()
    .then(() => {
      if (formAgentFunctionData.value.http_config.columns.length === 0){
        message.error("最少需要一个请求入参");
        return
      }
      formAgentFunctionData.value.agent_id = currentAgentId.value;
      const opt = formAgentFunctionData.value.id > 0?"更改":"创建";
      saveReleaseAgentFunction(formAgentFunctionData.value).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          // message.success(`${opt}成功`);
          const opt = formAgentFunctionData.value.id > 0?"update":"add";
          refreshAgentData();
          cancelAgentFunctionForm();
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

const showChatMenuModal = ref(false);
const chatMenuModalTitle = ref("添加菜单");
const showAgentModal = ref(false);
const agentModalTitle = ref("添加Agent助手");
const showAgentFunctionModal = ref(false);
const agentFunctionModalTitle = ref("添加函数");
const currentAgentId = ref(0);

function addChatMenu() {
  showChatMenuModal.value = true;
}

function addAgent() {
  resetAgentForm();
  showAgentModal.value = true;
}

function initAgentFunctionModal(){
  resetAgentFunctionForm();
  addHttpHeaderParamColumn();
  addHttpBodyParamColumn();
}

function addAgentFunction(agent_id) {
  showAgentFunctionModal.value = true;
  agentFunctionModalTitle.value = "添加函数";
  currentAgentId.value = agent_id;

  initAgentFunctionModal();
}

function botMenuEventNotice(eventType, data) {
  const eventData = {
    eventType: eventType,
    eventData: data
  }
  sendEventData(eventData);
}

function sendEventData(eventData) {
  var chatReleaseDebug = document.getElementById("chatReleaseDebug") as HTMLIFrameElement;
  if (chatReleaseDebug && chatReleaseDebug.contentWindow) {
    try {
      console.log(eventData);
      chatReleaseDebug.contentWindow.postMessage(eventData, "*");
    } catch (error) {
      console.error("发送消息失败:", error);
    }
  }
}

function refreshMenuData(){
  if (props.formData.id > 0){
    getReleaseMenu({release_id: props.formData.id}).then((response) => {
      console.log(response);
      const {code, data} = response;
      if (code === 0){
        releaseMenus.value = data;
      }
    }).catch((e) => {
      console.error(e);
    });
  }
}

function refreshAgentData(){
  if (props.formData.id > 0){
    getReleaseAgent({release_id: props.formData.id}).then((response) => {
      console.log(response);
      const {code, data} = response;
      if (code === 0){
        releaseAgents.value = data;
      }
    }).catch((e) => {
      console.error(e);
    });
  }
}

const handleQaTableChange = async (page, filters, sorter) => {
  const params = {
    release_id: props.formData.id,
    page_size: page.pageSize,
    page_number: page.current,
  }
  if (props.formData.id > 0){
    refreshQaPage(params);
  }
};

function refreshQaPage(params){
  if (props.formData.id > 0){
    releaseEnterQaPage(params).then((res) => {
      const { data } = res;
      console.log(data);
      qaTableData.value = data;
      return data;
    }).catch((e) => {
      console.error(e);
    });
  }
}

const optFunctionHttpEditableData: UnwrapRef<Record<string, DataItem>> = reactive({});
const editOptFunctionColumn = (key: string) => {
  optFunctionHttpEditableData[key] = cloneDeep(formAgentFunctionData.value.http_config.columns.filter(item => key === item.key)[0]);
};
const saveOptFunctionColumn = (key: string) => {
  Object.assign(formAgentFunctionData.value.http_config.columns.filter(item => key === item.key)[0], optFunctionHttpEditableData[key]);
  // delete optFunctionHttpEditableData[key];
};
const cancelOptFunctionColumn = (key: string) => {
  delete optFunctionHttpEditableData[key];
};
const deleteOptFunctionColumn = (key: string) => {
  formAgentFunctionData.value['http_config'].columns = formAgentFunctionData.value['http_config'].columns.filter(item => item.key!== key);
};

const optFunctionHttpHeaderEditableData: UnwrapRef<Record<string, DataItem>> = reactive({});
const editOptFunctionHeader = (key: string) => {
  optFunctionHttpHeaderEditableData[key] = cloneDeep(formAgentFunctionData.value.http_config.headers.filter(item => key === item.key)[0]);
};
const saveOptFunctionHeader = (key: string) => {
  Object.assign(formAgentFunctionData.value.http_config.headers.filter(item => key === item.key)[0], optFunctionHttpHeaderEditableData[key]);
  // delete optFunctionHttpHeaderEditableData[key];
};
const cancelOptFunctionHeader = (key: string) => {
  delete optFunctionHttpHeaderEditableData[key];
};
const deleteOptFunctionHeader = (key: string) => {
  formAgentFunctionData.value['http_config'].headers = formAgentFunctionData.value['http_config'].headers.filter(item => item.key!== key);
};

function generateRandomString(length) {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

const addHttpHeaderParamColumn = () => {
  const defaultColumn = {key: "", name: "", value: ""};
  defaultColumn["key"] = generateRandomString(5);
  formAgentFunctionData.value["http_config"]["headers"].push(defaultColumn);

  editOptFunctionHeader(defaultColumn["key"]);
}

const addHttpBodyParamColumn = () => {
  const defaultColumn = {key: "", name: "", required: false, source: "1"};
  defaultColumn["key"] = generateRandomString(5);
  formAgentFunctionData.value["http_config"]["columns"].push(defaultColumn);

  editOptFunctionColumn(defaultColumn["key"]);
}

const qaColumns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '标准问题',
      dataIndex: 'question',
      key: 'question',
      ellipsis: true,
    },
    {
      title: '标准回答',
      dataIndex: 'sample_answer',
      key: 'sample_answer',
      ellipsis: true,
    },
    {
      title: '最后修改时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 230,
    },
];

function setQaModalVisible(visible: boolean) {
  qaModalVisible.value = visible;
  if (visible === false){
    const params = {release_id: props.formData.id};
    refreshQaPage(params);
  }
}

function openQaModalVisible() {
  let tmp = {}
  tmp["from"] = 1
  tmp["release_id"] = `${props.formData.id}`
  tmp["source_release_id"] = props.formData.id
  tmp["question"] = ''
  tmp["answer"] = ''
  tmp["image_urls"] = []
  qaEditRecord.value = tmp
  setQaModalVisible(true);
}

function editQaRecord(record, action){
  console.log(record)
  let tmp = {};
  tmp["id"] = action === 'edit'?record.id:0
  tmp["question"] = record.question
  tmp["answer"] = record.sample_answer
  tmp["image_urls"] = record.image_urls.map(img_url => {return {
    uid: img_url.split("/").pop().split(".")[0],
    name: img_url,
    status: 'done',
    url: img_url,
  }});
  tmp["release_id"] = `${record.release_id}`
  tmp["source_release_id"] = record.release_id
  tmp["from"] = 1
  qaEditRecord.value = tmp
  setQaModalVisible(true);
}

function removeQaRecord(record) {
  deleteEnterQa({id: record.id}).then((response) => {
    const { code } = response;
    if (code === 0){
      message.success("已删除");
      refreshQaPage({release_id: record.release_id});
    }
  })
}

watch(activeKey, (val) => {
  if(val === '3'){
    refreshMenuData();
  }else if(val === '4'){
    refreshAgentData();
  }else if(val === '5'){
    const params = {release_id: props.formData.id};
    refreshQaPage(params);
  }
})

function displayMessage(d){
  // console.log(d);
}

function fetchLockInfo() {
  if (!props.is_team_workflow || !props.formData.id) return;
  
  releaseLockInfo({ release_id: props.formData.id }).then((response) => {
    const { code, data } = response;
    if (code === 0) {
      lockStatus.value = data.lock_status === true;
      editorName.value = data?.editor?.name || '';
    }
  }).catch((e) => {
    console.error('Failed to fetch lock info:', e);
  });
}

function startLockInfoPolling() {
  if (!props.is_team_workflow) return;
  
  // Clear any existing polling
  stopLockInfoPolling();
  
  // Set up new polling (every 5 seconds)
  lockPollingInterval.value = window.setInterval(() => {
    fetchLockInfo();
  }, 5000);
  
  // Initial fetch
  fetchLockInfo();
}

function stopLockInfoPolling() {
  if (lockPollingInterval.value) {
    clearInterval(lockPollingInterval.value);
    lockPollingInterval.value = null;
  }
}

onMounted(() => {
  if(props.fabuRadioValue === '1'){
    getApikeyList();
  }else{
    // 如果传入的参数有creator，则根据creator过滤获取apikey列表
    if (props.formData && props.formData.creator) {
      getApikeyList({creator: props.formData.creator});
    } else {
      getApikeyList();
    }
  }
  getKnowledegBaseList();
  getModelList();
  refreshMenuData();
  refreshAgentData();
  
  //getReleaseChatPageSetting
  getReleaseChatPageSetting({
    release_id: props.formData.id,
  }).then(res => {
    console.log('getReleaseChatPageSetting res: ', res);
    if (res.data.setting) {
      props.formData.font_settings = res.data.setting.font_settings;
      props.formData.background_preset = res.data.setting.background_preset;
      props.formData.background_type = res.data.setting.background_type;
      props.formData.background_image_url = res.data.setting.background_image_url;
      props.formData.background_css = res.data.setting.background_css;
      props.formData.style_preset = res.data.setting.style_preset;
      props.formData.style = res.data.setting.style;
      console.log('getReleaseChatPageSetting props.formData: ', props.formData);
    }
  });
  
  if (window.addEventListener) {
  	// For standards-compliant web browsers
    window.addEventListener("resize", displayMessage, false);
  }
  
  // Start lock info polling if this is a team workflow
  startLockInfoPolling();
  
});

onUnmounted(() => {
  // Stop lock info polling
  stopLockInfoPolling();
});

// Update style settings when they change
function updateStyleSettings(newStyleData) {
  // Update the props.formData with the new style values
  props.formData.font_settings = newStyleData.font_settings;
  props.formData.background_preset = newStyleData.background_preset;
  props.formData.background_type = newStyleData.background_type;
  props.formData.background_image_url = newStyleData.background_image_url;
  props.formData.background_css = newStyleData.background_css;
  props.formData.style_preset = newStyleData.style_preset;
  props.formData.style = newStyleData.style;
}

// 请求setReleaseChatPageSetting
function doReleaseChatPageSetting() {
  setReleaseChatPageSetting({
    release_id: props.formData.id,
    setting: {
      font_settings: props.formData.font_settings,
      background_preset: props.formData.background_preset,
      background_type: props.formData.background_type,
      background_image_url: props.formData.background_image_url,
      background_css: props.formData.background_css,
      style_preset: props.formData.style_preset,
      style: props.formData.style,  
    },
  }).then(res => {
    console.log('setReleaseChatPageSetting res: ', res);
  });
}
</script>

<style lang="less">
.tabs-extra-demo-button {
  margin-right: 16px;
}

.ant-row-rtl .tabs-extra-demo-button {
  margin-right: 0;
  margin-left: 16px;
}

.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }
  .ant-modal-body {
    flex: 1;
    padding: 5px;
  }
  .ant-modal-close {
    background-color: #333;
    color: #fff;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 10px 0 0;
    
    .ant-modal-close-x {
      width: 32px;
      height: 32px;
      line-height: 32px;
    }
  }
}
:deep(#components-grid-demo-playground) [class~='ant-col'] {
  background: transparent;
  border: 0;
}
:deep(#components-grid-demo-playground) [class~='ant-col'] > div {
  height: 120px;
  font-size: 14px;
  line-height: 120px;
  background: #0092ff;
  border-radius: 4px;
}
:deep(#components-grid-demo-playground) pre {
  padding: 8px 16px;
  font-size: 13px;
  background: #f9f9f9;
  border-radius: 6px;
}
:deep(#components-grid-demo-playground) pre.demo-code {
  direction: ltr;
}
:deep(#components-grid-demo-playground) .ant-col {
  padding: 0;
}
.mask{
  position: relative;
  width: 100%;
  &::before {
  content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
  }
}

.label-required {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: var(--font-size);
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.lock-status {
  display: inline-flex;
  align-items: center;
  margin-right: 10px;
  font-size: 14px;
  
  &.locked {
    color: #faa700;
  }
  
  &.unlocked {
    color: #52c41a;
  }
}

.style-card {
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  &.style-card-selected {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.bg-card {
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  &.bg-card-selected {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.font-size-slider {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  width: 400px;
}

.font-size-label {
  min-width: 50px;
  text-align: center;
  margin-right: 10px;
  font-weight: 500;
  user-select: none;
}

:deep(.ant-slider) {
  width: 100%;
  margin: 0;
}

:deep(.ant-slider-rail), :deep(.ant-slider-track) {
  height: 8px;
}

:deep(.ant-slider-handle) {
  height: 16px;
  width: 16px;
  margin-top: -4px;
}

.font-size-preview {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.button-preview {
  background-color: #3b82f6;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.color-picker-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.color-preview {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  margin-right: 10px;
}

.color-value {
  margin-right: 10px;
  font-family: monospace;
}

.font-color-preview {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.button-text-preview {
  background-color: #3b82f6;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.color-picker-modal {
  padding: 10px;
}

.color-gradient {
  width: 100%;
  height: 200px;
  position: relative;
  background: linear-gradient(to right, #fff, hsl(var(--hue), 100%, 50%)),
              linear-gradient(to top, #000, rgba(0, 0, 0, 0));
  background-blend-mode: multiply;
  margin-bottom: 15px;
  cursor: crosshair;
  border-radius: 4px;
  --hue: 210deg;
}

.color-picker-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  position: absolute;
  transform: translate(-50%, -50%);
  pointer-events: none;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.hue-slider {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.hue-slider input {
  width: 100%;
  margin: 0;
  background: linear-gradient(
    to right,
    #f00 0%,
    #ff0 17%,
    #0f0 33%,
    #0ff 50%,
    #00f 67%,
    #f0f 83%,
    #f00 100%
  );
  height: 20px;
  -webkit-appearance: none;
  appearance: none;
  outline: none;
  border-radius: 4px;
}

.hue-slider input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 20px;
  border-radius: 2px;
  background: white;
  cursor: pointer;
  border: 1px solid #d9d9d9;
}

.color-sample {
  width: 100%;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-sample .color-value {
  color: white;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7);
  font-weight: bold;
  margin: 0;
}

.rgb-inputs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.rgb-input {
  display: flex;
  flex-direction: column;
  width: 30%;
}

.rgb-input label {
  margin-bottom: 5px;
  text-align: center;
}

.color-picker-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.style-card {
  min-width: 0;
  width: 100%;
  max-width: 220px;
  min-height: 120px;
  font-size: 13px;
  margin: 0 auto;
  box-sizing: border-box;
  .ant-card-body {
    padding: 10px !important;
  }
}
</style>
