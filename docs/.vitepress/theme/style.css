/**
 * Customize default theme styling by overriding CSS variables:
 * https://github.com/vuejs/vitepress/blob/main/src/client/theme-default/styles/vars.css
 */

/**
 * Colors
 * -------------------------------------------------------------------------- */
 @tailwind base;
 @tailwind components;
 @tailwind utilities;
 body {
  @apply bg-container !important;
 }
 :root {
  --vp-c-brand: var(--color-primary-6);
  --vp-c-brand-light: var(--color-primary-7);
  --vp-c-brand-lighter: var(--color-primary-8);
  --vp-c-brand-lightest: var(--color-primary-9);
  --vp-c-brand-dark: var(--color-primary-6);
  --vp-c-brand-darker: var(--color-primary-5);
  --vp-c-brand-dimm: var(--color-primary-1);
  --vp-c-bg: var(--color-bg-container);
  --vp-sidebar-bg-color: var(--color-bg-side);

  --vp-c-text-1: var(--color-text-1);
  --vp-c-text-2: var(--color-text-2);
  --vp-c-text-3: var(--color-text-3);

  --vp-c-mute: var(--color-bg-layout);
  --vp-c-mute-light:  var(--color-bg-container-light);
  --vp-c-mute-lighter: var(--color-bg-container);
  --vp-c-mute-dark: var(--color-bg-layout);
  --vp-c-mute-darker:  var(--color-bg-container-light);
  --vp-c-text-code: var(--color-text-2);
}

/**
 * Component: Button
 * -------------------------------------------------------------------------- */

:root {
  --vp-button-brand-border: var(--vp-c-brand-light);
  --vp-button-brand-text: var(--vp-c-white);
  --vp-button-brand-bg: var(--vp-c-brand);
  --vp-button-brand-hover-border: var(--vp-c-brand-light);
  --vp-button-brand-hover-text: var(--vp-c-white);
  --vp-button-brand-hover-bg: var(--vp-c-brand-light);
  --vp-button-brand-active-border: var(--vp-c-brand-light);
  --vp-button-brand-active-text: var(--vp-c-white);
  --vp-button-brand-active-bg: var(--vp-button-brand-bg);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(
    120deg,
    #bd34fe 30%,
    #41d1ff
  );

  --vp-home-hero-image-background-image: linear-gradient(
    -45deg,
    #bd34fe 50%,
    #47caff 50%
  );
  --vp-home-hero-image-filter: blur(40px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(72px);
  }
}

/**
 * Component: Custom Block
 * -------------------------------------------------------------------------- */

:root {
  --vp-custom-block-tip-border: var(--vp-c-brand);
  --vp-custom-block-tip-text: var(--vp-c-brand-darker);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
}

.dark {
  --vp-custom-block-tip-border: var(--vp-c-brand);
  --vp-custom-block-tip-text: var(--vp-c-brand-lightest);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
}

/**
 * Component: Algolia
 * -------------------------------------------------------------------------- */

.DocSearch {
  --docsearch-primary-color: var(--vp-c-brand) !important;
}

/**
 * VitePress: Custom fix
 * -------------------------------------------------------------------------- */

/*
  Use lighter colors for links in dark mode for a11y.
  Also specify some classes twice to have higher specificity
  over scoped class data attribute.
*/
.dark .vp-doc a,
.dark .vp-doc a > code,
.dark .VPNavBarMenuLink.VPNavBarMenuLink:hover,
.dark .VPNavBarMenuLink.VPNavBarMenuLink.active,
.dark .link.link:hover,
.dark .link.link.active,
.dark .edit-link-button.edit-link-button,
.dark .pager-link .title {
  color: var(--vp-c-brand-lighter);
}

.dark .vp-doc a:hover,
.dark .vp-doc a > code:hover {
  color: var(--vp-c-brand-lightest);
  opacity: 1;
}

/* Transition by color instead of opacity */
.dark .vp-doc .custom-block a {
  transition: color 0.25s;
}

