<template>
  <div class="workplace grid grid-rows-none gap-4 mt-xxs">
    <div class="bg-container p-base rounded-b-lg rounded-tr-lg pt-8 flex items-end justify-between">
      <div class="flex items-center">
        <!-- <img :src="`${account.avatar}`" class="w-16 h-16 rounded-full" /> -->
        <a-avatar :size="{ xs: 24, sm: 32, md: 40, lg: 64, xl: 64, xxl: 64 }" style="background-color: #1890ff">
          {{account.name.slice(0, 1).toUpperCase()}}
        </a-avatar>
        <div class="ml-base">
          <div class="text-title font-bold text-lg">Hi，{{shortName(account.name)}}，欢迎回来</div>
          <div class="text-subtext font-bold text-sm">{{account.company_name}}</div>
        </div>
      </div>
      <div class="flex items-start">
        <a-statistic class="w-20" :valueStyle="{ fontWeight: 500 }" title="API keys" :value="`${totalStats?.apikey_total}`"></a-statistic>
        <a-statistic class="w-20" :valueStyle="{ fontWeight: 500 }" title="知识库" :value="`${totalStats?.knowledge_base_total}`"></a-statistic>
        <a-statistic class="w-20" :valueStyle="{ fontWeight: 500 }" title="文档" :value="`${totalStats?.document_total}`"></a-statistic>
        <a-statistic class="w-20" :valueStyle="{ fontWeight: 500 }" title="发布" :value="`${totalStats?.release_total}`"></a-statistic>
      </div>
    </div>
    <div class="grid grid-cols-12 gap-6">
      <mini-statistic-card
        class="card col-span-12 mdx:col-span-6 xlx:col-span-3"
        v-for="(item, i) in statisticList"
        :key="i"
        :title="item.title"
        :value="item.value"
        :change="item.change"
      >
        <template #icon>
          <component
            :class="`text-[96px] translate-x-[25%] translate-y-[25%] opacity-75 ${item.iconClass}`"
            v-bind:is="item.icon"
          />
        </template>
      </mini-statistic-card>
    </div>
    <div class="overview grid grid-cols-12 gap-4" v-if="apikeyUsageList !== null">
      <key-usage class="col-span-12 xlx:col-span-6 xxlx:col-span-5 drop-shadow-sm" :apikeyUsageList="apikeyUsageList"/>
      <token-usage class="col-span-12 xlx:col-span-6 xxlx:col-span-7 drop-shadow-sm" :apikeyUsageList="apikeyUsageList"/>
    </div>
    <about-us />
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted } from 'vue';
  import MiniStatisticCard from '@/components/statistic/MiniStatisticCard.vue';
  import KeyUsage from './KeyUsage.vue';
  import TokenUsage from './TokenUsage.vue';
  import AboutUs from './AboutUs.vue';
  import { useUnbounded } from '@/utils/useTheme';
  import { useAccountStore } from '@/store';
  import { storeToRefs } from 'pinia';
  import { useApikeyStore } from '@/store/apikey';
  import dayjs from 'dayjs';
  import { formatThousand } from '@/utils/formatter';

  const accountStore = useAccountStore();

  const {account} = accountStore;
  const apikeyStore = useApikeyStore();

  const { getApikeyTotal, getApikeyUsage } = apikeyStore;
  const { totalStats, apikeyUsageList } = storeToRefs(apikeyStore);

  useUnbounded();

  const shortName = (str) => {
    var pattern = new RegExp("[\u4E00-\u9FA5]+");
    if(pattern.test(str)){  
      return str.slice(1,);
    }
    return str;
  }

  const statisticList = computed(() => {
    let today = dayjs().format('YYYY-MM-DD');
    let yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD');    
    console.log(typeof apikeyUsageList);
    console.log(apikeyUsageList.value);
    let todayList = [];
    let yesterdayList = [];
    if (apikeyUsageList.value !== null){
      todayList = apikeyUsageList.value.filter((item) => item.date === today);
      yesterdayList = apikeyUsageList.value.filter((item) => item.date === yesterday);
    }
    const todayStats = todayList.reduce((curr, next) => curr = { 
        call_count: curr.call_count + next.call_count,
        total_tokens_sum: curr.total_tokens_sum + next.total_tokens_sum,
      },{
        call_count: 0,
        total_tokens_sum: 0
      });

    const yesterdayStats = yesterdayList.reduce((curr, next) => curr = { 
        call_count: curr.call_count + next.call_count,
        total_tokens_sum: curr.total_tokens_sum + next.total_tokens_sum,
      },{
        call_count: 0,
        total_tokens_sum: 0
      });

    let call_count_rate = yesterdayStats.call_count ===0?0:(todayStats.call_count - yesterdayStats.call_count) / yesterdayStats.call_count * 100 
    call_count_rate = parseInt(call_count_rate.toString())
    let call_count_rate_str = call_count_rate === 0?'':call_count_rate > 0?'+' + call_count_rate + '%':call_count_rate + '%'
    let total_tokens_sum_rate = yesterdayStats.total_tokens_sum ===0?0:(todayStats.total_tokens_sum - yesterdayStats.total_tokens_sum) / yesterdayStats.total_tokens_sum * 100
    total_tokens_sum_rate =  parseInt(total_tokens_sum_rate.toString())
    let total_tokens_sum_rate_str = total_tokens_sum_rate === 0?'':total_tokens_sum_rate > 0?'+' + total_tokens_sum_rate + '%':total_tokens_sum_rate + '%'

    return [
      {
        title: '昨日对话次数',
        value: formatThousand(yesterdayStats.call_count, 0),
        change: '',
        icon: 'bar-chart-outlined',
        iconClass: 'text-blue-100',
      },
      {
        title: '昨日token用量 (k)',
        value: formatThousand(yesterdayStats.total_tokens_sum/1000, 0),
        change: '',
        icon: 'line-chart-outlined',
        iconClass: 'text-purple-100',
      },
      {
        title: '今日实时对话次数',
        value: formatThousand(todayStats.call_count, 0),
        change: call_count_rate_str,
        icon: 'bar-chart-outlined',
        iconClass: 'text-primary-100',
      },
      {
        title: '今日实时token用量 (k)',
        value: formatThousand(todayStats.total_tokens_sum/1000, 0),
        change: total_tokens_sum_rate_str,
        icon: 'line-chart-outlined',
        iconClass: 'text-green-100',
      },
    ]
  });

  const params = {
    start_date: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
    end_date: dayjs().format('YYYY-MM-DD'),
  }
  onMounted(() => {
    getApikeyTotal();
    getApikeyUsage(params);
  });

</script>

<style scoped lang="less">
  .workplace {
  }
</style>
