<template>
  <div class="workplace grid grid-rows-none gap-4 mt-xxs">
    <div class="grid grid-cols-12 gap-6" v-if="account.is_superuser">
      <mini-statistic-card
        class="card col-span-12 mdx:col-span-6 xlx:col-span-3"
        v-for="(item, i) in accStatisticList"
        :key="i"
        :title="item.title"
        :value="item.value"
        :change="item.change"
        @click="viewAccountStatsDetail(item)"
      >
        <template #icon>
          <component
            :class="`text-[96px] translate-x-[25%] translate-y-[25%] opacity-75 ${item.iconClass}`"
            v-bind:is="item.icon"
          />
        </template>
      </mini-statistic-card>
    </div>
    <div class="bg-container p-base rounded-b-lg rounded-tr-lg flex items-end justify-end">
      <div v-if="account.is_superuser">
        <a-radio-group v-model:value="companyRadioValue" style="margin-right: 10px;" @change="handleCompanyRadioValueChange">
          <a-radio-button value="2">当前账号</a-radio-button>
          <a-radio-button value="1">全部账号</a-radio-button>
        </a-radio-group>
      </div>
      <a-select
        :disabled="companyRadioValue === '1'"
        mode="multiple"
        style="width: 20%;margin-right: 10px;"
        placeholder="Api keys"
        :max-tag-count="1"
        :options="apiKeyIds"
        :filter-option="filterOption"
        show-search
        allowClear
        @change="handleApiKeyChange"
      ></a-select>
      <!-- <a-select
        :disabled="companyRadioValue === '1'"
        mode="multiple"
        style="width: 20%;margin-right: 10px;"
        placeholder="model"
        :max-tag-count="1"
        :options="modelIds"
        allowClear
        @change="handleModelChange"
      ></a-select> -->
      <a-range-picker v-model:value="searchDate" 
        :ranges="ranges"
        :disabled-date="disabledDate"
        :placeholder="['开始日期', '结束日期']"
        :allowClear="false"
        @calendarChange="onCalendarChange"
      />
    </div>
    <div class="grid grid-cols-12 gap-6">
      <mini-statistic-card
        class="card col-span-12 mdx:col-span-6 xlx:col-span-6"
        v-for="(item, i) in statisticList"
        :key="i"
        :title="item.title"
        :value="item.value"
        :change="item.change"
      >
        <template #icon>
          <component
            :class="`text-[96px] translate-x-[25%] translate-y-[25%] opacity-75 ${item.iconClass}`"
            v-bind:is="item.icon"
          />
        </template>
      </mini-statistic-card>
    </div>
    <div class="overview grid grid-cols-12 gap-4" v-if="totalList.length > 0">
      <total-usage class="col-span-12 xlx:col-span-12 xxlx:col-span-12 drop-shadow-sm" :apikeyUsageList="totalList" />
    </div>
    <div v-if="totalList.length > 0" class="pb-4">
      <div :style="{ float: 'right', }">
        <a-select
          v-if="companyRadioValue === '1'"
          v-model:value="exportType"
          style="width: 90px"
          :options="exportTypeOptions"
          size="small"
          @change="handleExportTypeChange"
        >
        </a-select>
        <!-- <a-input style="margin-left: 8px;width: 100px;" placeholder="账号信息" size="small"/> -->
        <a-button type="link" size="small" style="margin-bottom: 8px;" @click="handleExport">导出</a-button>
      </div>
      <a-table :columns="columns" :data-source="tableData" :pagination="false" 
        :showSorterTooltip="false"
      >
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column.dataIndex === 'username'">
            {{ text }}
            <span v-if="text && text.indexOf(`_c${record.account_id}`) > 0" style="color: red;">(已清空)</span>
            <span v-else-if="record.delete_flag" style="color: red;">(已删除)</span>
          </template>
          <template v-if="column.dataIndex === 'courses'">
            <div style="display: flex;
                flex-direction: column; /* 设置为垂直排列 */
                align-items: flex-start; /* 左对齐 */
                gap: 8px; /* 设置标签之间的间距 */">
              <a-tag color="blue" v-for="course in record.courses">
                {{ course }}
              </a-tag>
            </div>
          </template>
          <template v-if="column.dataIndex === 'semesters'">
            <div style="display: flex;
                flex-direction: column; /* 设置为垂直排列 */
                align-items: flex-start; /* 左对齐 */
                gap: 8px; /* 设置标签之间的间距 */">
              <a-tag color="blue" v-for="semester in record.semesters">
                {{ semester }}
              </a-tag>
            </div>
          </template>
          <template v-if="column.dataIndex === 'total_tokens_sum'">
            {{ formatThousand(text/1000, 0) }}
          </template>
          <template v-else-if="column.dataIndex === 'call_count'">
            {{ formatThousand(text, 0) }}
          </template>
        </template>
        <template #summary>
          <a-table-summary-row>
            <a-table-summary-cell>合计</a-table-summary-cell>
            <a-table-summary-cell v-if="columns.length === 4">
              <a-typography-text>-</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
              <a-typography-text>{{ formatThousand(totals.totalCallCount, 0) }}</a-typography-text>
            </a-table-summary-cell>
            <a-table-summary-cell>
              <a-typography-text>{{ formatThousand(totals.totalTokenTokensSum/1000, 0) }}</a-typography-text>
            </a-table-summary-cell>
          </a-table-summary-row>
        </template>
      </a-table>
    </div>
  </div>
  <template>
    <a-drawer 
      :title="detailStatsTitle" 
      :closable="true" 
      size="large" 
      :visible="showStatsDetailDrawerVisible" 
      :destroyOnClose="true"
      @close="changeShowStatsDetailDrawerVisible(false)"
    >
      <template #extra>
        <a-button type="primary" style="margin-right: 8px" @click="copyStatsDetailData()" :disabled="detailRecords.length === 0">复制</a-button>
        <a-button style="margin-right: 8px" @click="changeShowStatsDetailDrawerVisible(false)">取消</a-button>
      </template>
      <a-table 
        size="small" 
        :columns="detailTableColumns" 
        :dataSource="detailRecords" 
        :pagination="{
          pageSize: detailRecordPage.pageSize,
          current: detailRecordPage.current,
          total: detailRecordPage.total,
          pageSizeOptions: pageSizeOptions,
          defaultPageSize: defaultPageSize
        }"
        @change="handleTableChange"
        :scroll="{ y: (height - 200) }"
      >
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column.dataIndex === 'id'">
            {{detailRecordPage.total - ((detailRecordPage.current - 1) * detailRecordPage.pageSize ) - index }}
          </template>
          <template v-else-if="column.dataIndex === 'name'">
            {{ text }}
          </template>
        </template>
      </a-table>
    </a-drawer>
  </template>
</template>
<script lang="ts" setup>
  import { reactive, computed, onMounted, ref, watch } from 'vue';
  import MiniStatisticCard from '@/components/statistic/MiniStatisticCard.vue';
  import { useUnbounded } from '@/utils/useTheme';
  import { storeToRefs } from 'pinia';
  import { useApikeyStore } from '@/store/apikey';
  import TotalUsage from './TotalUsage.vue';
  import { formatThousand } from '@/utils/formatter';
  import { useAccountStore } from '@/store';
  import{
    getAccountStats
  } from "@/api/account";

  import dayjs, { Dayjs } from 'dayjs';
  import * as XLSX from 'xlsx';
  import { useClipboard } from '@vueuse/core'
  import { message } from 'ant-design-vue';
  import { useWindowSize } from '@vueuse/core'

  const { copy } = useClipboard({ legacy: true })
  const { height } = useWindowSize();

  const detailRecordPage = ref({
    pageSize: 50,
    current: 1,
    total: 0,
  });
  const pageSizeOptions = ref<string[]>(['10', '50', '100', '200', '500', '1000']);
  
  // 列表当前页更改
  const handleTableChange = (page, filters, sorter) => {
    console.log(page, sorter);
    localStorage.setItem(`StatsRecordPageSize_${clickType.value}`, JSON.stringify({pageSize: page.pageSize}));
    calcTablePageData(page.current, page.pageSize);
  };

  type RangeValue = [Dayjs, Dayjs];

  const apikeyStore = useApikeyStore();
  const accountStore = useAccountStore();

  const { getApikeyUsage, getApikeyList, getModelList, getApikeyUsageActivateAccount } = apikeyStore;
  const { modelList, apikeyList, apikeyUsageList, activateAccountApikeyUsageList } = storeToRefs(apikeyStore);
  const {account} = accountStore;

  useUnbounded();

  let searchApiKeyIds = ref([]);
  let searchModelIds = ref([]);
  const exportType = ref("1");
  const showStatsDetailDrawerVisible = ref(false);
  const calcLastMonth = ref(false);
  const clickType = ref(0);

  const defaultPageSize = ref(
    localStorage.getItem(`StatsRecordPageSize_${clickType.value}`)? JSON.parse(localStorage.getItem('ChatRecordPageSize')).pageSize : 50
  )

  const exportTypeOptions = [
    {value: "1", label: "按日期"},
    {value: "2", label: "按账号"}
  ];

  const companyRadioValue = ref<string>('2');

  const searchDate = ref<[Dayjs, Dayjs]>();

  const ranges = {
    "昨天": [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')] as RangeValue,
    "今天": [dayjs(), dayjs()] as RangeValue,
    '近7天': [dayjs().subtract(6, 'day'), dayjs()] as RangeValue,
    '近30天': [dayjs().subtract(29, 'day'), dayjs()] as RangeValue,
    '近1年': [dayjs().subtract(12, 'month'), dayjs()] as RangeValue,
  }

  const disabledDate = (current: Dayjs) => {
    // if (!searchDate.value || (searchDate.value as any).length === 0) {
    //   return false;
    // }
    // const tooLate = current.diff(dayjs(), 'days') >= 0;
    // // console.log(current.diff(dayjs(), 'days'))
    // // console.log(current.diff(dayjs().subtract(29, 'day'), 'days'))
    // // const tooEarly = current.diff(dayjs().subtract(29, 'day'), 'days') < 0;
    // const tooLate = searchDate.value[1] && dayjs().diff(searchDate.value[1], 'days') <= 0;
    // const tooEarly = searchDate.value[1] && searchDate.value[1].diff(current, 'days') > 1;
    return false;
  };

  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  const accStatisticList = computed(() => {
    let active_account_total = 0;
    if (activateAccountApikeyUsageList.value) {
      const accountCount =  activateAccountApikeyUsageList.value.reduce((acc, item) => {
        if (!acc[item.account_id]) {
          acc[item.account_id] = 0;
        }
        acc[item.account_id] += item.call_count;
        return acc;
      }, {});

      active_account_total = Object.entries(accountCount).filter(
        ([accountId, total]) => total > 10
      ).length;
    }
  
    return [
      {
        title: '总用户数',
        value: formatThousand(accountStatsDict.value.account_total, 0),
        change: '',
        icon: 'dot-chart-outlined',
        iconClass: 'text-primary-100',
      },
      {
        title: '服务院系',
        value: formatThousand(accountStatsDict.value.department_total, 0),
        change: '',
        icon: 'dot-chart-outlined',
        iconClass: 'text-primary-100',
      },
      {
        title: '服务课程',
        value: formatThousand(accountStatsDict.value.course_total, 0),
        change: '',
        icon: 'dot-chart-outlined',
        iconClass: 'text-green-100',
      },
      {
        title: '上月活跃用户', 
        value: formatThousand(active_account_total, 0),
        change: '',
        icon: 'dot-chart-outlined',
        iconClass: 'text-green-100',
      },
    ]
  });

  const statisticList = computed(() => {
    const _apikeyUsageList = apikeyUsageList.value?apikeyUsageList.value:[];
    const totalStats = _apikeyUsageList.reduce((curr, next) => curr = { 
        call_count: curr.call_count + next.call_count,
        total_tokens_sum: curr.total_tokens_sum + next.total_tokens_sum,
      },{
        call_count: 0,
        total_tokens_sum: 0
      });

    return [
      {
        title: '对话次数',
        value: formatThousand(totalStats.call_count, 0),
        change: '',
        // icon: 'bar-chart-outlined',
        // iconClass: 'text-primary-100',
      },
      {
        title: 'Token用量 (k)',
        value: formatThousand(totalStats.total_tokens_sum/1000, 0),
        change: '',
        // icon: 'line-chart-outlined',
        // iconClass: 'text-green-100',
      },
    ]
  });
  
  const columns = ref<TableColumnsType>([
      {
        title: '日期',
        dataIndex: 'date',
      },
      {
        title: '对话次数',
        dataIndex: 'call_count',
        sorter: {
          compare: (a, b) => a.call_count - b.call_count,
          multiple: 1,
        },
      },
      {
        title: 'Token用量 (k)',
        dataIndex: 'total_tokens_sum',
        sorter: {
          compare: (a, b) => a.total_tokens_sum - b.total_tokens_sum,
          multiple: 2,
        },
      },
    ]);

  const totals = computed(() => {
    let totalCallCount = 0;
    let totalTokenTokensSum = 0;

    apikeyUsageList.value.forEach(({ call_count, total_tokens_sum }) => {
      totalCallCount += call_count;
      totalTokenTokensSum += total_tokens_sum;
    });
    return { totalCallCount, totalTokenTokensSum };
  });

  const search = async () => {
      const params = {
        start_date: searchDate.value?dayjs(searchDate.value[0]).format("YYYY-MM-DD"):"",
        end_date: searchDate.value?dayjs(searchDate.value[1]).format("YYYY-MM-DD"):"",
        apikey_id: searchApiKeyIds.value,
        model_id: searchModelIds.value,
        data_range: companyRadioValue.value,
      }
      await getApikeyUsage(params);
  };

  const handleExport = () => {
    let head = {
      username: '账号',
      name: '姓名',
      department: '院系',
      semesters: '学期',
      courses: '课程',
      partner: '单位',
      date: '日期',
      call_count: '对话次数',
      total_tokens_sum: 'Token用量(k)',
    }
    const results = tableData.value.map(item => {
      const obj = {}
      for (const k in item) {
        if (head[k]) {
          if (k === "date"){
            obj[head[k]] = dayjs(item[k]).format('YYYY-MM-DD')
          }else if (k === "username"){
            obj[head[k]] = item[k]
          }else if (k === "name"){
            obj[head[k]] = item[k]
          }else if (k === "department"){
            obj[head[k]] = item[k]
          }else if (k === "semesters"){
            obj[head[k]] = item[k]?.join('\n')
          }else if (k === "courses"){
            obj[head[k]] = item[k]?.join('\n')
          }else if (k === "partner"){
            obj[head[k]] = item[k]
          }else if (k === "call_count"){
            obj[head[k]] = formatThousand(item[k], 0)
          }else{
            obj[head[k]] = formatThousand(item[k]/1000, 0)
          }
        } 
      }
      return obj
    })
    if (exportType.value === '2') {
      results.push({
        ["账号"]: "合计",
        "对话次数": formatThousand(totals.value.totalCallCount, 0),
        "Token用量(k)": formatThousand(totals.value.totalTokenTokensSum/1000, 0),
        ["姓名"]: "",
        ["院系"]: "",
        ["学期"]: "",
        ["课程"]: "",
        ["单位"]: "",
      })
    }else{
      results.push({
        [exportType.value === '1'?"日期":"账号"]: "合计",
        "对话次数": formatThousand(totals.value.totalCallCount, 0),
        "Token用量(k)": formatThousand(totals.value.totalTokenTokensSum/1000, 0),
      })
    }
    // 创建工作表
    const dataExcel = XLSX.utils.json_to_sheet(results)
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    // 将工作表放入工作簿中
    XLSX.utils.book_append_sheet(wb, dataExcel, '用量统计')
    // 生成文件并下载
    XLSX.writeFile(wb, `${companyRadioValue.value === '2'?"当前账号":"全公司"}用量统计${dayjs(searchDate.value[0]).format('YYYYMMDD')}至${dayjs(searchDate.value[1]).format('YYYYMMDD')}.xlsx`)
  }

  const onCalendarChange = (val: RangeValue) => {
    calcLastMonth.value = false;
    // console.log(val);
    if(val === null || val[0] === null || val[1] === null){
      return;
    }
    searchDate.value = [dayjs(val[0]), dayjs(val[1])];
  };

  const handleCompanyRadioValueChange: SelectProps['onChange'] = value => {
    console.log(value)
    calcLastMonth.value = false;
  };

  const handleApiKeyChange: SelectProps['onChange'] = value => {
    calcLastMonth.value = false;
    searchApiKeyIds.value = value;
  };

  function exportChange(value){
    exportType.value = value;
    if (value === '1'){
      columns.value = [
        {
          title: '日期',
          dataIndex: 'date',
        },
        {
          title: '对话次数',
          dataIndex: 'call_count',
          sorter: {
            compare: (a, b) => a.call_count - b.call_count,
            multiple: 1,
          },
        },
        {
          title: 'Token用量 (k)',
          dataIndex: 'total_tokens_sum',
          sorter: {
            compare: (a, b) => a.total_tokens_sum - b.total_tokens_sum,
            multiple: 2,
          },
        },
      ];
    }else{
      columns.value = [
        {
          title: '账号',
          dataIndex: 'username',
        },
        {
          title: '对话次数',
          dataIndex: 'call_count',
          sorter: {
            compare: (a, b) => a.call_count - b.call_count,
            multiple: 1,
          },
        },
        {
          title: 'Token用量 (k)',
          dataIndex: 'total_tokens_sum',
          sorter: {
            compare: (a, b) => a.total_tokens_sum - b.total_tokens_sum,
            multiple: 1,
          },
        },
        {
          title: '姓名',
          dataIndex: 'name',
        },
        {
          title: '院系',
          dataIndex: 'department',
        },
        {
          title: '学期',
          dataIndex: 'semesters',
        },
        {
          title: '课程',
          dataIndex: 'courses',
        },
        {
          title: '单位',
          dataIndex: 'partner',
        },
      ];
    }
  }

  const handleExportTypeChange: SelectProps['onChange'] = value => {
      exportChange(value);
      calcTableData(false);
  };

  const handleModelChange: SelectProps['onChange'] = value => {
      searchModelIds.value = value;
  };

  function mergeObject(arr) {
    const result = arr.reduce((obj, item) => {
      if (!obj[item.key]) {
        obj[item.key] = 0
      }
      obj[item.key] += item.value
      return obj
    }, {})
    return Object.keys(result).map(key => ({key: key, value: result[key]}))
  }

  function getTotalList(){
    const searchStartDate = searchDate.value?dayjs(searchDate.value[0]):dayjs();
    const searchEndDate = searchDate.value?dayjs(searchDate.value[1]):dayjs();
    let intervalDay = dayjs().diff(searchEndDate, "day");
    let days = dayjs(searchEndDate).diff(dayjs(searchStartDate), "day");
    days = intervalDay < 0?days+intervalDay:days+1; 
    intervalDay = intervalDay < 0?0:intervalDay;

    let _totalList = Array.from({ length: days }, (v, i) => i).map(day => {
      const date = dayjs().subtract(day + intervalDay, 'day').format('YYYY-MM-DD');
      // console.log(date);
      const dateList = apikeyUsageList.value.filter((item) => item.date === date);
      // console.log(dateList);
      if (dateList.length > 0){
        const totalStats = dateList.reduce((curr, next) => curr = { 
          date: curr.date,
          call_count: curr.call_count + next.call_count,
          total_tokens_sum: curr.total_tokens_sum + next.total_tokens_sum,
        },{
          date: date,
          call_count: 0,
          total_tokens_sum: 0
        });
        return totalStats
      }
      return {date: date, call_count: 0, total_tokens_sum: 0};
    });
    _totalList.sort((a, b) => dayjs(a.date).unix() - dayjs(b.date).unix());
    console.log(_totalList);
    return _totalList;
  }

  function calcTableData(isCalcList){
    if(exportType.value === '1'){
      let _totalList = getTotalList();
      if (isCalcList === true){
        totalList.value = _totalList;
      }
      if (calcLastMonth.value === true){
        _totalList = _totalList.filter(item=> item.call_count > 10);
      }
      tableData.value = _totalList;
    }else{
      const usernameList = Array.from(new Set(apikeyUsageList.value.map(item=>{
        return item["username"];
      })));
      let _totalList = usernameList.map(username => {
        const dataList = apikeyUsageList.value.filter((item) => item.username === username);
        const totalStats = dataList.reduce((curr, next) => curr = { 
          username: next.username,
          name: next.name,
          department: next.department,
          semesters: next.semesters,
          courses: next.courses,
          partner: next.partner,
          delete_flag: next.delete_flag,
          call_count: curr.call_count + next.call_count,
          total_tokens_sum: curr.total_tokens_sum + next.total_tokens_sum,
        }, {
          username: "",
          name: "",
          department: "",
          semesters: [],
          courses: [],
          partner: "",
          delete_flag: false,
          call_count: 0,
          total_tokens_sum: 0
        });
        return totalStats;
      })
      const _totalListChart = getTotalList();
      totalList.value = _totalListChart; 
      if (calcLastMonth.value === true){
        _totalList = _totalList.filter(item=>item.call_count > 10);
      }
      tableData.value = _totalList;
    }
  }

  const accountStatsDict = ref({
    account_total: 0, 
    course_total: 0,
    department_total: 0,
    departments: [],
    courses: [],
  });
  
  function getSubAccountStatsData(){
    getAccountStats().then((res) => {
      const { code, data } = res;
      if (code === 0){
        accountStatsDict.value = data;
      }
    })
  }

  const detailTableColumns = ref([]);
  const detailStatsTitle = ref("");
  const detailRecords = ref([]);
  const totalRecords = ref([]);

  function calcTablePageData(current, pageSize){
    const start = (current - 1) * pageSize;
    const end = start + pageSize;

    detailRecordPage.value = {
      pageSize: pageSize,
      current: current,
      total: totalRecords.value.length,
    }
    detailRecords.value = totalRecords.value.slice(start, end);
  }
  
  function viewAccountStatsDetail(item){
    if (item.title === "总用户数") {
      return;
    }
    if (item.title === "上月活跃用户") {
      calcLastMonth.value = true;
      const lastMonth = dayjs().subtract(1, 'month')
      // 获取上个月的月初（第一天）
      const lastMonthStart = lastMonth.startOf('month');
      // 获取上个月的月末（最后一天）
      const lastMonthEnd = lastMonth.endOf('month');
      companyRadioValue.value = '1';
      searchDate.value = [lastMonthStart, lastMonthEnd,];
      exportChange('2');
      calcTableData(false);
      return;
    }
    if (item.title === "服务院系") {
      clickType.value = 1;
      detailStatsTitle.value = "服务院系";
      detailTableColumns.value = [
        {
          title: '名称',
          dataIndex: 'name',
        },
      ];
      totalRecords.value = accountStatsDict.value.departments?.map(item => {
        return {
          name: `${item}`
        }
      });
      calcTablePageData(
        detailRecordPage.value.current, detailRecordPage.value.pageSize
      )
    }else if (item.title === "服务课程") {
      clickType.value = 2;
      detailStatsTitle.value = "服务课程";
      detailTableColumns.value = [
        {
          title: '名称',
          dataIndex: 'name',
        },
      ];
      totalRecords.value = accountStatsDict.value.courses?.map(item => {
        return {
          name: `${item}`
        }
      });
      calcTablePageData(
        detailRecordPage.value.current, detailRecordPage.value.pageSize
      )
    }
    changeShowStatsDetailDrawerVisible(true);
  }

  function changeShowStatsDetailDrawerVisible(visible){
    showStatsDetailDrawerVisible.value = visible;
  }

  function copyStatsDetailData(){
    const text = detailRecords.value.map(item => item.name).join('\n');
    copy(text)
    message.success('已复制')
  }

  const totalList = ref([]);
  const tableData = ref([]);

  const refreshData = async () => {
    await search();
    calcTableData(true);
  };
  
  const apiKeyIds = ref([]);
  const modelIds = ref([]);

  watch(apikeyList, (val) => {
    apiKeyIds.value = val?.map(item => {
      return {
        label: `${item.name}`,
        value: `${item.id}`,
      }
    })
  })

  watch(modelList, (val) => {
    modelIds.value = val?.map(item => {
      return {
        label: `${item.name}`,
        value: `${item.id}`,
      }
    })
  })

  watch(companyRadioValue, () => {
    if (companyRadioValue.value === '2'){
      exportType.value = '1'
      columns.value = [
          {
            title: '日期',
            dataIndex: 'date',
          },
          {
            title: '对话次数',
            dataIndex: 'call_count',
            sorter: {
              compare: (a, b) => a.call_count - b.call_count,
              multiple: 1,
            },
          },
          {
            title: 'Token用量 (k)',
            dataIndex: 'total_tokens_sum',
            sorter: {
              compare: (a, b) => a.total_tokens_sum - b.total_tokens_sum,
              multiple: 1,
            },
          },
        ];
    }
    refreshData();
  });
  watch(searchDate, refreshData);
  watch(searchApiKeyIds, refreshData);
  watch(searchModelIds, () => {
    refreshData();
  });

  onMounted(() => {
    getApikeyList();
    getModelList();
    if (account.is_superuser){
      getSubAccountStatsData();
      const lastMonth = dayjs().subtract(1, 'month')
      // 获取上个月的月初（第一天）
      const lastMonthStart = lastMonth.startOf('month');
      // 获取上个月的月末（最后一天）
      const lastMonthEnd = lastMonth.endOf('month');

      const params = {
        start_date: lastMonthStart.format('YYYY-MM-DD'), 
        // start_date: "2023-01-01", 
        end_date: lastMonthEnd.format('YYYY-MM-DD'), 
        data_range: "1"
      }
      getApikeyUsageActivateAccount(params);
    }
    searchDate.value = [dayjs().subtract(6, 'day'), dayjs(),];
  });
</script>
