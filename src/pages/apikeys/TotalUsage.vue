<template>
  <div class="sales-overview card p-lg">
    <OverviewTitle title="用量统计" />
    <total-usage-line-chart 
      :list="apikeyUsageList" 
      :key="Math.random() * 100000000000000000"
    />
  </div>
</template>
<script lang="ts" >
  import { defineComponent } from 'vue';
  import TotalUsageLineChart from '@/components/chart/TotalUsageLineChart.vue';
  import OverviewTitle from '@/components/statistic/OverviewTitle.vue';

  export default defineComponent({
    components: { TotalUsageLineChart, OverviewTitle },
    name: 'TokenUsage',
    props:{
      apikeyUsageList:{
        type: Object
      }
    },
    setup(props, { attrs, slots, emit }) {
      // const apikeyUsageList = props.apikeyUsageList;
      // return {apikeyUsageList};
    },
  });
</script>
<style scoped lang="less">
  .sales-overview {
    :deep(.line-chart) {
      @apply -mt-10;
    }
  }
</style>
