<template>
  <div class="key-usage card p-lg">
    <bar-chart
      :color="['#ffffff']"
      :list=chartList
      :key="Math.random() * 100000000000000000"
    />
    <div class="details">
      <OverviewTitle title="近7日对话次数" up />
      <div class="content">近7日服务用量数据</div>
      <div class="statistics">
        <a-statistic :title="item.title" :value="item.value" v-for="(item, i) in statisticList" :key="i" />
      </div>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive } from 'vue';
  import BarChart from '@/components/chart/BarChart.vue';
  import OverviewTitle from '@/components/statistic/OverviewTitle.vue';
  import dayjs from 'dayjs';
  import { formatThousand } from '@/utils/formatter';

  export default defineComponent({
    name: 'KeyUsage',
    components: { Bar<PERSON>hart, OverviewTitle },
    props:{
      apikeyUsageList:{
          type: Object
      }
    },
    setup(props, { attrs, slots, emit }) {
      
      console.log(props.apikeyUsageList);

      let apikeyIdSet = new Set();

      props.apikeyUsageList.forEach(obj => {
        apikeyIdSet.add(obj.apikey_id);
      });

      const stats = props.apikeyUsageList.reduce((curr, next) => curr = { 
        call_count: curr.call_count + next.call_count,
        total_tokens_sum: curr.total_tokens_sum + next.total_tokens_sum,
      },{
        call_count: 0,
        total_tokens_sum: 0
      });

      const statisticList = reactive([
        { title: 'API keys', value: apikeyIdSet.size },
        { title: '对话次数', value: formatThousand(stats.call_count, 0) },
        { title: 'Token用量 (k)', value: formatThousand(stats.total_tokens_sum/1000, 0) },
      ]);

      let change = "+7%";

      const days = 7;
      const dateList = Array.from({ length: days }, (v, i) => i).map(day => {
        let date = dayjs().subtract(day, 'day').format('YYYY-MM-DD');
        const dateList = props.apikeyUsageList.filter((item) => item.date === date);
        if (dateList.length > 0){
          return [dayjs(date).format('MM/DD'), dateList[0].call_count]
        }
        return [dayjs(date).format('MM/DD'), 0];
      });
      console.log(dateList)
      // dateList.sort((a, b) => b[0] - a[0]);
      const chartList = dateList.reverse();
      console.log("chartListchartListchartListchartListchartList");
      console.log(chartList);
      return { statisticList, chartList, change };
    },
  });
</script>
<style scoped lang="less">
  .key-usage {
    .details {
      @apply mt-lg;
    }
    .content {
      @apply mt-md text-title;
    }
    .statistics {
      @apply flex mt-md;
      :deep(.ant-statistic) {
        @apply flex-1 flex flex-col-reverse;
        .ant-statistic-title {
          @apply text-xs -mt-1;
        }
        .ant-statistic-content {
          @apply font-bold;
        }
      }
    }
  }
</style>
