<template>
  <a-modal
    :key="Math.random() * 100000000000000000"
    :visible=qaModalVisible
    title="维护标准问答"
    width="1200px"
    style="top: 0px;"
    :footer="null"
    @cancel="setQaModalVisible(false)"
  >
    <a-alert
      :message="`如果用户的提问是问答列表中的标准问题，会使用对应的标准回答进行快速响应。`"
      style="background-color: #e6f4ff; border: 1px solid #91caff; margin-bottom: 10px;"
      closable
      v-if="formDataInput.from === 2"
    />
    <a-tabs v-model:activeKey="activeKey" type="card">
      <a-tab-pane key="1">
        <template #tab>
          <span>
            输入问答
            <edit-outlined v-if="formDataInput.id > 0"/>
          </span>
        </template>
        <a-form
          ref="form"
          name="input_source_2"
          :model="formDataInput"
          @finish="onFinishInput"
          >
          <a-form-item required name="question"
            :rules="[{ required: true, message: '请输入标准问题' }]"
          >
            <a-input v-model:value.trim="formDataInput.question" placeholder="输入标准问题" :maxlength="200" show-count/>
          </a-form-item>
          <a-form-item required name="answer"
            :rules="[{ required: true, message: '请输入标准回答' }]"
          >
            <a-textarea v-model:value.trim="formDataInput.answer" placeholder="输入标准回答"
            :rows="14" :maxlength="3000" show-count/>
          </a-form-item>
          <a-form-item help="(最多上传9张图片)">
            <a-upload list-type="picture-card" multiple :maxCount="9"
              accept=".png,.jpg,.jpeg,.webp,.gif"
              v-model:fileList="formDataInput.image_urls"
              :customRequest="customRequest"
              :before-upload="beforeUpload"
              @change="handleChange"
             >
              <div>
                <PlusOutlined />
              </div>
            </a-upload>
          </a-form-item>
          <a-form-item required name="release_id" label="选择要加入的应用" v-if="formDataInput.from === 2"
            :rules="[{ required: true, message: '请选择要加入的应用' }]"
          >
            <a-select
              style="width: 30%;"
              placeholder="选择要加入的应用"
              v-model:value="formDataInput.release_id"
              :options="releaseIds"
            ></a-select>
          </a-form-item>
          <a-form-item
            :wrapper-col="{
              xs: { span: 24, offset: 0 },
              sm: { span: 16, offset: 11 },
            }"
          >
            <a-button
              type="default"
              @click="setQaModalVisible(false)"
              >关闭</a-button>
              &nbsp;
            <a-button
              type="primary"
              html-type="submit"
              :disabled="!(formDataInput.question !== '' && formDataInput.answer !== '' && parseInt(formDataInput.release_id) > 0 && uploadComplete)"
              :loading="loading"
              >确认</a-button>
          </a-form-item>
        </a-form>
      </a-tab-pane>
      <a-tab-pane key="2" tab="批量导入" v-if="formDataInput.from === 1">
        <div>
          <a-steps :current="importQaCurrent">
            <a-step v-for="item in importUserSteps" :key="item.title" :title="item.title" />
          </a-steps>
          <div v-if="importQaCurrent === 0">
            <a-upload-dragger
              style="margin-top: 16px;"
              accept=".xls,.xlsx"
              :showUploadList="false"
              :multiple="false"
              :before-upload="handleImportQaBeforeUpload"
            >
              <p class="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p class="ant-upload-text">将Excel文件拖到此处或点击上传</p>
              <p class="ant-upload-hint">单次导入不超过1000条</p>
            </a-upload-dragger>
            <a target="blank" href="https://res.isapientia.com/tpl/QA%E9%97%AE%E7%AD%94%E6%A8%A1%E6%9D%BF.xlsx">下载Excel数据模板</a>
          </div>
          <div v-if="importQaCurrent === 1">
            <a-table
              style="margin-top: 16px;"
              bordered
              :data-source="importQaTableData"
              :columns="importQaTableColumns"
              :pagination="false"
              size="small"
              :scroll="{ y: 400 }"
            >
            <template #title>
              <div class="text-subtext text-sm flex justify-between items-center">
                <span style="font-size:14px;">共 {{ importQaTableData.length }} 条记录</span>
              </div>
            </template>
            <template #bodyCell="{ column, text, record }">
              <template v-if="['question'].includes(column.dataIndex)">
                <div>
                  <a-input
                    v-if="editableData[record.key]"
                    v-model:value="editableData[record.key][column.dataIndex]"
                    style="margin: -5px 0"
                    :maxlength="100"
                  />
                  <template v-else>
                    {{ text }}
                  </template>
                </div>
              </template>
              <template v-else-if="['answer'].includes(column.dataIndex)">
                <div>
                  <a-textarea
                    v-if="editableData[record.key]"
                    v-model:value="editableData[record.key][column.dataIndex]"
                    style="margin: -5px 0"
                    :rows="1"
                    :maxlength="3000"
                  />
                  <template v-else>
                    {{ text }}
                  </template>
                </div>
              </template>
              <template v-else-if="column.dataIndex === 'operation'">
                <div class="editable-row-operations">
                  <span v-if="editableData[record.key]">
                    <a-typography-link @click="save(record.key)">保存</a-typography-link>
                    &nbsp;
                    <a-typography-link @click="cancel(record.key)">取消</a-typography-link>
                  </span>
                  <span v-else>
                    <a-typography-link @click="edit(record.key)">修改</a-typography-link>
                    &nbsp;
                    <a-popconfirm title="确认要删除吗?" @confirm="remove(record.key)">
                      <a-typography-link style="color: red;">删除</a-typography-link>
                    </a-popconfirm>
                  </span>
                </div>
              </template>
            </template>
            </a-table>
          </div>
          <div v-if="importQaCurrent === 2">
            <a-result
              status="success"
              title="导入结果"
              :sub-title="`总共${importQaTableData.length}条记录，成功导入${importQaSuccessCount}条，失败${importQaFailedResult.length}条`"
            >
              <template #extra>
                <a-button type="primary" @click="continueImport">继续导入</a-button>
                <a-button @click="goTab('3')">查看</a-button>
              </template>
              <div v-if="importQaFailedResult.length > 0" style="max-height: 200px; overflow: auto;">
                <p style="font-size: 16px">
                  <strong>导入失败记录的原因:</strong>
                </p>
                <template v-for="(failed_reason, i) in importQaFailedResult" :key="i">
                  <p>
                    <close-circle-outlined :style="{ color: 'red' }" />
                    {{ failed_reason }}
                  </p>
                </template>
              </div>
            </a-result>
          </div>
          <div class="steps-action">
            <a-button v-if="importQaCurrent === 0"
              type="primary"
              :disabled="importQaTableData.length === 0"
              @click="importQaNext">下一步</a-button>
            <div v-if="importQaCurrent === 1" >
              <a-button
                type="primary"
                :disabled="importQaTableData.length === 0"
                :loading="loading"
                @click="batchUploadQaRecord">提交</a-button>
              <a-button style="margin-left: 8px" @click="importQaPrev">重新上传</a-button>
            </div>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="3" tab="问答列表" v-if="false">
        <a-table
          size="small"
          :columns="qaColumns"
          :dataSource="qaTableData.results"
          :pagination="{ pageSize: qaTableData.page_size, current: qaTableData.page_number, total: qaTableData.count }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ text, record, index, column }">
            <template v-if="column.dataIndex === 'operation'">
              <a-button
              v-if="record.input_source !== 1"
              type="link"
              size="small"
              :disabled="record.status === 0"
              @click="editQaRecord(record)">修改</a-button>
              <a-popconfirm placement="topLeft" title="确认删除吗？" @confirm="removeQaRecord(record)">
                <a-button type="link" danger size="small">删除</a-button>
              </a-popconfirm>
            </template>
            <template v-if="column.dataIndex === 'id'">
              {{qaTableData.count - ((qaTableData.page_number - 1) * qaTableData.page_size ) - index }}
            </template>
            <template v-else-if="column.dataIndex === 'update_time'">
              {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
            </template>
            <template v-else-if="column.dataIndex === 'sample_answer'">
              <PictureTwoTone v-if="record.image_urls.length > 0" style="margin-right: 2px;"/> {{ text }}
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>
<script lang="ts" setup>
import { toRefs, reactive, watch, ref, onMounted} from 'vue';
import { releaseEnterQaPage, releaseEnterQa, deleteEnterQa, releaseBatchUploadQa } from "@/api/release";
import { importExcelData } from '@/utils/importExcel'
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import OSS from 'ali-oss';
import { v4 as uuidv4 } from 'uuid';
import http from '@/store/http';
import { Response } from '@/types';
import { computed } from 'vue';
import { OssStsTokenProps } from '@/store/thirdparty';

const form = ref<FormInstance>();

const props = defineProps({
  qaModalVisible: Boolean,
  initQaEditRecord: Object,
  releaseIds: Array,
});


const {initQaEditRecord} = toRefs(props);

const loading = ref(false);
const activeKey = ref("1");
let fileDict = ref({});
const baseFilePath = ref<string>("file/release/qa/img");

const importQaCurrent = ref<number>(0);
const importQaSuccessCount = ref<number>(0);
const importQaFailedResult = ref([]);
const importQaTableData = ref([]);

const importUserSteps = [
  {
    title: '上传文件',
    content: '上传文件',
  },
  {
    title: '解析结果',
    content: '解析结果',
  },
  {
    title: '导入结果',
    content: '导入结果',
  },
];

const formDataInput = ref({
  'id': 0,
  'question': '',
  'answer': '',
  'release_id': '',
  'source_release_id': 0,
  'from': 1,
  'image_urls': [],
});

const emit = defineEmits(['setQaModalVisible'])


const uploadComplete = computed(() => {
  let image_urls = [];
  if(Object.keys(fileDict.value).length > 0){
    Object.keys(fileDict.value).forEach(function(key){
      const f = fileDict.value[key];
      if(f.url){
        image_urls.push(f.url);
      }
    });
  }
  if (image_urls.length === formDataInput.value.image_urls.length){
    return true
  }
  return false;
})

function resetFormDataInput(){
  formDataInput.value.id = 0
  if(formDataInput.value.from === 2){
    formDataInput.value.release_id = null
    formDataInput.value.source_release_id = 0
  }
  formDataInput.value.question = ''
  formDataInput.value.answer = ''
  formDataInput.value.image_urls = []
  fileDict.value = {}
}

const setQaModalVisible = (visible: boolean) => {
  console.log(visible);
  emit('setQaModalVisible', visible);
  resetFormDataInput()
};

const onFinishInput = (values: any) => {
  loading.value = true;
  console.log('Success:', values, formDataInput, fileDict.value);
  let image_urls = [];
  if(Object.keys(fileDict.value).length > 0){
    Object.keys(fileDict.value).forEach(function(key){
      const f = fileDict.value[key];
      if(f.url){
        image_urls.push(f.url);
      }
    });
  }
  const data = {
    id: formDataInput.value.id,
    release_id: formDataInput.value.release_id,
    question: formDataInput.value.question,
    answer: formDataInput.value.answer,
    image_urls,
  };
  releaseEnterQa(data).then((response) => {
    console.log(response);
    const { code } = response;
    if (code === 0){
      setQaModalVisible(false);
      if(formDataInput.value.from === 1){
        // activeKey.value = '3';
        console.log(formDataInput.value)
      }else{
        message.success("成功加入");
      }
      resetFormDataInput();
    }
  }).catch((e) => {
    console.error(e);
  }).finally(() => {
    loading.value = false;
  });
};

const qaColumns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '标准问题',
      dataIndex: 'question',
      key: 'question',
      ellipsis: true,
    },
    {
      title: '标准回答',
      dataIndex: 'sample_answer',
      key: 'sample_answer',
      ellipsis: true,
    },
    {
      title: '最后修改时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 120,
    },
];

function refreshQaPage(params){
  const data = releaseEnterQaPage(params).then((res) => {
    const { data } = res;
    console.log(data);
    qaTableData.value = data;
    return data;
  });
  return data;
}

const handleTableChange = async (page, filters, sorter) => {
  const params = {
    release_id: props.initQaEditRecord.source_release_id,
    page_size: page.pageSize,
    page_number: page.current,
  }
  if (props.initQaEditRecord.source_release_id > 0){
    refreshQaPage(params);
  }
};

function editQaRecord(record){
  let tmp = {};
  tmp["id"] = record.id
  tmp["question"] = record.question
  tmp["answer"] = record.sample_answer
  tmp["image_urls"] = record.image_urls.map(img_url => {return {
    uid: img_url.split("/").pop().split(".")[0],
    name: img_url,
    status: 'done',
    url: img_url,
  }});
  fileDict.value = tmp["image_urls"].reduce((p, c) => {
    p[c.uid] = c;
    return p;
  }, {});
  tmp["release_id"] = `${initQaEditRecord.value.source_release_id}`
  tmp["source_release_id"] = initQaEditRecord.value.source_release_id
  tmp["from"] = 1
  formDataInput.value = tmp
  console.log(formDataInput.value);
  activeKey.value = '1';
  form.value?.resetFields();
}

function removeQaRecord(record) {
  deleteEnterQa({id: record.id}).then((response) => {
    const { code } = response;
    if (code === 0){
      message.success("已删除");
      refreshQaPage({release_id: initQaEditRecord.value.source_release_id});
    }
  })
}

const qaTableData = ref([]);

const editableData: UnwrapRef<Record<string, DataItem>> = reactive({});

const edit = (key: string) => {
  editableData[key] = cloneDeep(importQaTableData.value.filter(item => key === item.key)[0]);
};

const remove = (rownumber: string) => {
  const idx = importQaTableData.value.findIndex(({ key }) => key !== undefined && key === rownumber);
  importQaTableData.value.splice(idx, 1)
}

const save = (key: string) => {
  Object.assign(importQaTableData.value.filter(item => key === item.key)[0], editableData[key]);
  delete editableData[key];
};

const cancel = (key: string) => {
  delete editableData[key];
};

const importQaTableColumns = [
    {
      title: '行号',
      width: 70,
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: '问题',
      dataIndex: 'question',
      key: 'question',
    },
    {
      title: '回答',
      dataIndex: 'answer',
      key: 'answer',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
    },
]

const importQaPrev = () => {
  importQaCurrent.value--;
};

const importQaNext = () => {
  importQaCurrent.value++;
};

const handleImportQaBeforeUpload = (file) => {
  // 数据处理excel=>json
  importExcelData(file).then((res) => {
    let head = {
      '问题': 'question',
      '回答': 'answer',
    }
    const arr = res.map(item => {
      console.log(item)
      const obj = {}
      console.log(item);
      obj['key'] = item["__rowNum__"] + 1
      for (const k in item) {
        obj[head[k]] = item[k]
      }
      return obj;
    });
    if (arr.length === 0){
      message.error("当前文件没有用于导入的数据");
    }else{
      importQaTableData.value = arr.slice(0, 1000);
      importQaNext();
    }
  });
  return false;
};

const batchUploadQaRecord = () =>{
  loading.value = true;
  releaseBatchUploadQa(
      {
        release_id: initQaEditRecord.value.source_release_id,
        qa_list: importQaTableData.value
      }
    ).then((res) => {
    const { code, data } = res;
    if (code === 0){
      const { success, failed } = data;
      importQaSuccessCount.value = success;
      importQaFailedResult.value = failed;
      // message.success(`导入成功`);
      importQaNext();
    }
  }).finally(() => {
    loading.value = false;
  });
}

function continueImport(){
  importQaCurrent.value = 0;
  importQaTableData.value = [];
  activeKey.value = "2"
}

function goTab(val){
  activeKey.value = val;
}

let ossStsToken = ref<OssStsTokenProps>();

async function getOssStsToken() {
  return http
    .request<OssStsTokenProps, Response<OssStsTokenProps>>('/thirdparty/oss/sts_token?public=1', 'GET')
    .then((res) => {
      console.log(res);
      const { data } = res;
      ossStsToken.value = data;
      return data;
    })
    .finally(() => (console.log("end")));
}

const beforeUpload: UploadProps['beforeUpload'] = async file => {
  const fileExtName = file.name.substring(file.name.lastIndexOf(".") + 1);
  const fileName = uuidv4() + "." + fileExtName;
  const oss_key = baseFilePath.value + '/' + fileName;

  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    message.error('单个图片最大1MB!');
    return false;
  }

  const fileData = {name: file.name, oss_key: oss_key, size: file.size, status: 0, url: ""};
  console.log(fileData);
  fileDict.value[file.uid] = fileData;
  return true;
}

const handleChange = async (info: UploadChangeParam) => {
  // console.log(props.knowledegBaseId, info);
  // console.log(fileDict.value);
  const status = info.file.status;
  console.log(info)
  console.log(fileDict.value)

  if(status === 'removed') {
    // const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
    // const oss_key = fileDict.value[info.file.uid]["oss_key"];
    // // if(fileDict.value){
    // //   oss_key = fileDict.value[info.file.uid]["oss_key"];
    // // }else{
    // //   // https://oss-ai.dqprism.com/file/release/qa/img/010d7f5a-e4c7-482a-9562-806c25b80821.jpg
    // //   oss_key = info.file.uid.split('.com')[1].slice(1,);
    // // }
    // console.log(oss_key)
    // const client = new OSS({
    //   region: ossStsToken.value.region,
    //   accessKeyId: ossStsToken.value.access_key_id,
    //   accessKeySecret: ossStsToken.value.access_key_secret,
    //   stsToken: ossStsToken.value.security_token,
    //   bucket: ossStsToken.value.bucket_name,
    //   endpoint: ossStsToken.value.endpoint,
    //   cname: use_cname,
    //   secure: true,
    // });
    // let result = await client.delete(oss_key);
    // console.log("removed file:", oss_key, result);
    delete fileDict.value[info.file.uid];
  }
};


async function customRequest(info) {
  console.log(info);
  const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
  const client = new OSS({
    region: ossStsToken.value.region,
    accessKeyId: ossStsToken.value.access_key_id,
    accessKeySecret: ossStsToken.value.access_key_secret,
    stsToken: ossStsToken.value.security_token,
    bucket: ossStsToken.value.bucket_name,
    endpoint: ossStsToken.value.endpoint,
    useFetch: true,
    cname: use_cname,
    secure: true,
  });

  const oss_key = fileDict.value[info.file.uid]["oss_key"];
  if(info.file){
    console.log(oss_key)
    await client.multipartUpload(oss_key, info.file, {
      parallel: 4,
      partSize: 100 * 1024,
      progress: function (percent) {
        // console.log(info.file.uid);
        console.log("progress is: ", percent * 100);
        console.log(formDataInput.value.image_urls)
        const fileIdx = formDataInput.value.image_urls.findIndex(obj => obj.uid === info.file.uid);
        console.log(fileIdx);
        formDataInput.value.image_urls[fileIdx].percent = percent * 100;
        if (percent === 1){
          formDataInput.value.image_urls[fileIdx].status = "success";
        }
      },
    }).then(res => {
      console.log('结果:', res);
      fileDict.value[info.file.uid]["status"] = 1;
      fileDict.value[info.file.uid]["url"] = ossStsToken.value.endpoint + '/' + oss_key;
      fileDict.value = fileDict.value;

      let unUploadDoneList = [];
      if(Object.keys(fileDict.value).length > 0){
        Object.keys(fileDict.value).forEach(function(key){
          const f = fileDict.value[key];
          if(f.status !== 1){
            unUploadDoneList.push(f)
          }
        });
      }
    }).catch((err) => {
      console.log(err);
    });
  }
}

watch(activeKey, (val) => {
  if (val === '3' && initQaEditRecord.value.source_release_id > 0){
    refreshQaPage({release_id: initQaEditRecord.value.source_release_id});
    resetFormDataInput();
    form.value?.resetFields();
  }
})

watch(initQaEditRecord, (val) => {
  console.log(val)
  formDataInput.value.id = val.id
  formDataInput.value.question = val.question
  formDataInput.value.answer = val.answer
  formDataInput.value.release_id = val.release_id
  formDataInput.value.source_release_id = val.source_release_id
  formDataInput.value.from = val.from
  formDataInput.value.image_urls = val.image_urls
  fileDict.value = val.image_urls.reduce((p, c) => {
    p[c.uid] = c;
    return p;
  }, {});
})

onMounted(async () => {
  await getOssStsToken();
  console.log(props.initQaEditRecord)
});
</script>

<style scoped>
.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 16px;
}

.steps-action {
  margin-top: 24px;
  text-align: center;
}

[data-theme='dark'] .steps-content {
  background-color: #2f2f2f;
  border: 1px dashed #404040;
}
</style>
