import { defineStore } from 'pinia';
import http from './http';
import { ref } from 'vue';
import { Response } from '@/types';

export interface ApiKeyProps {
  id?: number;
  name: string;
  title?: string;
  view?: string;
}

export interface ApiKeyStatsProps {
  apikey_total?: number;
  knowledge_base_total?: number;
  document_total?: number;
  release_total?: number;
}

export interface ApiKeyUsageProps {
  apikey_id?: number;
  date: string;
  call_count?: number;
  prompt_tokens_sum?: number;
  completion_tokens_sum?: number;
  total_tokens_sum?: number;
}

export const useApikeyStore = defineStore('apikey', () => {
  const modelList = ref<ApiKeyProps[]>([]);
  const apikeyList = ref<ApiKeyProps[]>([]);
  const apikeyPage = ref<ApiKeyProps[]>([]);
  const totalStats = ref<ApiKeyStatsProps>({
    apikey_total: 0,
    knowledge_base_total: 0,
    document_total: 0,
    release_total: 0,
  });
  const apikeyUsageList = ref<ApiKeyUsageProps[]>(null);
  const activateAccountApikeyUsageList = ref<ApiKeyUsageProps[]>(null);
  const loading = ref(false);

  async function getModelList() {
    loading.value = true;
    return http
      .request<ApiKeyProps[], Response<ApiKeyProps[]>>('/app/provider/model/list', 'GET')
      .then((res) => {
        const { data } = res;
        modelList.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  async function getApikeyList(params?) {
    loading.value = true;
    return http
      .request<ApiKeyProps[], Response<ApiKeyProps[]>>('/app/api_key/list', 'GET', params)
      .then((res) => {
        const { data } = res;
        apikeyList.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  async function getApikeyPage() {
    loading.value = true;
    return http
      .request<ApiKeyProps[], Response<ApiKeyProps[]>>('/app/api_key', 'GET')
      .then((res) => {
        const { data } = res;
        apikeyPage.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  async function getApikeyTotal() {
    loading.value = true;
    return http
      .request<ApiKeyStatsProps, Response<ApiKeyStatsProps>>('/app/api_key/stats/total', 'GET')
      .then((res) => {
        const { data } = res;
        totalStats.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  async function getApikeyUsage(params) {
    loading.value = true;
    return http
      .request<ApiKeyUsageProps[], Response<ApiKeyUsageProps[]>>('/app/api_key/stats/usage', 'GET', params)
      .then((res) => {
        const { data } = res;
        apikeyUsageList.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  async function getApikeyUsageActivateAccount(params) {
    loading.value = true;
    return http
      .request<ApiKeyUsageProps[], Response<ApiKeyUsageProps[]>>('/app/api_key/stats/usage', 'GET', params)
      .then((res) => {
        const { data } = res;
        activateAccountApikeyUsageList.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  async function addApikey(apikey: ApiKeyProps) {
    return http
      .request<any, Response<any>>('/app/api_key', 'POST_JSON', apikey)
      .then((res) => {
        return res.data;
      })
      .finally(getApikeyList);
  }

  async function updateApikey(apikey: ApiKeyProps) {
    return http
      .request<any, Response<any>>('/app/api_key', 'PUT_JSON', apikey)
      .then((res) => {
        return res.data;
      })
      .finally(getApikeyList);
  }

  async function removeApikey(id: number) {
    return http
      .request<any, Response<any>>('/app/api_key', 'DELETE', { id })
      .then(async (res) => {
        return res.data;
      })
      .finally(getApikeyList);
  }

  return {
    loading,
    modelList,
    apikeyList,
    apikeyPage,
    totalStats,
    apikeyUsageList,
    activateAccountApikeyUsageList,
    getModelList,
    getApikeyPage,
    getApikeyList,
    getApikeyUsageActivateAccount,
    getApikeyTotal,
    getApikeyUsage,
    addApikey,
    updateApikey,
    removeApikey,
  };
});
