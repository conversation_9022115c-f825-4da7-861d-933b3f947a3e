<template>
  <a-modal
    :key="Math.random() * 100000000000000000"
    :visible=runModalVisible
    :title="`知识库 - 在线体验`"
    width="1000px"
    style="top: 0px;"
    :footer="null"
    @cancel="setModalVisible(false)"
  >
    <div v-if="visibleType === 1">
      <p style="padding-bottom: 30px">请选择要使用的ApiKey及知识库</p>
      <a-form ref="form" :model="formData">
        <a-form-item required name="api_key" label="选择Apikey">
          <a-select
            v-if="apiKeyIds.value.length > 0"
            placeholder="选择要使用的Apikey"
            v-model:value="formData.api_key"
            :options="apiKeyIds"
          ></a-select>
        </a-form-item>
        <a-form-item required name="knowledge_base_ids" label="选择知识库">
          <a-select
            v-if="knowledgeBaseIds.value.length > 0"
            mode="multiple"
            placeholder="选择问答的知识库, 可选多个"
            v-model:value="formData.knowledge_base_ids"
            :max-tag-count="5"
            :options="knowledgeBaseIds"
            :allowClear=false
          ></a-select>
        </a-form-item>
        <a-form-item :wrapper-col="{ ...layout.wrapperCol, offset: 11 }" style="padding-top: 50px;">
          <a-button type="primary" @click="next_run">开始对话</a-button>
        </a-form-item>
      </a-form>
    </div>
    <div v-loading="iframeLoading" v-else>
      <iframe
        id="runIframe"
        :src="iframeUrl"
        frameborder="0"
        style="width: 100%; height: 70vh"
      ></iframe> 
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { reactive, computed, ref, onMounted} from 'vue';
import { storeToRefs } from 'pinia';
import { useApikeyStore } from '@/store/apikey';
import { useKnowledegBaseStore } from '@/store/knowledge';

const layout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

const apikeyStore = useApikeyStore();
const knowledegBaseStore = useKnowledegBaseStore()

const props = defineProps({
  runModalVisible: Boolean,
});

const { getApikeyList } = apikeyStore;
const { apikeyList } = storeToRefs(apikeyStore);
const { knowledegBaseList } = storeToRefs(knowledegBaseStore);

const form = ref<FormInstance>();

const apiKeyIds = computed(() => {
  let apiKeyIdsList = ref([]);
  apikeyList.value.forEach((item) => {
    apiKeyIdsList.value.push({
      label: `${item.name}`,
      value: `${item.api_key}`,
    });
  });
  return apiKeyIdsList;
});

const knowledgeBaseIds = computed(() => {
  let knowledgeBaseIdsList = ref([]);
  knowledegBaseList.value.forEach((item) => {
    knowledgeBaseIdsList.value.push({
      label: `${item.namespace}`,
      value: `${item.id}`,
    });
  });
  return knowledgeBaseIdsList;
});

const formData = reactive<any>({
  api_key: undefined,
  knowledge_base_ids: undefined,
});

const emit = defineEmits(['setRunModalVisible'])
const visibleType = ref<Number>(1);
const iframeLoading = ref<Boolean>(true);

const iframeUrl = ref<string>('');

const setModalVisible = (visible: boolean) => {
  visibleType.value = 1;
  emit('setRunModalVisible', visible);
  form.value?.resetFields();
  formData.api_key = undefined;
  formData.knowledge_base_ids = undefined;
};

function next_run() {
  console.log(formData);
  visibleType.value = 2;
  const knowledge_base_query = "&knowledge_base_ids=" + formData.knowledge_base_ids.join(",");
  console.log(knowledge_base_query);
  iframeUrl.value = "/q/chat?api_key=" + formData.api_key + knowledge_base_query;
  // iframeUrl.value = "http://localhost:9100/t/chat?api_key=" + formData.api_key + knowledge_base_query;
  this.$nextTick(() => {
    const iframe = this.$refs.runIframe;
    iframe.onload = () => {
      console.log("iframe加载完毕");
      iframeLoading.value = false;
    };
  });
}

onMounted(() => {
  getApikeyList();
});
</script>