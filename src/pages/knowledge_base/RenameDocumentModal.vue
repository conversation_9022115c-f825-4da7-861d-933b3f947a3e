<template>
<a-modal
  :visible="renameVisible"
  @cancel="setRenameVisible(false)"
  :maskClosable="false"
  :destroyOnClose="true"
>
  <template #title>
    <span>重命名</span>
  </template>
  <template #footer>
    <a-button key="取消" @click="setRenameVisible(false)">取消</a-button>
    <a-button key="保存" type="primary" :loading="loading" 
    @click="saveRenameDocument">保存</a-button>
  </template>
  <a-form
    layout="vertical"
    ref="form"
    name="input_source_2"
    :model="renameRecord"
  >
    <a-form-item required name="name" 
      :rules="[{ required: true, message: '请输入名称' }]"
    >
      <a-input v-model:value="name" placeholder="请输入名称" show-count :maxlength=100 />
    </a-form-item>
  </a-form>
</a-modal>
</template>
<script lang="ts" setup>
import { ref, onMounted} from 'vue';
const form = ref<FormInstance>();

const props = defineProps({
  loading: Boolean,
  renameVisible: Boolean,
  renameRecord: Object,
});

const name = ref(props.renameRecord.name);

const emit = defineEmits(['setRenameVisible', 'saveRenameDocument'])

const setRenameVisible = (visible: boolean) => {
  console.log(visible);
  emit('setRenameVisible', visible);
};

function saveRenameDocument(){
  const params = {
    id: props.renameRecord.id,
    name: name.value,
  }
  emit('saveRenameDocument', params);
  setRenameVisible(false);
}

onMounted(() => {
  
});
</script>