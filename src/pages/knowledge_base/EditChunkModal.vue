<template>
<a-modal
  :visible="editChunkVisible"
  width="800px"
  style="top: 0px;"
  @cancel="setEditChunkVisible(false)"
  :maskClosable="false"
  :destroyOnClose="true"
>
  <template #title>
    <span v-if="editChunkRecord.id > 0">修改 第 {{editChunkRecord.page}} 页 # {{editChunkRecord.seq}}</span>
    <span v-else>添加分段</span>
  </template>
  <template #footer>
    <a-button key="取消" @click="setEditChunkVisible(false)">取消</a-button>
    <a-button key="保存" type="primary" :loading="loading" 
    :disabled="!(props.editChunkRecord.text !== '' && uploadComplete)"
    @click="onFixTextFinishInput">保存</a-button>
  </template>
  <a-form
    layout="vertical"
    ref="form"
    name="input_source_2"
    :model="editChunkRecord"
  >
    <a-form-item required name="text" label="文本内容"
      :rules="[{ required: true, message: '文本内容' }]"
    >
      <a-textarea v-model:value="editChunkRecord.text" placeholder="文本内容" 
      :rows="14" :maxlength="3000" show-count />
    </a-form-item>
    <a-form-item help="(最多上传6张图片)">
      <template #label>
        <span>配图&nbsp;
          <a-popover title="" trigger="hover" placement="right">
            <template #content>
              对话时，当前文本内容被召回时,如有配图会将图片资源一并作为上下文内容返回，供回答需要展示图片使用。
            </template>
            <QuestionCircleOutlined />
          </a-popover>
        </span>
      </template>
      <a-upload list-type="picture-card" multiple :maxCount="maxCount"
        accept=".png,.jpg,.jpeg,.webp,.gif"
        v-model:fileList="imageUrls"
        :customRequest="customRequest"
        :before-upload="beforeUpload"
        :itemRender="itemRender"
        @change="handleChange"
        >
        <div v-if="imageUrls.length < maxCount">
          <PlusOutlined />
        </div>
      </a-upload>
    </a-form-item>
  </a-form>
</a-modal>
</template>
<script lang="ts" setup>
import { computed, createVNode, ref, onMounted} from 'vue';
import { Input, message } from 'ant-design-vue';
import type { UploadProps, UploadChangeParam } from 'ant-design-vue';

import OSS from 'ali-oss';
import { v4 as uuidv4 } from 'uuid';
import http from '@/store/http';
import { Response } from '@/types';
import { OssStsTokenProps } from '@/store/thirdparty';

const form = ref<FormInstance>();

const props = defineProps({
  loading: Boolean,
  editChunkVisible: Boolean,
  editChunkRecord: Object,
});

const loading = ref(false);
const maxCount = ref(6);
// attachments
const imageUrls = ref(props.editChunkRecord.attachments.map(item => {
  return {
    uid: item.url.split("/").pop().split(".")[0],
    name: item.name,
    status: 'done',
    url: item.url,
  }
}));
const fileDict = ref(imageUrls.value.reduce((p, c) => {
  p[c.uid] = c;
  return p;
}, {}));

const baseFilePath = ref<string>("file/dataset");
let ossStsToken = ref<OssStsTokenProps>();

const emit = defineEmits(['setEditChunkVisible', 'onFixTextFinishInput'])

const setEditChunkVisible = (visible: boolean) => {
  console.log(visible);
  emit('setEditChunkVisible', visible);
};

function onFixTextFinishInput(){
  let attachments = [];
  if(Object.keys(fileDict.value).length > 0){
    Object.keys(fileDict.value).forEach(function(key){
      const f = fileDict.value[key];
      if(f.url){
        attachments.push({name: f.name, url: f.url});
      }
    });
  }
  const params = {
    id: props.editChunkRecord.id,
    document_id: props.editChunkRecord.document_id,
    text: props.editChunkRecord.text,
    attachments: attachments,
  }
  emit('onFixTextFinishInput', params);
  setEditChunkVisible(false);
}

async function getOssStsToken() {
  return http
    .request<OssStsTokenProps, Response<OssStsTokenProps>>('/thirdparty/oss/sts_token?public=1', 'GET')
    .then((res) => {
      console.log(res);
      const { data } = res;
      ossStsToken.value = data;
      return data;
    })
    .finally(() => (console.log("end")));
}

const uploadComplete = computed(() => {
  let image_urls = [];
  if(Object.keys(fileDict.value).length > 0){
    Object.keys(fileDict.value).forEach(function(key){
      const f = fileDict.value[key];
      if(f.url){
        image_urls.push(f.url);
      }
    });
  }  
  if (image_urls.length === imageUrls.value.length){
    return true
  }
  return false;
})

const beforeUpload: UploadProps['beforeUpload'] = async file => {
  const fileExtName = file.name.substring(file.name.lastIndexOf(".") + 1);
  const fileName = uuidv4() + "." + fileExtName;
  const oss_key = baseFilePath.value + '/' + fileName;

  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    message.error('单个图片最大1MB!');
    return false;
  }

  const fileData = {name: file.name, oss_key: oss_key, size: file.size, status: 0, url: ""};
  console.log(fileData);
  fileDict.value[file.uid] = fileData;
  return true;
}

const handleChange = async (info: UploadChangeParam) => {
  const status = info.file.status;
  console.log(info)
  console.log(fileDict.value)

  if(status === 'removed') {
    delete fileDict.value[info.file.uid];
  }
};

async function customRequest(info) {
  console.log(info);
  const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
  const client = new OSS({
    region: ossStsToken.value.region,
    accessKeyId: ossStsToken.value.access_key_id,
    accessKeySecret: ossStsToken.value.access_key_secret,
    stsToken: ossStsToken.value.security_token,
    bucket: ossStsToken.value.bucket_name,
    endpoint: ossStsToken.value.endpoint,
    useFetch: true,
    cname: use_cname,
    secure: true,
  });
  console.log(props.editChunkRecord)
  const oss_key = fileDict.value[info.file.uid]["oss_key"];
  if(info.file){
    console.log(oss_key)
    await client.multipartUpload(oss_key, info.file, {
      parallel: 4,
      partSize: 100 * 1024,
      progress: function (percent) {
        console.log("progress is: ", percent * 100);
        console.log(imageUrls.value)
        const fileIdx = imageUrls.value.findIndex(obj => obj.uid === info.file.uid);
        console.log(fileIdx);
        imageUrls.value[fileIdx].percent = percent * 100;
        if (percent === 1){
          imageUrls.value[fileIdx].status = "success";
        }
      },
    }).then(res => {
      console.log('结果:', res);
      fileDict.value[info.file.uid]["status"] = 1;
      fileDict.value[info.file.uid]["url"] = ossStsToken.value.endpoint + '/' + oss_key;
      fileDict.value = fileDict.value;

      let unUploadDoneList = [];
      if(Object.keys(fileDict.value).length > 0){
        Object.keys(fileDict.value).forEach(function(key){
          const f = fileDict.value[key];
          if(f.status !== 1){
            unUploadDoneList.push(f)
          }
        });
      }
    }).catch((err) => {
      console.log(err);
    });
  }
}

// 自定义上传列表项渲染
const itemRender: UploadProps['itemRender'] = (originNode, file) => {
  console.log(originNode)
  console.log(file)
  // return createVNode('div', null, [originNode.originNode]);
  return createVNode(
    'div',
    { class: 'custom-upload-item' },
    [
      originNode.originNode, // 保留默认的图片预览和删除功能
      createVNode(Input, {
        value: originNode.file.name,
        placeholder: '图片名称',
        class: 'custom-upload-input',
        onChange: (e: Event) => {
          originNode.file.name = (e.target as HTMLInputElement).value;
          fileDict.value[originNode.file.uid]["name"] = originNode.file.name;
        },
      }),
    ]
  );
};

onMounted(async () => {
  await getOssStsToken();
});
</script>

<style>
.custom-upload-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.custom-upload-input {
  margin-top: 2px; /* 输入框与图片的间距 */
  width: 104px;    /* 输入框宽度 */
}
</style>

<style lang="less" scoped>
/* 覆盖 Ant Design Vue 的默认样式 */
:deep(.ant-upload-list-picture-card-container) {
  display: inline-block;
  width: 104px;
  height: 134px;
  margin: 0 var(--margin-xs) var(--margin-xs) 0;
  vertical-align: top;
}

:deep(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail) {
  width: 86px !important;
  height: 88px !important;
}

/* 保持图片比例 */
:deep(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail img) {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持图片比例，裁剪多余部分 */
}
</style>