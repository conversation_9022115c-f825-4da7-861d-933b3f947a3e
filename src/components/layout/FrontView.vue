<script lang="ts" setup>
  import { LogoutOutlined } from '@ant-design/icons-vue';
  import { onMounted } from 'vue';
  import { ThemeProvider, alert } from 'stepin';

  const siteTitle = import.meta.env.VITE_SITE_NAME || "Sapientia";
  const siteLogo = "https://res.isapientia.com/img/tsinghua_logo.png";

  const customBgCss = siteTitle && siteTitle !== 'Sapientia';
  onMounted(() => {
    // alert.info(
    //   `<div class="text-text">
    //     Stepin is a fast, light framework to Vue3 – try it out today with the
    //     <span class="underline">Stepin Template Beta</span>.
    //   </div>`,
    //   { renderRaw: true, duration: -1 }
    // );
  });

  // const navList = [
  //   {
  //     title: '产品',
  //     children: [
  //       {
  //         // title: 'Stepin Template',
  //         list: ['ChatGPT', 'GPT4', '大模型'],
  //       },
  //       // {
  //       //   title: 'Stepin',
  //       //   list: ['Stepin Vue', 'Stepin React', 'Stepin Angular'],
  //       // },
  //     ],
  //   },
  //   {
  //     title: '集成',
  //     children: [
  //       {
  //         // title: 'Developers',
  //         list: ['概述', '文档', 'API', '接入示例'],
  //       },
  //     ],
  //   },
  //   {
  //     title: '大模型',
  //   },
  //   // {
  //   //   title: 'Business',
  //   //   children: [{ title: 'Business', list: ['Contact Us', 'Cooperation', 'Support'] }],
  //   // },
  //   {
  //     title: '关于我们',
  //   },
  // ];
  const navList = [];
</script>
<template>
  <ThemeProvider :color="{ middle: { 'bg-base': '#003f8c' }, primary: { DEFAULT: '#1896ff' } }">
    <div :class="`front-view flex flex-col ${customBgCss ? 'bg' : ''}`">
      <div class="text-text flex-1">
        <div class="front-header flex items-baseline py-md px-xl">
          <router-link to="/home" class="text-xxl text-text hover:text-text">
            <!-- <img :src="siteIcon" v-if="customBgCss"/> -->
            <div v-if="!customBgCss">
              <img src="@/assets/vite.svg"/>
              {{ siteTitle }}
            </div>
            <div v-if="customBgCss" style="display: flex; justify-content: center; align-items: center;">
              <!-- <img :src="siteLogo" style="height: 64px;width: 64px;"/> -->
              <span style="font-size: larger;padding-left: 8px;">{{ siteTitle }}</span>
            </div>
          </router-link>
          <div
            style="width: calc(100% - 430px)"
            class="front-navigation mx-xl flex overflow-hidden items-center text-lg overflow-ellipsis whitespace-nowrap"
          >
            <div
              :class="`front-nav-item flex items-center cursor-pointer mx-base ${nav.children ? 'with-list' : ''}`"
              v-for="nav in navList"
            >
              <template v-if="!nav.children">
                {{ nav.title }}
              </template>
              <a-popover :mouseEnterDelay="0.1" v-else placement="bottom">
                <div class="front-nav-item-content">
                  {{ nav.title }}
                </div>
                <template #content>
                  <div class="flex">
                    <div class="not-[:first-child]:ml-lg" v-for="group in nav.children">
                      <h3>{{ group.title }}</h3>
                      <div
                        class="cursor-pointer hover:text-text text-subtext font-light py-xs text-lg"
                        v-for="item in group.list"
                      >
                        {{ item }}
                      </div>
                    </div>
                  </div>
                </template>
              </a-popover>
            </div>
          </div>
          <!-- <div :style="`${customBgCss ? 'width: 100px;' : ''}`"> -->
          <div style="width: 100px;">
            <router-link
              to="/login"
              class="h-[46px] border-transparent hover:text-text hover:border-transparent text-lg text-text"
            >
              <LogoutOutlined class="mr-xs" />
              登录
            </router-link>
            <!-- <a-button
              class="ml-md px-lg border-text hover:border-text hover:bg-text border-2 h-[46px] hover:text-bg-container"
              size="large"
              >注册</a-button
            > -->
          </div>
        </div>
        <div class="front-content px-xl">
          <router-view />
        </div>
      </div>
    </div>
  </ThemeProvider>
</template>
<style lang="less" scoped>
  .front-view {
    .front-header {
      .front-nav-item {
        &.with-list .front-nav-item-content {
          &:after {
            content: '';
            @apply ~"h-[8px]" ~"w-[8px]" transition-transform ml-2 inline-block border-text border-l-0 border-t-0 border-r-2 border-b-2 border-solid ~"rotate-[-135deg]" translate-y-1/4;
          }
          &:hover {
            &:after {
              @apply ~"rotate-[45deg]" translate-y-0;
            }
          }
        }
      }
    }
    .front-content {
      min-height: calc(100vh - 78px);
    }
  }

  // .bg {
  //   background-image: url('https://res.isapientia.com/img/tsinghua_icenter_bg1.jpg');
  //   background-repeat: no-repeat;
  //   background-position: center;
  //   background-attachment: fixed;
  //   background-size: cover;
  //   -webkit-background-size: cover;
  //   -moz-background-size: cover;
  //   -o-background-size: cover;
  // }

  .bg {
    box-sizing: border-box;
    font-size: 22px;
    overflow: hidden;
    background: no-repeat center top / 100% 100%;
    background-image: linear-gradient(to top, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0)), url(https://res.isapientia.com/img/qinghuayuan1.png)
  }
</style>
