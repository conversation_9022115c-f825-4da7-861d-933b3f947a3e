<template>
  <div>
    <!-- Debug indicator to confirm component is loaded -->
    <div v-if="isDevelopment" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 5px; font-size: 12px; z-index: 9999; border-radius: 4px;">
      Stagewise: {{ isDevelopment ? 'LOADED' : 'NOT LOADED' }}
    </div>
    
    <!-- Stagewise toolbar component -->
    <StagewiseToolbar v-if="isDevelopment" :config="config" />
  </div>
</template>

<script setup lang="ts">
import { StagewiseToolbar, type ToolbarConfig } from '@stagewise/toolbar-vue';
import { onMounted } from 'vue';

// Only show in development mode
const isDevelopment = false && import.meta.env.DEV;

// Basic configuration with empty plugins array
const config: ToolbarConfig = {
  plugins: []
};

// Debug logging
onMounted(() => {
  console.log('🚀 Stagewise component mounted:', {
    isDevelopment,
    mode: import.meta.env.MODE,
    config
  });
  
  if (isDevelopment) {
    console.log('✅ Stagewise toolbar should be visible in development mode');
  } else {
    console.log('❌ Stagewise toolbar hidden in production mode');
  }
});
</script>