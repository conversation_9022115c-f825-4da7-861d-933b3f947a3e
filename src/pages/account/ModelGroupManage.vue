<template>
  <a-drawer 
    title="模型分组"
    :closable="true" 
    :width="900"
    :visible="modelGroupDrawer" 
    @close="setVisible(false)"
  >
    <a-table 
      size="small"
      :columns="columns"
      :dataSource="groupModelTableList"
      :pagination="false"
      >
      <template #title>
        <div class="text-subtext text-sm flex justify-between items-center">
          <span style="font-size:14px;"></span>
          <a-button style="margin-right: 5px;" type="primary" @click="addGroupModal">
            <PlusOutlined class="text-lg" />新增
          </a-button>
        </div>
      </template>
      <template #headerCell="{ column }">
        <template v-if="column.key === 'secret_key'">
          秘钥
          <a-popover title="" trigger="hover" placement="right">
            <template #content>
              微信群助手账号与平台的通信凭证
            </template>
            <QuestionCircleOutlined />
          </a-popover>
        </template>
      </template>
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'create_time'">
          {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
        </template>
        <template v-else-if="column.dataIndex === 'allocate_model'">
          <template v-for="(model, index) in record.models" :key="tag">
            <a-tag color="blue">{{ model.name }}</a-tag>
          </template>
          <span v-if="record.models.length === 0">尚未分配</span>
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <a style="font-weight: normal;" @click="editGroup(record)">修改</a>
          <a-divider type="vertical"/>
          <a style="font-weight: normal;" @click="changeAllocateModel(record)">分配模型</a>
        </template>
      </template>
    </a-table>
  </a-drawer>
  <a-modal :okButtonProps="{ loading }" width="540px" v-model:visible="showForm" :title="title" @ok="submit" 
    ok-text="确认" cancel-text="取消" 
    style="right: 20px"
  >
    <a-form ref="form" :model="formData" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16, offset: 1 }">
      <a-form-item required name="name" label="名称"
        :rules="[{ required: true, message: '请输入名称' }]"
      >
        <a-input v-model:value="formData.name" placeholder="请输入名称" show-count :maxlength="50" />
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal :okButtonProps="{ loading }" width="740px" v-model:visible="showAllocateModelForm" :title="allocateTitle" 
    @ok="saveAllocateModel" 
    ok-text="确认" cancel-text="取消" 
  >
    <a-transfer
      v-model:target-keys="targetKeys"
      :data-source="modelDataList"
      :titles="['待分配', '']"
      :select-all-labels="selectAllLabels"
      :list-style="{
        width: '350px',
        height: '550px',
      }"
      @change="allocateModelSelectChange"
    >
      <template #render="item">
        <a-button type="text">{{ item.title }}</a-button>
      </template>
    </a-transfer>
  </a-modal>
</template>
<script lang="ts" setup>
import { reactive, watch, onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import {
  saveModelGroup,
  groupAllocateModel,
} from "@/api/account";

import {
  getProviderModelList
} from "@/api/apikeys";

import type { SelectAllLabel } from 'ant-design-vue/es/transfer';

const selectAllLabels: SelectAllLabel[] = [
  ({ selectedCount, totalCount }) => `${selectedCount}/${totalCount}`,
  '已分配',
];


const showForm = ref(false);
const title = ref("新增");

const showAllocateModelForm = ref(false);
const allocateTitle = ref("");

const modelList = ref([]);
const modelDataList = ref<ModelData[]>([]);
const targetKeys = ref<string[]>([]);

const currentGroupId = ref(null);
const selectedAllocateModelIds = ref([]);

const formData = reactive({
  id: undefined,
  name: '',
});

const loading = ref(false);

const props = defineProps({
  modelGroupDrawer: Boolean,
  groupModelTableList: Array,
});

const emit = defineEmits(['setModelGroupVisible', 'getModelGroupTableList'])

const setVisible = (visible: boolean) => {
  console.log(visible);
  emit('setModelGroupVisible', visible);
};

function getModelGroupTableList(){
  emit('getModelGroupTableList'); 
}

function addGroupModal(){
  showForm.value = true;
  title.value = "新增";
  formData.id=undefined;
  formData.name = '';
}

function editGroup(record){
  title.value = "修改";
  formData.id = record.id;
  formData.name = record.name;
  showForm.value = true;
}

function submit() {
  loading.value = true;
  form.value
    ?.validate()
    .then(() => {
      saveModelGroup(formData).then((res) => {
        const { code, data } = res;
        console.log(data);
        if (code === 0){
          showForm.value = false;
          getModelGroupTableList();
        }
        return data;
      });
    })
    .finally(() => {
      loading.value = false; 
    });
}

const form = ref<FormInstance>();

const columns = [
  {
    title: '分组名称',
    dataIndex: 'name',
    key: 'name',
    width: 140,
    ellipsis: true,
  },
  {
    title: '已分配模型',
    dataIndex: 'allocate_model',
    key: 'allocate_model',
  },
  {
    title: '创建时间',
    key: 'create_time',
    dataIndex: 'create_time',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 120,
  },
];

function changeAllocateModel(record){
  showAllocateModelForm.value = true;
  allocateTitle.value = `${record.name} - 分配模型`;
  currentGroupId.value = record.id;
  selectedAllocateModelIds.value = [];

  const allocatedModelIds = record.models.map(item => {
    return item.id.toString();
  });

  modelDataList.value = modelList.value.map(item => {
    return {
      key: item.id.toString(),
      title: item.name,
      description: item.introduce,
      chosen: Math.random() * 2 > 1,
    }
  });
  targetKeys.value = modelList.value.filter(
    item => allocatedModelIds.includes(item.id.toString())
  ).map(item => {
    return item.id.toString();
  });
}

function saveAllocateModel(){
  // if(selectedAllocateModelIds.value.length === 0){
  //   showAllocateModelForm.value = false;
  //   return;
  // }
  const params = {
    group_id: currentGroupId.value, 
    model_ids: selectedAllocateModelIds.value
  }
  groupAllocateModel(params).then((res) => {
    const { data } = res;
    console.log(data);
    showAllocateModelForm.value = false;
    getModelGroupTableList();
    return data;
  });
}

interface ModelData {
  key: string;
  title: string;
  description: string;
  chosen: boolean;
}

const allocateModelSelectChange = (keys: string[], direction: string, moveKeys: string[]) => {
  console.log(keys, direction, moveKeys);
  selectedAllocateModelIds.value = keys;
};

function initProviderModelList(){
  getProviderModelList().then((res) => {
    const { data } = res;
    console.log(data);
    modelList.value = data;
    return data;
  });
}

onMounted(() => {
  getModelGroupTableList();
  initProviderModelList();
})
</script>
<style>
.avatar-uploader > .ant-upload {
  width: 248px;
  height: 248px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.sound{
  width: 12px;
  height: 16px;
}

.video-player {
  max-width: 600px;
  /* margin: 0 auto; */
}

.video-js {
  width: 100%;
  height: auto;
}

.doc-item {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 300px;
  height: 60px;
  margin-right: 10px;
  border-radius: 10px;
  background-color: #1890ff;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
}
.doc-icon {
  width: 30px;
  margin-right: 10px;
  img {
    width: 30px;
    object-fit: contain;
  }
}
.location-icon{
  width: 30px;
  img {
    width: 30px;
    object-fit: contain;
  }
}

.doc-name {
  color: #fff;
  font-size: 13px;
  font-weight: 600;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
}
.doc-size {
  color: #fff;
  font-size: 12px;
  font-weight: 400;
}
</style>