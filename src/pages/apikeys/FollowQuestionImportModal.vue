<template>
<a-modal
    :visible="modalVisible"
    title="批量导入"
    width="1200px"
    style="top: 0px;"
    :footer="null"
    @cancel="setModalVisible(false)"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <div>
      <a-steps :current="importUserCurrent">
        <a-step v-for="item in importUserSteps" :key="item.title" :title="item.title" />
      </a-steps>
      <div v-if="importUserCurrent === 0">
        <a-upload-dragger 
          style="margin-top: 16px;" 
          accept=".xls,.xlsx"
          :showUploadList="false"
          :multiple="false"
          :before-upload="handleImportUserBeforeUpload"
        >
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">将Excel文件拖到此处或点击上传</p>
          <p class="ant-upload-hint">单次导入的文件大小应小于10MB</p>
        </a-upload-dragger>
        <a target="blank" href="https://res.isapientia.com/tpl/%E5%9B%9E%E5%BD%92%E6%B5%8B%E8%AF%95%E9%97%AE%E9%A2%98%E6%A8%A1%E6%9D%BF.xlsx">下载Excel数据模板</a>
      </div>
      <div v-if="importUserCurrent === 1">
        <a-table 
          style="margin-top: 16px;"
          bordered
          :data-source="importUserTableData" 
          :columns="importUserTableColumns"
          :pagination="false"
          size="small"
          :scroll="{x: 1200, y: 400}"
        >
        <template #title>
          <div class="text-subtext text-sm flex justify-between items-center">
            <span style="font-size:14px;">共 {{ importUserTableData.length }} 条记录</span>
          </div>
        </template>
        <template #bodyCell="{ column, text, record }">
          <!-- <template v-if="['username', 'email', 'nickname'].includes(column.dataIndex)"> -->
          <template v-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <span v-if="editableData[record.key]">
                <a-typography-link @click="save(record.key)">保存</a-typography-link>
                &nbsp;
                <a-typography-link @click="cancel(record.key)">取消</a-typography-link>
              </span>
              <span v-else>
                <a-typography-link @click="edit(record.key)">修改</a-typography-link>
                &nbsp;
                <a-popconfirm title="确认要删除吗?" @confirm="remove(record.key)">
                  <a-typography-link style="color: red;">删除</a-typography-link>
                </a-popconfirm>
              </span>
            </div>
          </template>
          <template v-else>
            <div>
              <a-input
                v-if="column.dataIndex != 'key' && editableData[record.key]"
                v-model:value="editableData[record.key][column.dataIndex]"
                style="margin: -5px 0"
              />
              <template v-else>
                {{ text }}
              </template>
            </div>
          </template>
        </template>
        </a-table>
      </div>
      <div v-if="importUserCurrent === 2">
        <a-result
          status="success"
          title="导入结果"
          :sub-title="`总共${importUserTableData.length}条记录，成功导入${importUserSuccessCount}条，失败${importUserFailedResult.length}条`"
        >
          <template #extra>
            <a-button type="primary" @click="continueImport">继续导入</a-button>
            <a-button @click="setModalVisible(false)">关闭</a-button>
          </template>
          <div v-if="importUserFailedResult.length > 0" style="max-height: 200px; overflow: auto;">
            <p style="font-size: 16px">
              <strong>导入失败记录的原因:</strong>
            </p>
            <template v-for="(failed_reason, i) in importUserFailedResult" :key="i">
              <p>
                <close-circle-outlined :style="{ color: 'red' }" />
                {{ failed_reason }}
              </p>
            </template>
          </div>
        </a-result>
      </div>
      <div class="steps-action">
        <a-button v-if="importUserCurrent === 0" 
          type="primary" 
          :disabled="importUserTableData.length === 0"
          @click="importUserNext">下一步</a-button>
        <div v-if="importUserCurrent === 1" >
          <a-button 
            type="primary" 
            :loading="loading"
            :disabled="importUserTableData.length === 0"
            @click="batchImportFollowQuestion">导入</a-button>  
          <a-button style="margin-left: 8px" @click="importUserPrev">重新上传</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { watch, reactive, ref } from 'vue';
import { importFollowQuestion } from "@/api/release";
import { importExcelData } from '@/utils/importExcel'
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

const props = defineProps({
  modalVisible: Boolean,
  source: Number,
  biz_id: Number,
});

const importUserCurrent = ref<number>(0);
const importUserSuccessCount = ref<number>(0);
const importUserFailedResult = ref([]);
const importUserTableData = ref([]);

const importUserSteps = [
  {
    title: '上传文件',
    content: '上传文件',
  },
  {
    title: '解析结果',
    content: '解析结果',
  },
  {
    title: '导入结果',
    content: '导入结果',
  },
];

const importUserTableColumns = ref([]);

const emit = defineEmits(['setFollowQuestionImportModalVisible'])

function continueImport(){
  importUserCurrent.value = 0;
  importUserTableData.value = [];
}

const setModalVisible = (visible: boolean) => {
  emit('setFollowQuestionImportModalVisible', visible);
};

const loading = ref(false);

const handleImportUserBeforeUpload = (file) => {
  // 数据处理excel=>json
  importExcelData(file).then((res) => {
    let head = {
      '问题': 'question',
    }
    const arr = res.map(item => {
      const obj = {}
      console.log(item);
      obj['key'] = item["__rowNum__"] + 1
      for (const k in item) {
        if(k in head){
          obj[head[k]] = String(item[k])
        }
      } 
      return obj;
    });
    console.log(arr);
    if (arr.length === 0){
      message.error("当前文件没有用于导入的数据");
    }else{
      let tmp = [
        {
          title: '行号',
          width: 70,
          dataIndex: 'key',
          key: 'key',
          fixed: 'left',
        },
        {
          title: '问题',
          dataIndex: 'question',
          key: 'question',
          fixed: 'left',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          width: 100,
          fixed: 'right',
        },
      ]
      importUserTableColumns.value = tmp;
      importUserTableData.value = arr;
      importUserNext();
    }
  });
  return false;
};

const editableData: UnwrapRef<Record<string, DataItem>> = reactive({});

const edit = (key: string) => {
  editableData[key] = cloneDeep(importUserTableData.value.filter(item => key === item.key)[0]);
};

const remove = (rownumber: string) => {
  const idx = importUserTableData.value.findIndex(({ key }) => key !== undefined && key === rownumber);
  importUserTableData.value.splice(idx, 1)
}

const save = (key: string) => {
  Object.assign(importUserTableData.value.filter(item => key === item.key)[0], editableData[key]);
  delete editableData[key];
};

const cancel = (key: string) => {
  delete editableData[key];
};

const importUserNext = () => {
  importUserCurrent.value++;
};
const importUserPrev = () => {
  importUserCurrent.value--;
};

const batchImportFollowQuestion = () =>{
  loading.value = true;
  const questions = importUserTableData.value.map(item => item.question);
  importFollowQuestion({
    source: props.source,
    biz_id: props.biz_id,
    questions: questions,
  }).then((res) => {
    const { code, data } = res;
    if (code === 0){
      const { success, failed } = data;
      importUserSuccessCount.value = success;
      importUserFailedResult.value = failed;
      importUserNext();
    }
  }).finally(() => {
    loading.value = false;
  });
}
</script>
<style scoped>
.limit-popover-width {
  max-width: 50%;
  word-wrap: break-word;
}

.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 16px;
}

.steps-action {
  margin-top: 24px;
  text-align: center;
}

[data-theme='dark'] .steps-content {
  background-color: #2f2f2f;
  border: 1px dashed #404040;
}
</style>