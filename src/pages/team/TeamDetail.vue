<template>
  <div class="team-detail">
    <a-card :bordered="false" v-if="team">
      <template #title>
        <div class="flex justify-between items-center">
          <div class="team-info">
            <span class="team-name">{{ team.name }}</span>
            <div class="team-description-wrapper" v-if="team.description">
              <div class="team-description-full" v-if="isDescriptionExpanded">{{ team.description }}</div>
              <div class="team-description" v-else>{{ truncatedDescription }}</div>
              <a-button v-if="shouldShowExpandButton" type="link" size="small" @click="toggleDescription">
                {{ isDescriptionExpanded ? '收起' : '展开' }}
              </a-button>
            </div>
          </div>
          <a-button @click="goBack" style="margin-top: -30px;">返回列表</a-button>
        </div>
      </template>

      <!-- 标签页容器 -->
      <a-tabs class="mt-4" style="margin-top: -20px;">
        <!-- 团队成员标签页 -->
        <a-tab-pane key="members" tab="团队成员">
          <div class="flex justify-between items-center mb-4">
            <h3>成员列表</h3>
            <a-button type="primary" @click="showAddMemberModal" v-if="isAdmin">添加成员</a-button>
          </div>
          <a-table
            :loading="membersLoading"
            :columns="memberColumns"
            :data-source="teamMembers"
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'is_superuser'">
                <a-tag :color="record.is_superuser ? 'red' : 'blue'">
                  {{ record.is_superuser ? '管理员' : '成员' }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <div class="action-btns">
                  <a-button 
                    type="link" 
                    danger 
                    @click="confirmDeleteMember(record)"
                    v-if="isAdmin"
                  >
                    <template #icon><delete-outlined /></template>
                  </a-button>
                </div>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 工作流标签页 -->
        <a-tab-pane key="workflows" tab="工作流">
          <div class="flex justify-between items-center mb-4">
            <div class="flex items-center">
              <h3>工作流列表</h3>
              <!-- <div v-if="isAdmin" class="ml-4">
                <a-radio-group v-model:value="fabuRadioValue" style="margin-right: 10px;">
                  <a-radio-button value="1">我创建的</a-radio-button>
                  <a-radio-button value="2">其他创建</a-radio-button>
                </a-radio-group>
              </div> -->
            </div>
            <div>
              <a-button type="primary" @click="createWorkflow('workflow-chat')">创建工作流编排对话</a-button>
              <a-button type="primary" @click="createWorkflow('workflow')" style="margin-left: 10px;">创建工作流</a-button>
            </div>
          </div>
          <a-table
            :loading="workflowsLoading"
            :columns="workflowColumns"
            :data-source="teamWorkflows"
            :pagination="{ showSizeChanger: true, showTotal: (total) => `共 ${total} 条` }"
            row-key="id"
            v-if="!isAdmin || (isAdmin && fabuRadioValue === '1')"
          >
            <template #bodyCell="{text, column, record }">
              <template v-if="column.dataIndex === 'update_time'">
                {{ text?dayjs(text).format("YYYY-MM-DD HH:mm"):'-' }}
              </template>
              <template v-if="column.key === 'status'">
                <a-tag :color="record.lock_status === true ? 'orange' : 'green'">
                  {{ record.lock_status === true ? '锁定中' : '未锁定' }}
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'mode'">
                {{ text === 2 ? '对话服务(工作流编排)' : text === 3 ? '工作流' : text === 1 ? '对话服务(基础编排)' : '-' }}
              </template>
              <template v-if="column.key === 'action'">
                <div class="action-btns">
                  <a-button type="link" @click="navigateToWorkflow(record)">编排</a-button>
                  <a-divider type="vertical"/>
                  <a-button type="link" @click="copyWorkflow(record)">复制</a-button>
                  <a-divider type="vertical"/>
                  <a-button type="link" @click="showWorkflowLogDrawer(record)">日志</a-button>
                  <a-divider type="vertical"/>
                  <a-upload
                    accept=".yml"
                    :before-upload="beforeUpload"
                    :show-upload-list="false"
                  >
                    <a-button type="link" @click="selectImportFile(record)">导入</a-button>
                  </a-upload>
                  <a-divider type="vertical"/>
                  <a-button type="link" @click="exportWorkflow(record)">导出</a-button>
                  <a-divider type="vertical"/>
                  <a-button 
                    type="link" 
                    danger 
                    @click="showDeleteConfirm(record)"
                    v-if="record.creator === accountStore.account.id || isAdmin"
                  >删除</a-button>
                </div>
              </template>
            </template>
          </a-table>
          
          <!-- 其他人创建的工作流表格 -->
          <a-table
            :loading="workflowsLoading"
            :columns="otherWorkflowColumns"
            :data-source="otherTeamWorkflows"
            :pagination="{ showSizeChanger: true, showTotal: (total) => `共 ${total} 条` }"
            row-key="id"
            v-if="isAdmin && fabuRadioValue === '2'"
          >
            <template #bodyCell="{text, column, record }">
              <template v-if="column.dataIndex === 'update_time'">
                {{ text?dayjs(text).format("YYYY-MM-DD HH:mm"):'-' }}
              </template>
              <template v-if="column.key === 'status'">
                <a-tag :color="record.lock_status === true ? 'orange' : 'green'">
                  {{ record.lock_status === true ? '锁定中' : '未锁定' }}
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'mode'">
                {{ text === 2 ? '对话服务(工作流编排)' : text === 3 ? '工作流' : text === 1 ? '对话服务(基础编排)' : '-' }}
              </template>
              <template v-if="column.key === 'action'">
                <div class="action-btns">
                  <a-button type="link" @click="navigateToWorkflow(record)">编排</a-button>
                  <a-divider type="vertical"/>
                  <a-button type="link" @click="copyWorkflow(record)">复制</a-button>
                  <a-divider type="vertical"/>
                  <a-button type="link" @click="showWorkflowLogDrawer(record)">日志</a-button>
                  <a-divider type="vertical"/>
                  <a-upload
                    accept=".yml"
                    :before-upload="beforeUpload"
                    :show-upload-list="false"
                  >
                    <a-button type="link" @click="selectImportFile(record)">导入</a-button>
                  </a-upload>
                  <a-divider type="vertical"/>
                  <a-button type="link" @click="exportWorkflow(record)">导出</a-button>
                  <a-divider type="vertical"/>
                  <a-button 
                    type="link" 
                    danger 
                    @click="showDeleteConfirm(record)"
                    v-if="record.creator === accountStore.account.id || isAdmin"
                  >清空</a-button>
                </div>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 知识库标签页 -->
        <a-tab-pane key="knowledgebases" tab="知识库">
          <div class="flex justify-between items-center mb-4">
            <h3>知识库列表</h3>
            <a-button type="primary" @click="createKnowledgeBase"><PlusOutlined />创建知识库</a-button>
          </div>
          <a-table
            :loading="knowledgeBasesLoading"
            :columns="knowledgeBaseColumns"
            :data-source="teamKnowledgeBases"
            :pagination="{ showSizeChanger: true, showTotal: (total) => `共 ${total} 条` }"
            row-key="id"
          >
            <template #bodyCell="{ text, column, record }">
              <template v-if="column.dataIndex === 'last_process_time'">
                {{ text ? dayjs(text).format("YYYY-MM-DD HH:mm") : '-' }}
              </template>
              <template v-if="column.dataIndex === 'creator'">
                {{ record.create_account ? record.create_account.name : '-' }}
              </template>
              <template v-if="column.dataIndex === 'total'">
                <a @click="setDocumentModalVisible(record.id, record.namespace, true, '2')">{{ text || 0 }}</a>
              </template>
              <template v-if="column.dataIndex === 'dataset_total'">
                <a @click="setDocumentModalVisible(record.id, record.namespace, true, '5')">{{ text || 0 }}</a>
              </template>
              <template v-if="column.dataIndex === 'share_scope'">
                <a-tag :color="record.share_scope === 0 ? 'blue' : (record.share_scope === 1 ? 'green' : 'orange')">
                  {{ record.share_scope === 0 ? '仅自己' : (record.share_scope === 1 ? '全员' : '学期课程') }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <div class="action-btns">
                  <a-button 
                    type="link" 
                    @click="setDocumentModalVisible(record.id, record.namespace, true, '1')"
                  >上传文档</a-button>
                  <a-divider type="vertical"/>
                  <a-button 
                    type="link" 
                    @click="editKnowledgeBase(record)"
                  >编辑</a-button>
                  <a-divider type="vertical"/>
                  <a-button 
                    type="link" 
                    danger 
                    @click="confirmDeleteKnowledgeBase(record)"
                  >{{ record.share_scope == 3 ? '取消共享' : '删除' }}</a-button>
                </div>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 添加成员模态框 -->
    <a-modal
      v-model:visible="memberModalVisible"
      title="添加成员"
      ok-text="确认" cancel-text="取消"
      @ok="addMembers"
      :confirmLoading="savingMembers"
    >
      <a-form :model="memberForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="团队成员" name="account_ids">
          <a-select
            v-model:value="memberForm.account_ids"
            mode="multiple"
            placeholder="请选择团队成员"
            :options="userOptions"
            :loading="usersLoading"
            :filter-option="filterOption"
            show-search
          ></a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 创建工作流模态框 - 复用AppRelease工作流创建 -->
    <a-modal
      :title="appModalTitle"
      :visible="showCreateAppVisible"
      :okButtonProps="{ loading }"
      width="540px"
      ok-text="确认" cancel-text="取消"
      @ok="submit"
      @cancel="showCreateAppVisible = false"
    >
      <div>
        <a-form
          ref="form"
          :model="formData"
          :labelCol="{ span: 5 }"
          :wrapperCol="{ span: 16, offset: 1 }"
        >
          <a-form-item required name="name" label="工作流名称"
            :rules="[{ required: true, message: '请输入工作流名称' }]"
          >
            <a-input v-model:value="formData.name" placeholder="工作流名称" show-count :maxlength="50" />
          </a-form-item>
          <a-form-item required name="apikey_id" label="选择Apikey"
            :rules="[{ required: true, message: '请选择要使用的Apikey' }]"
          >
            <a-select
              placeholder="选择要使用的Apikey"
              v-model:value="formData.apikey_id"
              :options="apikeyList.map(item => {
                  return {
                    label: `${item.name}`,
                    value: `${item.id}`,
                  }
                })"
            ></a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 创建知识库模态框 -->
    <a-modal
      v-model:visible="knowledgeBaseModalVisible"
      title="设置知识库"
      ok-text="确认" cancel-text="取消"
      @ok="saveKnowledgeBase"
      :confirmLoading="savingKnowledgeBase"
      width="540px"
    >
      <a-form :model="knowledgeBaseForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item
          label="知识库名称"
          name="namespace"
          :rules="[{ required: true, message: '请输入知识库名称' }]"
        >
          <a-input v-model:value="knowledgeBaseForm.namespace" placeholder="请输入知识库名称" show-count :maxlength="100" />
        </a-form-item>
        <a-form-item
          label="知识库描述" name="description"
          :rules="[{ required: true, message: '请输入知识库描述' }]"
        >
          <a-textarea
            v-model:value="knowledgeBaseForm.description"
            placeholder="请输入知识库描述"
            show-count
            :maxlength="200"
            :rows="4"
          />
        </a-form-item>
        <a-form-item name="bg" label="封面">
          <a-upload 
            list-type="picture-card" 
            accept=".png,.jpg,.jpeg,.webp,.gif"
            :fileList="knowledgeBaseFileList"
            :show-upload-list="false"
            :customRequest="customRequest"
            :before-upload="knowledgeBaseBeforeUpload"
            @change="handleChange"
          >
            <div>
              <img v-if="knowledgeBaseForm.bg" :src="knowledgeBaseForm.bg" alt="avatar" style="width: 100px;height: 100px;"/>
              <div v-else>
                <loading-outlined v-if="uploadLoading"></loading-outlined>
                <plus-outlined v-else></plus-outlined>
                <div class="ant-upload-text">上传</div>
              </div>
            </div>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 工作流日志抽屉 -->
    <a-drawer :title="`日志记录`"
      :closable="false"
      width="90%"
      :visible="showLogVisible"
      :destroyOnClose="true"
      @close="changeShowLogVisible(false)"
      :bodyStyle="{ 'overflow-y': 'hidden' }"
    >
      <template #extra>
        <a-button type="text" @click="changeShowLogVisible(false)"><close-outlined /></a-button>
      </template>
      <iframe
        id="workflowLog"
        :src="workflowRunLogUrl"
        allow="clipboard-read; clipboard-write; microphone *; autoplay;"
        style="width: 100%; height: 100%; border: none;"
      ></iframe>
    </a-drawer>

    <!-- 复制工作流模态框 -->
    <a-modal
      title="复制工作流"
      :visible="showCopyWorkflowVisible"
      :okButtonProps="{ loading: copyingWorkflow }"
      width="540px"
      ok-text="确认" cancel-text="取消"
      @ok="workflowCopySubmit"
      @cancel="showCopyWorkflowVisible = false"
    >
      <div>
        <a-form
          ref="formCopy"
          :model="workflowCopyForm"
          :labelCol="{ span: 5 }"
          :wrapperCol="{ span: 16, offset: 1 }"
        >
          <a-form-item required name="name" label="工作流名称"
            :rules="[{ required: true, message: '请输入工作流名称' }]"
          >
            <a-input v-model:value="workflowCopyForm.name" placeholder="工作流名称" show-count :maxlength="50" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 知识库文档管理模态框 -->
    <DocumentManageModal 
      :documentModalVisible="documentModalVisible" 
      :knowledegBaseId="knowledgeBaseId"
      :knowledgeBaseNamespace="knowledgeBaseNamespace"
      :tabActiveKey="String(tabActiveKey)"
      :kbShareTab="kbShareTab"
      @setDocumentModalVisible="setDocumentModalVisible"
      v-if="knowledgeBaseId > 0"
    />

    <!-- 工作流编辑模态框 -->
    <ReleaseModal
      :formData="editWorkflowRecord"
      :showFormModalVisible="showWorkflowModalVisible"
      :fabuRadioValue="fabuRadioValue"
      :isWorkflowMode="isWorkflowMode"
      :is_team_workflow="true"
      @setEditRecord="setEditWorkflowRecord"
      @setShowFormModalVisible="setWorkflowModalVisible"
      v-if="showWorkflowModalVisible"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message, Modal, FormInstance } from 'ant-design-vue';
import { DeleteOutlined, CloseOutlined, ExclamationCircleOutlined, PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { 
  getTeamDetail,
  getTeamMembers,
  addTeamMembers,
  deleteTeamMembers,
  getTeamWorkflows,
  createTeamWorkflow,
  getTeamKnowledgeBases,
  createTeamKnowledgeBase,
  deleteTeamKnowledgeBase
} from '@/api/team';
import { 
  getAccountPage,
  getAccountInfo 
} from '@/api/account';
import { 
  saveRelease,
  copyReleaseApp,
  exportReleaseWorkflow,
  importReleaseWorkflow,
  deleteRelease,
  postReleaseLock 
} from '@/api/release';
import { useApikeyStore } from '@/store/apikey';
import { storeToRefs } from 'pinia';
import { createVNode } from 'vue';
import http from '@/store/http';
import { v4 as uuidv4 } from 'uuid';
import DocumentManageModal from '../knowledge_base/DocumentManage.vue';
import { useAccountStore } from '@/store';
import ReleaseModal from '../apikeys/ReleaseModal.vue';
import OSS from 'ali-oss';


const router = useRouter();
const route = useRoute();
const teamId = ref(route.params.id as string);

// 用户权限相关
const accountStore = useAccountStore();
const isAdmin = computed(() => accountStore.account.is_superuser === true);

// 数据加载状态
const loading = ref(false);
const membersLoading = ref(false);
const workflowsLoading = ref(false);
const knowledgeBasesLoading = ref(false);
const usersLoading = ref(false);
const copyingWorkflow = ref(false);

// 模态框显示状态
const memberModalVisible = ref(false);
const showCreateAppVisible = ref(false);
const knowledgeBaseModalVisible = ref(false);
const showLogVisible = ref(false);
const showCopyWorkflowVisible = ref(false);
const showWorkflowModalVisible = ref(false);

// 保存操作的加载状态
const savingMembers = ref(false);
const savingWorkflow = ref(false);
const savingKnowledgeBase = ref(false);

// 用于工作流编辑模态框
const fabuRadioValue = ref('1'); // 默认显示"我创建的"

// 监听 fabuRadioValue 变化
watch(fabuRadioValue, (val) => {
  fetchTeamWorkflows(); // 当切换筛选项时重新获取工作流列表
});

// 团队数据
const team = ref<any | null>(null);
const teamMembers = ref<any[]>([]);
const teamWorkflows = ref<any[]>([]);
const otherTeamWorkflows = ref<any[]>([]); // 其他人创建的工作流
const teamKnowledgeBases = ref<any[]>([]);

// 用户选择相关
const userOptions = ref<{ label: string; value: string }[]>([]);

// 成员表单
const memberForm = reactive<{
  account_ids: string[];
}>({
  account_ids: [],
});

// 创建工作流相关
const form = ref<FormInstance>();
const formCopy = ref<FormInstance>();
const apikeyStore = useApikeyStore();
const { apikeyList } = storeToRefs(apikeyStore);

// 工作流表单
const formData = reactive({
  'id': 0,
  'name': '',
  'mode': 3, // 工作流模式
  'appMode': '2',
  'team_id': '', // 团队ID
  'apikey_id': null,
});

// 知识库表单
const knowledgeBaseForm = reactive<{
  id?: number;
  team_id: string;
  namespace: string;
  description?: string;
  bg?: string;
  share_scope?: string;
}>({
  team_id: teamId.value,
  namespace: '',
  description: '',
  bg: '',
  share_scope: '0',
});

// 工作流相关状态
const currImportWorkflowId = ref(0);
const currWorkflowId = ref('');
const workflowCopyForm = reactive({
  id: '',
  name: '',
});
const appModalTitle = ref("创建工作流");

// 文件内容
const fileContent = ref();

// 知识库上传相关
const knowledgeBaseFileList = ref([]);
const uploadLoading = ref(false);
const baseFilePath = ref<string>("file/knowledge/base/cover");
const ossStsToken = ref<any>();
const fileDict = ref({});

// 知识库文档管理相关
const documentModalVisible = ref(false);
const knowledgeBaseId = ref(0);
const knowledgeBaseNamespace = ref('');
const tabActiveKey = ref('2'); // 确保是字符串类型
const kbShareTab = ref('0'); // 默认共享标签

// 描述展开/收起相关状态
const isDescriptionExpanded = ref(false);

// 当前选中的工作流记录
interface WorkflowRecord {
  id?: number;
  name?: string;
  mode?: number;
  d_app_id?: string;
  [key: string]: any;
}

const editWorkflowRecord = ref<WorkflowRecord>({});

// 计算工作流是否为纯工作流模式 (mode === 3)
const isWorkflowMode = computed(() => {
  return editWorkflowRecord.value && editWorkflowRecord.value.mode === 3;
});

// 计算属性
const workflowRunLogUrl = computed(() => {
  const url = import.meta.env.VITE_CHATBOT;
  const base = url.replaceAll("/q/chat", "");
  return base + `/f/app/${currWorkflowId.value}/logs`;
});

// 计算属性 - 截断的描述文本
const truncatedDescription = computed(() => {
  if (!team.value?.description) return '';
  return team.value.description.length > 60 
    ? team.value.description.substring(0, 60) + '...' 
    : team.value.description;
});

// 计算属性 - 是否需要显示展开按钮
const shouldShowExpandButton = computed(() => {
  return team.value?.description && team.value.description.length > 60;
});

// 表格列定义 - 成员
const memberColumns = [
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '姓名',
    dataIndex: 'nickname',
    key: 'nickname',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
  },
  {
    title: '角色',
    dataIndex: 'is_superuser',
    key: 'is_superuser',
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
  },
];

// 表格列定义 - 工作流
const workflowColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '创建者',
    dataIndex: 'account_name',
    key: 'account_name',
  },
  {
    title: '应用类型',
    dataIndex: 'mode',
    key: 'mode',
  },
  {
    title: '对话次数',
    dataIndex: 'chat_record_count',
    key: 'chat_record_count',
    sorter: {
      compare: (a, b) => a.chat_record_count - b.chat_record_count,
      multiple: 1,
    },
  },
  { title: '发布时间', dataIndex: 'update_time', key: 'update_time'},
  {
    title: '锁定状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
    width: 400,
  },
];

// 表格列定义 - 其他人创建的工作流
const otherWorkflowColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '创建者',
    dataIndex: 'account_name',
    key: 'account_name',
  },
  {
    title: '应用类型',
    dataIndex: 'mode',
    key: 'mode',
  },
  {
    title: '对话次数',
    dataIndex: 'chat_record_count',
    key: 'chat_record_count',
    sorter: {
      compare: (a, b) => a.chat_record_count - b.chat_record_count,
      multiple: 1,
    },
  },
  { title: '发布时间', dataIndex: 'update_time', key: 'update_time'},
  {
    title: '锁定状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
    width: 400,
  },
];

// 表格列定义 - 知识库
const knowledgeBaseColumns = [
  {
    title: '名称',
    dataIndex: 'namespace',
    key: 'namespace',
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
  },
  {
    title: '文档数',
    dataIndex: 'total',
    key: 'total',
  },
  {
    title: '数据集数',
    dataIndex: 'dataset_total',
    key: 'dataset_total',
  },
  {
    title: '最后更新',
    dataIndex: 'last_process_time',
    key: 'last_process_time',
  },
  {
    title: '操作',
    key: 'action',
    width: 220,
  },
];

// 切换描述展开/收起状态
const toggleDescription = () => {
  isDescriptionExpanded.value = !isDescriptionExpanded.value;
};

// 轮询相关
const pollingInterval = ref<number | null>(null);

// 启动轮询
const startPolling = () => {
  // 清除可能存在的旧轮询
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value);
  }
  // 设置新的轮询，每5秒执行一次
  pollingInterval.value = window.setInterval(() => {
    fetchTeamWorkflows();
  }, 5000);
};

// 停止轮询
const stopPolling = () => {
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value);
    pollingInterval.value = null;
  }
};

// 获取团队详情
const fetchTeamDetail = async () => {
  loading.value = true;
  try {
    const res = await getTeamDetail({ id: teamId.value });
    if (res.code === 0) {
      team.value = res.data;
    }
  } catch (error) {
    console.error('获取团队详情出错:', error);
    message.error('获取团队详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取团队成员
const fetchTeamMembers = async () => {
  membersLoading.value = true;
  try {
    const res = await getTeamMembers({ team_id: teamId.value });
    // const res = await getTeamMembers({page_size: 99999});
    console.log('getTeamMembers: ', res)
    if (res.code == 200) {
      teamMembers.value = res.data.results || [];
    }
  } catch (error) {
    console.error('获取团队成员出错:', error);
    message.error('获取团队成员失败');
  } finally {
    membersLoading.value = false;
  }
};

// 获取团队工作流
const fetchTeamWorkflows = async () => {
  // workflowsLoading.value = true;
  try {
    const res = await getTeamWorkflows({ team_id: teamId.value });
    console.log('getTeamWorkflows: ', res)
    if (res.code === 0) {
      const allWorkflows = res.data || [];

      teamWorkflows.value = allWorkflows || [];
      
      // 根据当前用户ID和fabuRadioValue区分
      // if (isAdmin.value) {
      //   if (fabuRadioValue.value === '1') {
      //     // 管理员选择"我创建的"
      //     teamWorkflows.value = allWorkflows.filter(item => item.creator === accountStore.account.id) || [];
      //   } else {
      //     // 管理员选择"其他创建"
      //     otherTeamWorkflows.value = allWorkflows.filter(item => item.creator !== accountStore.account.id) || [];
      //   }
      // } else {
      //   // 非管理员只能看到自己创建的
      //   teamWorkflows.value = allWorkflows || [];
      // }
    }
  } catch (error) {
    console.error('获取团队工作流出错:', error);
    message.error('获取团队工作流失败');
  } finally {
    workflowsLoading.value = false;
  }
};

// 获取团队知识库
const fetchTeamKnowledgeBases = async () => {
  knowledgeBasesLoading.value = true;
  try {
    const res = await getTeamKnowledgeBases({ team_id: teamId.value });
    console.log('getTeamKnowledgeBases: ', res)
    if (res.code === 0) {
      teamKnowledgeBases.value = res.data || [];
    }
  } catch (error) {
    console.error('获取团队知识库出错:', error);
    message.error('获取团队知识库失败');
  } finally {
    knowledgeBasesLoading.value = false;
  }
};

// 获取所有用户
const fetchUsers = async () => {
  usersLoading.value = true;
  try {
    const res = await getAccountPage({ page_size: 99999 });
    if (res.code == 200) {
      const users = res.data.results || [];
      // 过滤掉已经是团队成员的用户
      const currentMemberIds = teamMembers.value.map(member => member.id);
      const filteredUsers = users.filter((user: any) => !currentMemberIds.includes(user.id));
      
      userOptions.value = filteredUsers.map((user: any) => ({
        label: `${user.nickname || user.username} (${user.email})`,
        value: user.id,
      }));
    }
  } catch (error) {
    console.error('获取用户列表出错:', error);
  } finally {
    usersLoading.value = false;
  }
};

// 用于模糊搜索的过滤函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 显示添加成员模态框
const showAddMemberModal = () => {
  memberForm.account_ids = [];
  memberModalVisible.value = true;
  fetchUsers();
};

// 创建工作流
const createWorkflow = (type = 'workflow') => {
  // 设置模态框标题
  if (type === 'workflow-chat') {
    appModalTitle.value = "创建工作流编排对话";
  } else {
    appModalTitle.value = "创建工作流";
  }
  
  // 使用保存工作流模态框替代原有工作流表单
  formData.team_id = teamId.value;
  formData.name = '';
  formData.apikey_id = null;
  formData.mode = type === 'workflow' ? 3 : 2; // 设置为工作流模式或工作流编排对话模式
  
  // 获取API Key列表
  fetchApiKeys();
  
  showCreateAppVisible.value = true;
};

// 获取API Key列表
const fetchApiKeys = async () => {
  try {
    await apikeyStore.getApikeyList();
  } catch (error) {
    console.error('获取API Key出错:', error);
    message.error('获取API Key失败');
  }
};

// 提交创建工作流
const submit = () => {
  loading.value = true;
  form.value
    ?.validate()
    .then(() => {
      // 确保设置了团队ID
      formData.team_id = teamId.value;
      saveRelease(formData).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          message.success(`创建工作流成功`);
          showCreateAppVisible.value = false;
          // 刷新工作流列表
          fetchTeamWorkflows();
        }
      }).catch((e) => {
        console.error(e);
        message.error('创建工作流失败');
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

// 创建知识库
const createKnowledgeBase = () => {
  knowledgeBaseForm.id = undefined; // 确保ID为undefined表示新建
  knowledgeBaseForm.namespace = '';
  knowledgeBaseForm.description = '';
  knowledgeBaseForm.bg = '';
  knowledgeBaseForm.share_scope = '0';
  knowledgeBaseForm.team_id = teamId.value;
  knowledgeBaseModalVisible.value = true;
};

// 编辑知识库
const editKnowledgeBase = (record) => {
  knowledgeBaseForm.id = record.id; // 设置ID表示编辑
  knowledgeBaseForm.namespace = record.namespace;
  knowledgeBaseForm.description = record.description;
  knowledgeBaseForm.bg = record.bg;
  knowledgeBaseForm.share_scope = String(record.share_scope);
  knowledgeBaseForm.team_id = teamId.value;
  knowledgeBaseModalVisible.value = true;
};

// 确认删除知识库
const confirmDeleteKnowledgeBase = (record) => {
  const isOwner = record.create_account?.id === accountStore.account.id;
  const actionText = isOwner ? '删除' : '取消共享';
  const actionDescription = isOwner ? '删除' : '取消共享';
  
  Modal.confirm({
    title: `确定要${actionText}该知识库吗？`,
    content: `将从团队中${actionDescription}知识库: ${record.namespace}`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteTeamKnowledgeBase({
          id: record.id,
          team_id: teamId.value
        });
        
        if (res.code === 0) {
          message.success(`知识库${actionText}成功`);
          // 重新获取知识库列表
          fetchTeamKnowledgeBases();
        }
      } catch (error) {
        console.error(`${actionText}知识库出错:`, error);
        message.error(`${actionText}知识库失败`);
      }
    }
  });
};

// 保存知识库
const saveKnowledgeBase = async () => {
  if (!knowledgeBaseForm.namespace) {
    message.warning('请输入知识库名称');
    return;
  }
  if (!knowledgeBaseForm.description) {
    message.warning('请输入知识库描述');
    return;
  }

  savingKnowledgeBase.value = true;
  try {
    // 确保设置团队ID和共享范围
    knowledgeBaseForm.team_id = teamId.value;
    knowledgeBaseForm.share_scope = '0'; // 强制设置为仅自己可用
    
    let res;
    if (knowledgeBaseForm.id) {
      // 如果有ID，说明是编辑操作
      res = await http.request('/resource/knowledge_base', 'PUT_JSON', knowledgeBaseForm);
    } else {
      // 否则是新增操作
      res = await createTeamKnowledgeBase(knowledgeBaseForm);
    }
    
    if (res.code === 0) {
      message.success(knowledgeBaseForm.id ? '更新知识库成功' : '创建知识库成功');
      knowledgeBaseModalVisible.value = false;
      fetchTeamKnowledgeBases(); // 重新获取知识库列表
    }
  } catch (error) {
    console.error(knowledgeBaseForm.id ? '更新知识库出错:' : '创建知识库出错:', error);
    message.error(knowledgeBaseForm.id ? '更新知识库失败' : '创建知识库失败');
  } finally {
    savingKnowledgeBase.value = false;
  }
};

// 知识库文件上传预处理
const knowledgeBaseBeforeUpload = async file => {
  const fileExtName = file.name.substring(file.name.lastIndexOf(".") + 1);
  const fileName = uuidv4() + "." + fileExtName;
  const oss_key = baseFilePath.value + '/' + fileName;

  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    message.error('单个图片最大1MB!');
    return false;
  }

  const fileData = {name: file.name, oss_key: oss_key, size: file.size, status: 0, url: ""};
  fileDict.value[file.uid] = fileData;
  return true;
};

const handleChange = async (info) => {
  const status = info.file.status;

  if (info.file.status === 'uploading') {
    uploadLoading.value = true;
    return;
  }
  if (info.file.status === 'done') {
    knowledgeBaseForm.bg = fileDict.value[info.file.uid]?.url || '';
    uploadLoading.value = false;
  }
  if (info.file.status === 'error') {
    uploadLoading.value = false;
    message.error('上传失败');
  }

  if(status === 'removed') {
    delete fileDict.value[info.file.uid];
  }
};

const customRequest = async (info) => {
  try {
    if (!ossStsToken.value) {
      const res = await getOssStsToken();
      ossStsToken.value = res;
      if (!ossStsToken.value) {
        message.error('获取上传凭证失败');
        return;
      }
    }
    
    const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
    const client = new OSS({
      region: ossStsToken.value.region,
      accessKeyId: ossStsToken.value.access_key_id,
      accessKeySecret: ossStsToken.value.access_key_secret,
      stsToken: ossStsToken.value.security_token,
      bucket: ossStsToken.value.bucket_name,
      endpoint: ossStsToken.value.endpoint,
      useFetch: true,
      cname: use_cname,
      secure: true,
    });

    const oss_key = fileDict.value[info.file.uid]["oss_key"];
    if (info.file) {
      await client.multipartUpload(oss_key, info.file, {
        parallel: 4,
        partSize: 100 * 1024,
        progress: function (percent) {
          uploadLoading.value = true;
          if (percent === 1) {
            uploadLoading.value = false;
          }
        },
      }).then(res => {
        fileDict.value[info.file.uid]["status"] = 1;
        fileDict.value[info.file.uid]["url"] = ossStsToken.value.endpoint + '/' + oss_key;
        fileDict.value = {...fileDict.value};
        knowledgeBaseForm.bg = fileDict.value[info.file.uid]["url"];
      }).catch((err) => {
        console.error('上传文件出错:', err);
        message.error('上传失败');
        uploadLoading.value = false;
      });
    }
  } catch (error) {
    console.error('上传文件出错:', error);
    uploadLoading.value = false;
    message.error('上传失败');
  }
};

// 获取OSS令牌
const getOssStsToken = async () => {
  try {
    const res = await http.request('/thirdparty/oss/sts_token?public=1', 'GET');
    if (res && res.data) {
      return res.data;
    }
    return null;
  } catch (error) {
    console.error('获取OSS令牌出错:', error);
    return null;
  }
};

// 导航到工作流详情
const navigateToWorkflow = (item) => {
  // 确保apikey_id是字符串类型
  if (item.apikey_id !== null && item.apikey_id !== undefined) {
    item = {
      ...item,
      apikey_id: String(item.apikey_id)
    };
  }
  editWorkflowRecord.value = item;
  showWorkflowModalVisible.value = true;
};

// 返回团队列表
const goBack = () => {
  router.push('/team/list');
};

// 删除成员
const confirmDeleteMember = (member: any) => {
  Modal.confirm({
    title: '确定要删除该成员吗？',
    content: `将从团队中移除成员: ${member.nickname || member.username}`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteTeamMembers({
          team_id: teamId.value,
          account_id: member.id
        });
        
        if (res.code === 0) {
          message.success('成员删除成功');
          // 重新获取成员列表
          fetchTeamMembers();
        }
      } catch (error) {
        console.error('删除成员出错:', error);
        message.error('删除成员失败');
      }
    }
  });
};

// 添加成员
const addMembers = async () => {
  if (memberForm.account_ids.length === 0) {
    message.warning('请选择要添加的成员');
    return;
  }

  savingMembers.value = true;
  try {
    const res = await addTeamMembers({
      team_id: teamId.value,
      account_ids: memberForm.account_ids
    });
    
    if (res.code !== 0) {
      return;
    }
    
    message.success('添加成员成功');
    memberModalVisible.value = false;
    fetchTeamMembers(); // 重新获取成员列表
  } catch (error) {
    console.error('添加团队成员出错:', error);
    message.error('添加团队成员失败');
  } finally {
    savingMembers.value = false;
  }
};

// 显示工作流日志抽屉
const showWorkflowLogDrawer = (workflow) => {
  currWorkflowId.value = workflow.d_app_id || workflow.id;
  showLogVisible.value = true;
};

// 关闭日志抽屉
const changeShowLogVisible = (visible) => {
  showLogVisible.value = visible;
};

// 选择导入文件目标
const selectImportFile = (workflow) => {
  currImportWorkflowId.value = workflow.id;
};

// 导入工作流
const beforeUpload = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    fileContent.value = e.target.result;
    importWorkflow(fileContent.value);
  };
  reader.readAsText(file);
  // 阻止默认的上传行为
  return false;
};

const importWorkflow = (fileContent) => {
  if (!currImportWorkflowId.value) {
    message.error('未选择工作流');
    return;
  }
  
  const params = {
    release_id: currImportWorkflowId.value,
    yaml_content: fileContent,
  };
  
  try {
    importReleaseWorkflow(params).then((res) => {
      if (res.code === 0) {
        message.success('导入成功');
        fetchTeamWorkflows(); // 刷新工作流列表
      }
    });
  } catch (error) {
    console.error('导入工作流出错:', error);
    message.error('导入失败');
  }
};

// 工作流导出
const exportWorkflow = (workflow) => {
  const params = {
    release_id: workflow.id,
  };
  
  try {
    exportReleaseWorkflow(params).then((res) => {
      if (res.code === 0) {
        downloadYml(workflow.name, res.data?.data);
        message.success('导出成功');
      }
    });
  } catch (error) {
    console.error('导出工作流出错:', error);
    message.error('导出失败');
  }
};

// 下载YAML文件
const downloadYml = (filename, textContent) => {
  if (!textContent) {
    message.error('导出内容为空');
    return;
  }
  
  // 创建一个Blob实例，类型为纯文本
  const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
  // 创建一个指向Blob的URL
  const url = URL.createObjectURL(blob);
  // 创建一个a标签用于下载
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename + '.yml');
  document.body.appendChild(link);
  // 触发下载
  link.click();
  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// 复制工作流
const copyWorkflow = (workflow) => {
  showCopyWorkflowVisible.value = true;
  workflowCopyForm.id = workflow.id;
  // 生成5位随机字符串
  const randomStr = uuidv4().replace(/-/g, '').substring(0, 5);
  workflowCopyForm.name = `${workflow.name}_copy_${randomStr}`;
};

// 提交复制工作流
const workflowCopySubmit = () => {
  if (!workflowCopyForm.name) {
    message.warning('请输入工作流名称');
    return;
  }

  copyingWorkflow.value = true;
  const params = {
    release_id: workflowCopyForm.id,
    name: workflowCopyForm.name,
    team_id: teamId.value
  };
  
  try {
    copyReleaseApp(params).then((res) => {
      if (res.code === 0) {
        message.success('复制成功');
        showCopyWorkflowVisible.value = false;
        fetchTeamWorkflows(); // 刷新工作流列表
      }
    }).finally(() => {
      copyingWorkflow.value = false;
    });
  } catch (error) {
    console.error('复制工作流出错:', error);
    message.error('复制失败');
    copyingWorkflow.value = false;
  }
};

// 确认删除工作流
const showDeleteConfirm = (workflow) => {
  const opt = isAdmin.value && fabuRadioValue.value === '2' ? "清空" : "删除";
  Modal.confirm({
    title: `确认要${opt}工作流"${workflow.name}"吗?`,
    icon: createVNode(ExclamationCircleOutlined),
    content: `${opt}后将无法恢复，请谨慎操作`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteWorkflow(workflow);
    }
  });
};

// 删除工作流
const deleteWorkflow = (workflow) => {
  try {
    deleteRelease(workflow).then((res) => {
      if (res.code === 0) {
        message.success('删除成功');
        fetchTeamWorkflows(); // 刷新工作流列表
      }
    });
  } catch (error) {
    console.error('删除工作流出错:', error);
    message.error('删除失败');
  }
};

// 设置文档管理模态框可见
const setDocumentModalVisible = (itemId, itemName, visible, docTabKey) => {
  knowledgeBaseId.value = itemId;
  knowledgeBaseNamespace.value = itemName;
  documentModalVisible.value = visible;
  tabActiveKey.value = docTabKey;
};

// 设置工作流模态框可见状态
const setWorkflowModalVisible = (visible, refresh = false) => {
  showWorkflowModalVisible.value = visible;
  if (showWorkflowModalVisible.value === false) {
    if (editWorkflowRecord?.value?.id) {
      postReleaseLock({
        release_id: editWorkflowRecord.value.id
      });
    }
  }
  if (refresh) {
    fetchTeamWorkflows(); // 刷新工作流列表
  }
};

// 设置正在编辑的工作流记录
const setEditWorkflowRecord = (record) => {
  editWorkflowRecord.value = record;
};

onMounted(() => {
  fetchTeamDetail();
  fetchTeamMembers();
  fetchTeamWorkflows();
  fetchTeamKnowledgeBases();
  fetchApiKeys(); // 确保API Key列表在初始化时就加载
  // 尝试预加载OSS Token
  getOssStsToken().then(token => {
    ossStsToken.value = token;
  });
  // 启动轮询
  startPolling();
});

// 组件卸载时清除轮询
onUnmounted(() => {
  stopPolling();
  if (editWorkflowRecord?.value?.id) {
    postReleaseLock({
      release_id: editWorkflowRecord.value.id
    });
  }
});
</script>

<style scoped>
.team-detail {
  padding: 16px;
}

.team-name {
  font-size: 20px;
  font-weight: bold;
  margin-right: 12px;
}

.team-info {
  display: block;
  max-width: 90%;
}

.team-description-wrapper {
  margin-top: 10px;
}

.team-description {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}
.team-description-full {
  display: block;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  height: auto; 
  word-wrap: break-word; 
  word-break: break-all;
  white-space: normal;
  overflow: visible;
}

.action-btns {
  display: flex;
  align-items: center;
}

</style> 