<template>
  <div
    style="width: 100%; height: 400px"
    class="line-chart"
    ref="container"
  ></div>
</template>
<script lang="ts" setup>
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import type { EChartsType } from 'echarts';
  import * as echarts from 'echarts';
  import dayjs from 'dayjs';
  import { formatThousand } from '@/utils/formatter';

  let chart: EChartsType | null = null;
  const container = ref<HTMLElement>();

  const props = defineProps({
    list: Array
  });

  function resize() {
    chart?.resize();
  }

  onMounted(() => {
    console.log("line chart");
    console.log(props.list);
    
    const days = 7;
    const promptTokensList = Array.from({ length: days }, (v, i) => i).map(day => {
      let date = dayjs().subtract(day, 'day').format('YYYY-MM-DD');
      const dateList = props.list.filter((item) => item.date === date);
      if (dateList.length > 0){
        return [dayjs(date).format('MM/DD'), dateList[0].prompt_tokens_sum]
      }
      return [dayjs(date).format('MM/DD'), 0];
    });
    const completionTokensList = Array.from({ length: days }, (v, i) => i).map(day => {
      let date = dayjs().subtract(day, 'day').format('YYYY-MM-DD');
      const dateList = props.list.filter((item) => item.date === date);
      if (dateList.length > 0){
        return [dayjs(date).format('MM/DD'), dateList[0].completion_tokens_sum]
      }
      return [dayjs(date).format('MM/DD'), 0];
    });
    const totalTokensList = Array.from({ length: days }, (v, i) => i).map(day => {
      let date = dayjs().subtract(day, 'day').format('YYYY-MM-DD');
      const dateList = props.list.filter((item) => item.date === date);
      if (dateList.length > 0){
        return [dayjs(date).format('MM/DD'), dateList[0].total_tokens_sum]
      }
      return [dayjs(date).format('MM/DD'), 0];
    });
    const promptTokensLineList = promptTokensList.reverse();
    const completionTokensLineList = completionTokensList.reverse();
    const totalTokensLineList = totalTokensList.reverse();


    chart = echarts.init(container.value!);
    chart.setOption({
      color: ['#005af9', '#985af9', 'green',],
      grid: [
        {
          top: 80,
          left: 42,
          right: 24,
          bottom: 20,
        },
      ],
      xAxis: [
        {
          name: '日期',
          nameTextStyle: { color: 'rgba(0 , 0, 0, 0)' },
          type: 'category',
          axisTick: { show: false },
          axisLine: { show: false },
          boundaryGap: 0,
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          name: '用量',
          nameTextStyle: { color: 'rgba(0 , 0, 0, 0)' },
          type: 'value',
          axisTick: { show: false },
          axisLine: { show: false },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              width: 1,
              color: 'rgba(0, 0, 0, 0.15)',
            },
          },
          axisLabel: {
            formatter: function (value, index){
              return formatThousand(value/1000, 0)
            }
          }
        },
      ],
      legend: {
        show: true,
        right: '8',
        top: 0,
        orient: 'vertical',
      },
      tooltip: {
        show: true,
        trigger: 'axis',
      },
      series: [
        {
          name: 'prompt tokens',

          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
          },
          tooltip: {
            valueFormatter: value => formatThousand(value/1000, 0)
          },
          data: promptTokensLineList,
        },
        {
          name: 'completion tokens',

          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
          },
          tooltip: {
            valueFormatter: value => formatThousand(value/1000, 0)
          },
          data: completionTokensLineList,
        },
        {
          name: 'total tokens',
          type: 'line',
          smooth: true,
          width: 4,
          lineStyle: {
            width: 3,
          },
          tooltip: {
            valueFormatter: value => formatThousand(value/1000, 0)
          },
          data: totalTokensLineList,
        },
      ],
    });
    window.addEventListener('resize', resize);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', resize);
  });
</script>
