<template>
  <a-table 
    size="small"
    :columns="computedColumns"
    :dataSource="groupAssistantPage.results"
    :pagination="{ pageSize: groupAssistantPage.page_size, current: groupAssistantPage.page_number, total: groupAssistantPage.count }"
    >
    <template #title>
      <div class="text-subtext text-sm flex justify-between items-center">
        <span style="font-size:14px;">绑定{{ accountType == 1?"企业微信":"微信" }}群助手账号</span>
        <a-button style="margin-right: 5px;" type="primary" @click="chagegeQwAccountDialog(true)">
          <PlusOutlined class="text-lg" />绑定账号
        </a-button>
      </div>
    </template>
    <template #headerCell="{ column }">
      <template v-if="column.key === 'secret_key'">
        秘钥
        <a-popover title="" trigger="hover" placement="right">
          <template #content>
            {{accountLabel}}群助手账号与平台的通信凭证
          </template>
          <QuestionCircleOutlined />
        </a-popover>
      </template>
      <template v-if="column.key === 'release_id'">
        关联应用项目
        <a-popover title="" trigger="hover" placement="right">
          <template #content>
            需要回复的对话未配置关联应用项目时，默认使用账号配置的关联应用项目。
          </template>
          <QuestionCircleOutlined />
        </a-popover>
      </template>
    </template>
    <template #bodyCell="{ text, record, index, column }">
      <template v-if="column.dataIndex === 'create_time'">
        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
      <template v-else-if="column.dataIndex === 'secret_key'">
        {{ isSecretKeyVisible[record.id]?text:text.substring(1, 3)+"******************************" }}
        <a-button type="link" @click="changeSecretKeyVisible(record)">{{ !isSecretKeyVisible[record.id] ? "显示" : "隐藏" }}</a-button>
      </template>
      <template v-else-if="column.dataIndex === 'status'">
        <span v-if="record.status === '在线'">
          <a-badge color="lime" :text="record.status"/>
        </span>
        <span v-else-if="['离线', '退出登录'].includes(record.status)">
          <a-badge color="red" :text="record.status"/>
          <a-popover placement="right" overlay-class-name="limit-popover-width">
            <template #content>
              <div style="white-space: pre-line;">{{record.reason?record.reason:"请检查助手程序是否处于运行状态，或网络是否正常。"}}</div>
            </template>
            <info-circle-outlined style="color: red;margin-left: 4px;"/>
          </a-popover>
        </span>
        <span v-else>{{ text }}</span>
      </template>
      <template v-else-if="column.dataIndex === 'release_id'">
        <a-dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            <span v-if="record.release_id === 0">
              请选择
            </span>
            <span v-if="record.release_id > 0 && releaseAppList.length > 0">
              {{ releaseAppList.filter((item) => parseInt(item.value) === record.release_id)[0]['label'] }}
            </span>
            <DownOutlined />
          </a>
          <template #overlay>
            <a-menu v-if="releaseAppList.length > 0">
              <a-menu-item v-for="item in releaseAppList.filter((item) => parseInt(item.value) !== record.release_id)">
                <a @click="updateGroupRelease(record, item.value)">{{ item.label }}</a>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-tooltip placement="topLeft">
          <template #title>
            <span>解除关联的应用项目</span>
          </template>
          <span style="margin-left: 4px;cursor: pointer;" v-if="record.release_id > 0">
            <CloseCircleOutlined @click="updateGroupRelease(record, '0')"/>
          </span>
        </a-tooltip>
      </template>
      <template v-else-if="column.dataIndex === 'last_answer_time'">
        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
      <template v-else-if="column.dataIndex === 'operation'">
        <a style="font-weight: normal;" @click="downloadSoft(record)">下载</a>
      </template>
    </template>
  </a-table>
  <a-modal :okButtonProps="{ loading }" v-model:visible="showForm" :title="`绑定${accountLabel}群助手账号`"
    @cancel="chagegeQwAccountDialog(false)"
    width="1200px"
    style="top: 0px;"
    :footer="null"
  >
    <div>
      <a-steps :current="current">
        <a-step v-for="item in steps" :key="item.title" :title="item.title" />
      </a-steps>
      <div class="steps-content">
        {{ steps[current].content }}
        <div v-if="current === 0" style="margin-top: 8px;">
          <p>请先下载指定版本的{{accountLabel}}，版本: {{version}}，文件大小: {{ size }}</p>
          <p>
            <a :href="weixinDownloadUrl" target="_blank">点击下载</a> &nbsp;
            <a @click="copyDownloadUrl(weixinDownloadUrl)">复制下载地址</a>
          </p>
        </div>
        <div v-if="current === 1" style="margin-top: 8px;">
          <a-form ref="form" :model="formData" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16, offset: 1 }">
            <a-form-item required name="account" :label="accountLabel"
              :rules="[{ required: true, message: `请输入${accountLabel}账号` }]"
            >
              <a-input 
                v-model:value="formData.account" 
                :placeholder="`请输入${accountLabel}账号`" 
                show-count 
                :maxlength="50"
              />
            </a-form-item>
          </a-form>
        </div>
        <div v-if="current === 2" style="margin-top: 8px;">
          <p>下载{{accountLabel}}群助手.zip，版本: V1，文件大小: 17.3MB</p>
          <p>
            <a :href="weixinAssistantInstallPkgDownloadUrl" target="_blank">点击下载</a> &nbsp;
            <a @click="copyDownloadUrl(weixinAssistantInstallPkgDownloadUrl)">复制下载地址</a>
          </p>
        </div>
      </div>
      <div class="steps-action">
        <a-button v-if="current > 0" @click="prev" style="margin-right: 8px">上一步</a-button>
        <a-button v-if="current < steps.length - 1" type="primary" @click="next">下一步</a-button>
        <a-button
          v-if="current == steps.length - 1"
          type="primary"
          @click="bindComplete"
        >
          完成
        </a-button>
      </div>
    </div>
  </a-modal>
  <a-drawer 
    title="软件下载"
    :closable="true" 
    size="large" 
    :visible="downloadSoftDrawer" 
    @close="downloadSoftDrawer = false"
  >
    <div class="steps-content">
      <div style="margin-top: 8px;">
        <p>下载{{accountLabel}}群助手.zip，版本: V1，文件大小: {{assistantSize}}</p>
        <p>
          <a :href="weixinAssistantInstallPkgDownloadUrl" target="_blank">点击下载</a> &nbsp;
          <a @click="copyDownloadUrl(weixinAssistantInstallPkgDownloadUrl)">复制下载地址</a>
        </p>
      </div>
    </div>
    <div class="steps-content">
      <div style="margin-top: 8px;">
        <p>指定版本的微信，版本: {{version}}，文件大小: {{ size }}</p>
        <p>
          <a :href="weixinDownloadUrl" target="_blank">点击下载</a> &nbsp;
          <a @click="copyDownloadUrl(weixinDownloadUrl)">复制下载地址</a>
        </p>
      </div>
    </div>
  </a-drawer>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { FormInstance } from 'ant-design-vue';
import { getReleaseList } from "@/api/release";
import { 
  weixinGroupAssistantPage, 
  addWeixinGroupAssistant, 
  weixinGroupChangeRelaeseId } from "@/api/group_assistant";
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { useClipboard } from '@vueuse/core'

const props = defineProps({
  accountType: Number,
});

const releaseAppList = ref([]);

const accountLabel = computed(() => {
  return props.accountType === 1?"企业微信": "微信";
})

const { copy } = useClipboard({ legacy: true })

const weixinDownloadUrl = computed(() => {
  return props.accountType === 1?"https://dldir1.qq.com/wework/work_weixin/WeCom_4.1.33.8005.exe": "https://dldir1v6.qq.com/weixin/Windows/WeChatSetup.exe";
})
const version = computed(() => {
  return props.accountType === 1?"4.1.33.8005": "3.9.12";
})
const size = computed(() => {
  return props.accountType === 1?"507.9MB": "271MB";
})
const assistantSize = computed(() => {
  return props.accountType === 1?"73.7MB": "17.3MB";
})

const weixinAssistantInstallPkgDownloadUrl = ref(null);

const loading = ref(false);
const showForm = ref(false);
const downloadSoftDrawer = ref(false);

const columns = [
  {
    title: `${accountLabel.value}账号`,
    dataIndex: 'account',
    key: 'account',
  }, 
  {
    title: '昵称',
    dataIndex: 'nickname',
    key: 'nickname',
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
  },
  {
    title: '默认应用',
    key: 'release_id',
    dataIndex: 'release_id',
  },
  {
    title: '已完成回答',
    key: 'answer_count',
    dataIndex: 'answer_count',
    sorter: {
      compare: (a, b) => a.answer_count - b.answer_count,
      multiple: 1,
    },
  },
  {
    title: '最近回答时间',
    key: 'last_answer_time',
    dataIndex: 'last_answer_time',
  },
  {
    title: '创建时间',
    key: 'create_time',
    dataIndex: 'create_time',
  },
  {
    title: '秘钥',
    dataIndex: 'secret_key',
    key: 'secret_key',
    width: 350,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 100,
    fixed: 'right',
  },
];

const computedColumns = computed(() => {
  if (props.accountType === 1){
    return columns
  }
  return columns.filter(item=> item.key !== 'release_id');
})

const current = ref<number>(0);

const next = () => {
  console.log(current.value);
  if(current.value === 1){
    submit()
  }else{
    current.value++;
  }
};

const bindComplete = () => {
  message.success(`恭喜您，已完成${accountLabel.value}账号绑定！`);
  chagegeQwAccountDialog(false);
  getWeixinGroupAssistantPage();
}

const prev = () => {
  current.value--;
};

const steps = [
  {
    title: `登录${accountLabel.value}账号`,
    content: `在Windows系统上下载安装指定版本的${accountLabel.value}`,
  },
  {
    title: `填写${accountLabel.value}账号`,
    content: `填写刚刚登录的${accountLabel.value}账号`,
  },
  {
    title: `运行${accountLabel.value}群助手`,
    content: `在Windows系统上下载安装${accountLabel.value}群助手`,
  },
];

const groupAssistantPage = ref([]);

function chagegeQwAccountDialog(visible){
  if(visible === false){
    formData.value = {
      account: null,
    }
  }
  showForm.value = visible
}

const formData = ref({
  account: null,
});

const form = ref<FormInstance>();

function submit() {
  loading.value = true;
  form.value
    ?.validate()
    .then(() => {
      const params = {
        account_type: props.accountType,
        account: formData.value.account
      }
      addWeixinGroupAssistant(params).then((res) => {
        const { code, data } = res;
        console.log(data);
        // getWeixinGroupAssistantPage();
        if(code === 0){
          // const { install_pkg_url } = data;
          // console.log(install_pkg_url);
          // weworkAssistantInstallPkgDownloadUrl.value = install_pkg_url;
          current.value++;
        }
      });
      // chagegeQwAccountDialog(false);
    })
    .finally(() => {
      loading.value = false;
    });
}

function getWeixinGroupAssistantPage(){
  const params = {account_type: props.accountType};
  const data = weixinGroupAssistantPage(params).then((res) => {
    const { data } = res;
    console.log(data);
    groupAssistantPage.value = data;
    return data;
  });
  return data;
}

function downloadSoft(record){
  downloadSoftDrawer.value = true;
  weixinAssistantInstallPkgDownloadUrl.value = record.install_pkg_url;
}

const isSecretKeyVisible = ref({});

const changeSecretKeyVisible = (record) => {
  isSecretKeyVisible.value[record.id] = !isSecretKeyVisible.value[record.id];
}

async function copyDownloadUrl(url){
  await copy(url)
  message.success("已复制")
}

function getReleaseAppList(){
  getReleaseList().then((response) => {
    console.log(response);
    const { data } = response;
    data?.forEach(item => {
      if(item.mode === 3){
        return;
      }
      releaseAppList.value.push({
        label: `${item.name}`,
        value: `${item.id}`,
      });
    })
  }).catch((e) => {
    console.error(e);
  });
}

const updateGroupRelease = (record, release_id: string) => {
  console.log(record, release_id);
  const weixin_account_id = record.id;
  const releaseId = parseInt(release_id);
  if (record.release_id === releaseId){
    return
  }
  const params = {
    weixin_account_id: weixin_account_id,
    release_id: releaseId,
  }
  weixinGroupChangeRelaeseId(params).then((res) => {
    const { code, data } = res;
    console.log(res);
    if (code === 0){
      let fd = groupAssistantPage.value.results.filter(item => weixin_account_id === item.id)[0];
      console.log(fd);
      fd["release_id"] = releaseId;
      Object.assign(groupAssistantPage.value.results.filter(item => weixin_account_id === item.id)[0], fd);
      message.success("操作成功");
    }else{
      message.error("操作失败");
    }
    return data;
  });
}

onMounted(() => {
  getWeixinGroupAssistantPage();
  getReleaseAppList();
})
</script>
<style scoped>
.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 60px;
}

.steps-action {
  margin-top: 24px;
  text-align: center;
}

[data-theme='dark'] .steps-content {
  background-color: #2f2f2f;
  border: 1px dashed #404040;
}
</style>