<template>
  <a-modal :okButtonProps="{ loading }" width="540px" :visible="modalVisible" :title="formData.id > 0? '编辑问题' : '新增问题'" 
    @ok="saveRecord" ok-text="确认" cancel-text="取消"
    @cancel="setModalVisible(false, false)"
  >
    <a-form layout="vertical" ref="form" :model="formData" :labelCol="{ span: 24 }" :wrapperCol="{ span: 24, offset: 0 }">
      <a-form-item required name="question" label="问题"
        :rules="[{ required: true, message: '请输入问题' }]"
      >
        <a-input 
          v-model:value="formData.question" 
          placeholder="请输入问题" 
          show-count 
          :maxlength="200" 
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { watch, toRefs, ref } from 'vue';
import { saveFollowQuestion } from "@/api/release";

const props = defineProps({
  modalVisible: Boolean,
  source: Number,
  biz_id: Number,
  editRecordData: Object,
});

const {editRecordData} = toRefs(props);

const emit = defineEmits(['setFollowQuestionModalVisible'])

function resetFormDataInput(){
  formData.value.question = null
}

const formData = ref({
  id: 0,
  question: null,
});

const setModalVisible = (visible: boolean, refresh: boolean) => {
  console.log(visible, refresh);
  emit('setFollowQuestionModalVisible', visible, refresh);
  resetFormDataInput()
};

const loading = ref(false);

const form = ref<FormInstance>();

function saveRecord(){
  loading.value = true;
  form.value
    ?.validate()
    .then(() => {
      const params = {
        id: formData.value.id,
        source: props.source,
        biz_id: props.biz_id,
        question: formData.value.question,
      }
      saveFollowQuestion(params).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          setModalVisible(false, true);
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

watch(editRecordData, (val) => {
  console.log(val)
  formData.value.id = val.id
  formData.value.question = val.question
})

</script>