import { Response } from '@/types';
import http from "@/store/http";

export const groupAssistantPage = (params) =>
  http.request<any, Response<any>>("/app/group/assistant", 'GET', params)

export const addGroupAssistant = (params) =>
  http.request<any, Response<any>>("/app/group/assistant", 'POST_JSON', params)

export const weworkGroupPage = (params) =>
  http.request<any, Response<any>>("/app/wework/group", 'GET', params)

export const weworkGroupChangeRelaeseId = (params) =>
  http.request<any, Response<any>>("/app/wework/group/change/release", 'POST_JSON', params)

export const weworkGroupSendMessage = (params) =>
  http.request<any, Response<any>>("/app/wework/group/send/message", 'POST_JSON', params)

export const weworkGroupSendMessageRecordPage = (params) =>
  http.request<any, Response<any>>("/app/wework/group/send/message/record", 'GET', params)

export const deleteWeworkGroupSendTask = (params) =>
  http.request<any, Response<any>>("/app/wework/group/send/message/record", 'DELETE', params)

export const weworkGroupMessageList = (params) =>
  http.request<any, Response<any>>("/app/wework/group/message", 'GET', params)

export const weixinGroupAssistantPage = (params) =>
  http.request<any, Response<any>>("/app/weixin/group/assistant", 'GET', params)

export const addWeixinGroupAssistant = (params) =>
  http.request<any, Response<any>>("/app/weixin/group/assistant", 'POST_JSON', params)

export const weixinGroupPage = (params) =>
  http.request<any, Response<any>>("/app/weixin/group", 'GET', params)

export const saveWeixinGroup = (params) =>
  http.request<any, Response<any>>("/app/weixin/group", 'POST_JSON', params)

export const weixinGroupChangeRelaeseId = (params) =>
  http.request<any, Response<any>>("/app/weixin/group/change/release", 'POST_JSON', params)

export const weixinGroupMessageList = (params) =>
  http.request<any, Response<any>>("/app/weixin/group/message", 'GET', params)

export const weixinGroupSendMessage = (params) =>
  http.request<any, Response<any>>("/app/weixin/group/send/message", 'POST_JSON', params)

export const weixinGroupSendMessageRecordPage = (params) =>
  http.request<any, Response<any>>("/app/weixin/group/send/message/record", 'GET', params)

export const deleteWeixinGroupSendTask = (params) =>
  http.request<any, Response<any>>("/app/weixin/group/send/message/record", 'DELETE', params)


