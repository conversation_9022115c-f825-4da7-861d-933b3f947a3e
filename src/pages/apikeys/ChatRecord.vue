<template>
  <EnterQaModal
    :initQaEditRecord="editRecord"
    :qaModalVisible="qaModalVisible"
    :releaseIds="searchReleaseIds.filter((item) => item.value && parseInt(item.value) > 0)"
    @setQaModalVisible="setQaModalVisible"
  />
  <div class="chat-record grid grid-rows-none gap-4 mt-xxs">
    <div class="bg-container p-base rounded-b-lg rounded-tr-lg pt-8 flex items-end justify-end">
      <span v-if="username" style="line-height: 30px;margin-right: 5px;white-space: nowrap;">账号：{{ username }}</span>
      <div v-if="account.is_superuser" style="line-height: 30px;white-space: nowrap;">
        <a-radio-group v-model:value="scope" style="margin-right: 10px;">
          <a-radio-button value="1">当前账号</a-radio-button>
          <a-radio-button value="2">全部账号</a-radio-button>
        </a-radio-group>
      </div>
      <a-select
        :disabled="scope === '2'"
        mode="multiple"
        style="width: 15%;margin-right: 10px;"
        placeholder="Api keys"
        :max-tag-count="1"
        :filter-option="filterOption"
        :options="apiKeyIds"
        show-search
        allowClear
        @change="handleApiKeyChange"
      ></a-select>
      <a-select
        :disabled="scope === '2'"
        style="width: 15%;margin-right: 10px;"
        placeholder="来自"
        :filter-option="filterOption"
        :options="searchReleaseIds"
        show-search
        allowClear
        @change="handleReleaseChange"
      ></a-select>
      <a-select
        style="width: 10%;margin-right: 10px;"
        placeholder="回答状态"
        :options="answerStatusOptions"
        allowClear
        @change="handleAnswerStatusChange"
      ></a-select>
      <a-range-picker v-model:value="searchDate"
        :ranges="ranges"
        :disabled-date="disabledDate"
        :placeholder="['开始日期', '结束日期']"
        :allowClear="false"
        @calendarChange="onCalendarChange"
      />
      <a-input-search
        v-model:value="keyword"
        placeholder="多个关键字用空格分隔"
        style="width: 250px;margin-left: 10px;"
        allowClear
        @search="onSearch"
        @change="onSearchChange"
      />
      <a-button style="margin-left: 10px;" type="primary"
      @click="exportData"
      :disabled="chatRecordPage.count === 0"
      ><ExportOutlined class="text-lg" />导出</a-button>
    </div>
    <a-table
      size="small"
      :columns="columns"
      :dataSource="chatRecordPage.results"
      :pagination="{
        pageSize: chatRecordPage.page_size,
        current: chatRecordPage.page_number,
        total: chatRecordPage.count,
        pageSizeOptions: pageSizeOptions,
        defaultPageSize: defaultPageSize
      }"
      @change="handleTableChange"
      :defaultExpandAllRows="true"
    >
      <template #expandedRowRender="{ record }">
        <p style="margin: 15px;">
          <span style="font-weight: 700; color:#303133;">提问：</span>
          <div v-html="searchResultHighlight(record.user_message , keyword)" />
          <span v-if="record.user_message_ext">
            <br/>
            <img style="margin-top:5px; margin-right: 5px;" :src="item.image_url.url+'?x-oss-process=image/resize,m_lfit,w_100,h_100'" v-for="item in record.user_message_ext.filter(i => i.type === 'image_url')"/>
          </span>
        </p>
        <p style="margin: 15px;">
          <span style="font-weight: 700; color:#303133;">回答：</span><span>
            <div v-html="searchResultHighlight(record.assistant , keyword)" />
          </span>
        </p>
      </template>
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'id'">
          {{chatRecordPage.count - ((chatRecordPage.page_number - 1) * chatRecordPage.page_size ) - index }}
        </template>
        <template v-else-if="column.dataIndex === 'chat_start_time'">
          {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
        </template>
        <template v-else-if="column.dataIndex === 'user_message'">
          <a-popover placement="top" trigger="hover" overlay-class-name="limit-popover-width">
            <template #content>
              <div v-html="searchResultHighlight(text, keyword)" />
            </template>
            <div>
              <PaperClipOutlined style="color: #3758DE" v-if="record.user_message_ext && record.user_message_ext.some(x => x.type === 'document')" />
              <PictureTwoTone v-if="record.user_message_ext && record.user_message_ext.some(x => x.type === 'image_url')"/>
              <AudioTwoTone v-if="record.input_source === 2"/>
              <span v-html="searchResultHighlight(text, keyword)" />
            </div>
          </a-popover>
        </template>
        <template v-else-if="column.dataIndex === 'assistant'">
          <a-popover placement="top" trigger="hover" overlay-class-name="limit-popover-width">
            <template #content>
              <div v-html="searchResultHighlight(text, keyword)" />
            </template>
            <!-- <div v-if="record.stop_answer_flag"
            style="border-radius: 2px; width: .8em; height: .8em; background-color: #2870EA;"></div> -->
            <PauseCircleTwoTone two-tone-color="red" v-if="record.stop_answer_flag" />
            <div>
              {{searchResultHighlight(text, keyword)}}
            </div>
            <!-- <span><span role="img" class="anticon" style="color:#2870EA;" v-if="record.stop_answer_flag"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M864 64H160C107 64 64 107 64 160v704c0 53 43 96 96 96h704c53 0 96-43 96-96V160c0-53-43-96-96-96z"></path></svg></span> {{text}}</span> -->
          </a-popover>
        </template>
        <template v-else-if="column.dataIndex === 'source'">
          {{ text === 1?"对话":text === 2 || text === 5 ? "API调用":text === 4? "回归测试":text === 6? "企微群助手":text === 7? "微信群助手":"网页"}}
        </template>
        <template v-else-if="column.dataIndex === 'answer_status'">
          <CloseCircleTwoTone two-tone-color="red" v-if="record.no_answer_flag" />
          <CheckCircleTwoTone two-tone-color="#52c41a" v-else />
        </template>
        <template v-else-if="column.dataIndex === 'feedback'">
          <LikeTwoTone two-tone-color="#2870EA" v-if="record.feedback && record.feedback === 'like'"/>
          <DislikeTwoTone two-tone-color="red" v-if="record.feedback && record.feedback === 'dislike'" />
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <!-- <a-button class="text-xs" type="link" size="small" @click="editQa(record)">加入标准问答</a-button> -->
          <a @click="editQa(record)">加入标准问答</a>
        </template>
      </template>
      <template #expandColumnTitle>
        <span style="color: red">More</span>
      </template>
    </a-table>
  </div>
</template>
<script lang="ts" setup>
  import { createVNode, reactive, onMounted, ref, watch } from 'vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { Modal } from 'ant-design-vue';
  import { useUnbounded } from '@/utils/useTheme';
  import { storeToRefs } from 'pinia';
  import { useApikeyStore } from '@/store/apikey';
  import { getChatRecordPage, getChatRecordExport } from '@/api/chat_record';
  import { getReleaseList } from "@/api/release";
  import {getApiKeyListByCreator} from "@/api/apikeys"
  import EnterQaModal from './EnterQaModal.vue';
  import * as XLSX from 'xlsx'
  import { useAccountStore } from '@/store';
  import { searchResultHighlight } from '@/utils/helpers';

  import dayjs, { Dayjs } from 'dayjs';
  import { useRouter } from 'vue-router';

  type RangeValue = [Dayjs, Dayjs];

  const router = useRouter();
  const apikeyStore = useApikeyStore();
  const accountStore = useAccountStore();
  const scope = ref("1");

  const { getApikeyList } = apikeyStore;
  const { apikeyList } = storeToRefs(apikeyStore);
  const {account} = accountStore;

  useUnbounded();

  const qaModalVisible = ref(false);
  const editRecord = ref({});
  const username = ref(null);
  const creator = ref(null);
  const keyword = ref("");

  const searchApiKeyIds = ref([]);
  const searchReleaseId = ref(null);
  const answerStatus = ref(null);

  const pageSizeOptions = ref<string[]>(['10', '50', '100', '200', '500', '1000']);

  const searchDate = ref<[Dayjs, Dayjs]>();

  const ranges = {
    "昨天": [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')] as RangeValue,
    "今天": [dayjs(), dayjs()] as RangeValue,
    '近7天': [dayjs().subtract(6, 'day'), dayjs()] as RangeValue,
    '近30天': [dayjs().subtract(29, 'day'), dayjs()] as RangeValue,
    '近1年': [dayjs().subtract(12, 'month'), dayjs()] as RangeValue,
  }

  const disabledDate = (current: Dayjs) => {
    // if (!searchDate.value || (searchDate.value as any).length === 0) {
    //   return false;
    // }
    // const tooLate = current.diff(dayjs(), 'days') >= 0;
    // // console.log(current.diff(dayjs(), 'days'))
    // // console.log(current.diff(dayjs().subtract(29, 'day'), 'days'))
    // // const tooEarly = current.diff(dayjs().subtract(29, 'day'), 'days') < 0;
    // const tooLate = searchDate.value[1] && dayjs().diff(searchDate.value[1], 'days') <= 0;
    // // const tooEarly = searchDate.value[1] && searchDate.value[1].diff(current, 'days') > 1;
    return false;
  };

  function setQaModalVisible(visible: boolean) {
    qaModalVisible.value = visible;
  }

  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  function editQa(record) {
    if(existReleaseIds.value.length === 0){
      showAddQaConfirm()
      return
    }
    // 记录上的release_id是不已被删除
    if (record.release_id > 0 && !existReleaseIds.value.some((item) => item.id === record.release_id)){
      console.log("deleted: ", record.release_id)
      record.release_id = 0;
    }
    qaModalVisible.value = true;
    let tmp = {}
    tmp["from"] = 2
    tmp["question"] = `${record["user_message"]}`
    tmp["answer"] = `${record["assistant"]}`
    tmp["release_id"] = record.release_id > 0?`${record["release_id"]}`:existReleaseIds.value.length>0?`${existReleaseIds.value[0].id}`:null;
    tmp["source_release_id"] = record.release_id
    tmp["chat_id"] = record["id"]
    tmp["image_urls"] = []
    editRecord.value = tmp;
    console.log(editRecord.value);
  }

  const exportData = () => {
    const params = {
      start_date: searchDate.value?dayjs(searchDate.value[0]).format("YYYY-MM-DD"):"",
      end_date: searchDate.value?dayjs(searchDate.value[1]).format("YYYY-MM-DD"):"",
      apikey_ids: searchApiKeyIds.value,
      release_id: searchReleaseId.value,
      no_answer_flag: answerStatus.value
    }
    getChatRecordExport(params).then((response) => {
      console.log(response);
      const { data } = response;

      let head = {
        id: 'ID',
        user_message: '提问',
        assistant: '回答',
        chat_start_time: '时间',
        chat_user: '提问者',
        source: '来自',
        feedback: '用户反馈',
        input_source: '输入区分',
      }
      const results = data.map(item => {
        const obj = {}
        for (const k in item) {
          if (head[k]) {
            if (k === "chat_start_time"){
              obj[head[k]] = dayjs(item[k]).format('YYYY-MM-DD HH:mm')
            }else if (k === "source"){
              const text = parseInt(item[k])
              obj[head[k]] = text === 1?"对话":text === 2? "API调用":"网页"
            }else if (k === "input_source"){
              const text = parseInt(item[k])
              obj[head[k]] = text === 1?"文本":"语音"
            }else{
              obj[head[k]] = item[k]
            }
          }
        }
        return obj
      })
      // 创建工作表
      const dataExcel = XLSX.utils.json_to_sheet(results)
      // 创建工作簿
      const wb = XLSX.utils.book_new()
      // 将工作表放入工作簿中
      XLSX.utils.book_append_sheet(wb, dataExcel, '对话记录')
      // 生成文件并下载
      XLSX.writeFile(wb, `对话记录${dayjs().format('YYYY-MM-DD HH:mm')}.xlsx`)
    }).catch((e) => {
      console.error(e);
    }).finally(() => {
    });
  }

  // 列表当前页更改
  const handleTableChange = async (page, filters, sorter) => {
    console.log(page, sorter);
    localStorage.setItem('ChatRecordPageSize', JSON.stringify({pageSize: page.pageSize}));
    await search(page.current, page.pageSize, sorter.order);
  };

  const columns = ref<TableColumnsType>([
    {
      title: '序号',
      dataIndex: 'id',
      width: 80,
      key: 'id',
    },
    // {
    //   title: 'api key',
    //   dataIndex: 'id',
    // },
    {
      title: '提问',
      dataIndex: 'user_message',
      ellipsis: true,
      key: 'user_message',
    },
    {
      title: '回答',
      dataIndex: 'assistant',
      ellipsis: true,
      key: 'assistant',
    },
    {
      title: '回答状态',
      dataIndex: 'answer_status',
      key: 'answer_status',
      width: 80,
    },
    {
      title: '用户反馈',
      dataIndex: 'feedback',
      key: 'feedback',
      width: 80,
    },
    {
      title: '时间',
      dataIndex: 'chat_start_time',
      key: 'chat_start_time',
      width: 150,
      sorter: true,
      sortDirections: ['descend', 'ascend', 'descend'],
      defaultSortOrder: 'descend',
      showSorterTooltip: false
    },
    {
      title: '来自',
      dataIndex: 'source',
      key: 'source',
      width: 80,
    },
    {
      title: '提问者',
      dataIndex: 'chat_user',
      key: 'chat_user',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 120,
    },
  ]);

  const defaultPageSize = ref(
    localStorage.getItem('ChatRecordPageSize')? JSON.parse(localStorage.getItem('ChatRecordPageSize')).pageSize : 50
  )

  const search = async (current?, pageSize?, sorter?) => {
    if(pageSize === undefined){
      pageSize = defaultPageSize.value;
    }
    const params = {
      start_date: searchDate.value?dayjs(searchDate.value[0]).format("YYYY-MM-DD"):"",
      end_date: searchDate.value?dayjs(searchDate.value[1]).format("YYYY-MM-DD"):"",
      apikey_ids: searchApiKeyIds.value,
      release_id: searchReleaseId.value,
      no_answer_flag: answerStatus.value,
      creator: creator.value,
      scope: scope.value,
      keyword: keyword.value,
      page_number: current,
      page_size: pageSize,
      sorter: sorter
    }
    getChatRecordPage(params).then((response) => {
      console.log(response);
      const { data } = response;
      chatRecordPage.value = data;
    }).catch((e) => {
      console.error(e);
    }).finally(() => {
    });
  };

  const onSearch = async (searchValue: string) => {
    console.log('use value', searchValue);
    await refreshData();
  };

  const onSearchChange = async (searchValue: string) => {
    console.log('use value', searchValue);
    await onSearch('')
  };
  
  const refreshData = async () => {
    await search();
  }

  const onCalendarChange = (val: RangeValue) => {
    console.log(val);
    if(val === null || val[0] === null || val[1] === null){
      return;
    }
    searchDate.value = [dayjs(val[0]), dayjs(val[1])];
  };

  const handleApiKeyChange: SelectProps['onChange'] = value => {
    searchApiKeyIds.value = value;
  };

  const handleReleaseChange: SelectProps['onChange'] = value => {
    searchReleaseId.value = value;
  };

  const handleAnswerStatusChange: SelectProps['onChange'] = value => {
    answerStatus.value = value;
  };


  const showAddQaConfirm = () => {
    Modal.confirm({
      title: "未找到可用的应用项目",
      icon: createVNode(ExclamationCircleOutlined),
      content: '需要先创建应用项目后，再加入标准答案使用',
      okText: '去创建',
      cancelText: '取消',
      onOk() {
        console.log('Ok');
        router.push("/myapp/release");
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  const fetchApiKeyListByCreator = (creator) => {
    getApiKeyListByCreator(creator).then((res) => {
      const { data } = res;
      console.log(data);
      apiKeyIds.value = data?.map(item => {
        return {
          label: `${item.name}`,
          value: `${item.id}`,
        }
      })
      return data;
    });
  }

  const apiKeyIds = ref([]);

  const searchReleaseIds = ref([
    {
      label: "全部",
      value: null,
    },
    {
      label: "对话",
      value: 0,
    },
  ]);

  const answerStatusOptions = ref([
    {
      label: "全部",
      value: null,
    },
    {
      label: "找到答案",
      value: 0,
    },
    {
      label: "未找到答案",
      value: 1,
    },
  ]);

  const existReleaseIds = ref([]);

  watch(apikeyList, (val) => {
    apiKeyIds.value = val?.map(item => {
      return {
        label: `${item.name}`,
        value: `${item.id}`,
      }
    })
  })
  watch(searchDate, refreshData);
  watch(searchApiKeyIds, refreshData);
  watch(searchReleaseId, refreshData);
  watch(answerStatus, refreshData);

  watch(scope, (val) => {
    console.log(val);
    refreshData();
    if (val === '2'){
      columns.value = [
        {
          title: '序号',
          dataIndex: 'id',
          width: 80,
          key: 'id',
        },
        // {
        //   title: 'api key',
        //   dataIndex: 'id',
        // },
        {
          title: '提问',
          dataIndex: 'user_message',
          ellipsis: true,
          key: 'user_message',
        },
        {
          title: '回答',
          dataIndex: 'assistant',
          ellipsis: true,
          key: 'assistant',
        },
        {
          title: '回答状态',
          dataIndex: 'answer_status',
          key: 'answer_status',
          width: 80,
        },
        {
          title: '用户反馈',
          dataIndex: 'feedback',
          key: 'feedback',
          width: 80,
        },
        {
          title: '时间',
          dataIndex: 'chat_start_time',
          key: 'chat_start_time',
          width: 150,
          sorter: true,
          sortDirections: ['descend', 'ascend', 'descend'],
          defaultSortOrder: 'descend',
          showSorterTooltip: false
        },
        {
          title: '来自',
          dataIndex: 'source',
          key: 'source',
          width: 80,
        },
        {
          title: '提问者',
          dataIndex: 'chat_user',
          key: 'chat_user',
          width: 120,
          ellipsis: true,
        },
        {
          title: '账号',
          dataIndex: 'owner',
          key: 'owner',
          width: 80,
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          width: 120,
        },
      ]
    }else{
      columns.value = [
        {
          title: '序号',
          dataIndex: 'id',
          width: 80,
          key: 'id',
        },
        // {
        //   title: 'api key',
        //   dataIndex: 'id',
        // },
        {
          title: '提问',
          dataIndex: 'user_message',
          ellipsis: true,
          key: 'user_message',
        },
        {
          title: '回答',
          dataIndex: 'assistant',
          ellipsis: true,
          key: 'assistant',
        },
        {
          title: '回答状态',
          dataIndex: 'answer_status',
          key: 'answer_status',
          width: 80,
        },
        {
          title: '用户反馈',
          dataIndex: 'feedback',
          key: 'feedback',
          width: 80,
        },
        {
          title: '时间',
          dataIndex: 'chat_start_time',
          key: 'chat_start_time',
          width: 150,
          sorter: true,
          sortDirections: ['descend', 'ascend', 'descend'],
          defaultSortOrder: 'descend',
          showSorterTooltip: false
        },
        {
          title: '来自',
          dataIndex: 'source',
          key: 'source',
          width: 80,
        },
        {
          title: '提问者',
          dataIndex: 'chat_user',
          key: 'chat_user',
          width: 120,
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          width: 120,
        },
      ]
    }
  })

  const chatRecordPage = ref({});

  onMounted(() => {
    const queryCreator = router.currentRoute.value.query?.creator;
    if(queryCreator){
      username.value = router.currentRoute.value.query?.username;
      creator.value = queryCreator;
      fetchApiKeyListByCreator(queryCreator);
    }else{
      getApikeyList();
    }
    getReleaseList().then((response) => {
      console.log(response);
      const { data } = response;
      existReleaseIds.value = data;
      data?.forEach(item => {
        searchReleaseIds.value.push({
          label: `${item.name}`,
          value: `${item.id}`,
        });
      })
    }).catch((e) => {
      console.error(e);
    });
    searchDate.value = [dayjs().subtract(6, 'day'), dayjs(),];
  });
</script>

<style lang="less">
.limit-popover-width {
  max-width: 50%;
  word-wrap: break-word;
}
</style>

<style lang="scss" scoped>
:deep(img) {
  max-width: 10%;
  // cursor: pointer;
}
</style>
