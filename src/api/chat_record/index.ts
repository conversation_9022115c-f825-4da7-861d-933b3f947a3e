import { Response } from '@/types';
import http from "@/store/http";

export interface ChatRecord {
  id?: number;
  count?: number;
  has_next?: boolean;
  has_previous?: boolean;
  page_number?: number;
  page_size?: number;
  results?: Array<{}>;
  total_pages?: number;
  start_date?: String,
  end_date?: String,
  apikey_ids?: Array<String>,
}

export const getChatRecordPage = (params: ChatRecord) => 
  http.request<any, Response<any>>("/app/chat/record/page", 'GET', params)


export const getChatRecordExport = (params: ChatRecord) => 
  http.request<any, Response<any>>("/app/chat/record/export", 'GET', params)
