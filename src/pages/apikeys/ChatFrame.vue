<template>
  <div class="chat-frame">
    <div class="title-bar">
      <div class="title"></div>
      <div class="actions">
        <a-button 
          type="text" 
          class="config-icon" 
          :class="{ 'config-icon--active': showStyleSettingsModal }"
          :style="getButtonStyle"
          @click="showStyleSettingsModal = true"
        >
          <template #icon><BgColorsOutlined class="settings-icon" /></template>
        </a-button>
      </div>
    </div>
    <iframe
      v-if="showIframe"
      id="chatDebug"
      :src="iframeUrl"
      frameborder="0"
      allow="clipboard-read; clipboard-write; microphone *;"
      style="width: 100%; height: calc(100% - 1px);"
    ></iframe> 
    
    <!-- Style Settings Modal -->
    <a-modal
      v-model:visible="showStyleSettingsModal"
      title="界面风格设置"
      width="90%"
      :footer="null"
      :mask-style="{ backgroundColor: 'rgba(0, 0, 0, 0.01)' }"
    >
      <style-settings 
        :model-value="formData"
        @update:model-value="updateStyleSettings"
        :save-settings="saveStyleSettings"
      />
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, computed } from 'vue';
import { BgColorsOutlined } from '@ant-design/icons-vue';
import StyleSettings from '@/components/setting/StyleSettings.vue';
import { message } from 'ant-design-vue';
import { getReleaseChatPageSetting, setReleaseChatPageSetting } from '@/api/release';

const iframeUrl = import.meta.env.VITE_CHATBOT + "?debug=1";
const showStyleSettingsModal = ref(false);
const showIframe = ref(true);

// Default formData with id as required by StyleSettings component
const formData = ref({
  id: '00000000',
  style_preset: '',
  style: {
    primaryColor: '#2B59E6'
  } as { primaryColor?: string },
  background_preset: '',
  background_type: '',
  background_image_url: '',
  background_css: '',
  font_settings: {}
});

// Load settings when modal is opened
const loadPageSettings = async () => {
  try {
    const response = await getReleaseChatPageSetting({
      release_id: formData.value.id
    });
    
    if (response.code === 0 && response.data && response.data.setting) {
      const setting = response.data.setting;
      formData.value = {
        ...formData.value,
        font_settings: setting.font_settings || {},
        background_preset: setting.background_preset || '',
        background_type: setting.background_type || '',
        background_image_url: setting.background_image_url || '',
        background_css: setting.background_css || '',
        style_preset: setting.style_preset || '',
        style: setting.style || {}
      };
    }
  } catch (error) {
    console.error('Failed to get page settings:', error);
    message.error('获取页面设置失败');
  }
};

// Save settings when confirmed in StyleSettings
const saveStyleSettings = async (updatedData) => {
  try {
    const response = await setReleaseChatPageSetting({
      release_id: formData.value.id,
      setting: {
        font_settings: updatedData.font_settings,
        background_preset: updatedData.background_preset,
        background_type: updatedData.background_type,
        background_image_url: updatedData.background_image_url,
        background_css: updatedData.background_css,
        style_preset: updatedData.style_preset,
        style: updatedData.style
      }
    });
    
    if (response.code === 0) {
      message.success('设置已保存');
      // 重新加载iframe
      showIframe.value = false;
      setTimeout(() => {
        showIframe.value = true;
      }, 1000);
    } else {
      message.error('保存失败');
    }
    
    return response;
  } catch (error) {
    console.error('Failed to save page settings:', error);
    message.error('保存设置失败');
    throw error;
  }
};

// Update local formData state
const updateStyleSettings = (newData) => {
  formData.value = {
    ...formData.value,
    ...newData
  };
};

// Watch for modal visibility changes to load settings when opened
watch(showStyleSettingsModal, (newValue) => {
  if (newValue) {
    loadPageSettings();
  }
});

// Helper function to convert hex to HSL and increase saturation
const hexToHsl = (hex: string) => {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0, l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
};

const hslToHex = (h: number, s: number, l: number) => {
  l /= 100;
  const a = s * Math.min(l, 1 - l) / 100;
  const f = (n: number) => {
    const k = (n + h / 30) % 12;
    const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
    return Math.round(255 * color).toString(16).padStart(2, '0');
  };
  return `#${f(0)}${f(8)}${f(4)}`;
};

// Computed style for button with enhanced colors when active
const getButtonStyle = computed(() => {
  const baseColor = formData.value.style?.primaryColor || '#2B59E6';
  
  if (showStyleSettingsModal.value) {
    // When active, increase saturation and adjust lightness for better visibility
    const hsl = hexToHsl(baseColor);
    const enhancedColor = hslToHex(
      hsl.h, 
      Math.min(100, hsl.s + 20), // Increase saturation by 20%
      Math.max(20, Math.min(80, hsl.l > 50 ? hsl.l - 10 : hsl.l + 15)) // Adjust lightness
    );
    return { backgroundColor: enhancedColor };
  }
  
  return { backgroundColor: baseColor };
});

onMounted(() => {
  loadPageSettings();
});
</script>
<style lang="less" scoped>
.chat-frame {
  width: calc(100% + 24px);
  height: calc(100% + 12px);
  display: flex;
  flex-direction: column;
  margin-left: -12px!important;
  margin-right: -12px!important;
  margin-top: -12px!important;
}

.title-bar {
  height: 1px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
}

.title {
  font-weight: 500;
  font-size: 16px;
}

.actions {
  display: flex;
  align-items: center;
}

.config-icon {
  margin: 120px 5px 0 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  opacity: 0;
  animation: fadeIn 1s ease-in forwards;
  animation-delay: 2s;
  position: relative;
  z-index: 1001;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  &--active {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    transform: scale(1.02);
    border: 1px solid rgba(255, 255, 255, 0.4);
    
    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
      border-radius: 5px;
      z-index: -1;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.settings-icon {
  color: white;
  font-size: 20px;
}






</style>
