<template>
  <EnterQaModal
    :initQaEditRecord="qaEditRecord"
    :qaModalVisible="qaModalVisible"
    :releaseIds="tableData.map(item => {
      return {
        label: `${item.name}`,
        value: `${item.id}`,
      }
    })"
    @setQaModalVisible="setQaModalVisible"
  />
  <div class="font-semibold text-lg">
    <div class="flex justify-between">
      <h1>应用项目</h1>
    </div>
    <div class="text-subtext text-sm flex justify-between items-center">
      <div v-if="account.is_superuser">
        <a-radio-group v-model:value="fabuRadioValue" style="margin-right: 10px;">
          <a-radio-button value="1">我创建的</a-radio-button>
          <a-radio-button value="2">其他创建</a-radio-button>
        </a-radio-group>
      </div>
      <span style="font-size:14px;">&nbsp;</span>
      <div>
        <!-- <a-button type="link" @click="setRunModalVisible(true)">体验</a-button> -->
        <a-button type="primary" @click="createRelease('workflow-chat')">创建对话式工作流</a-button>
        <a-button type="primary" @click="createRelease('workflow')" style="margin-left: 10px;">创建流程式工作流</a-button>
        <a-button type="primary" @click="createRelease('basic-chat')" style="margin-left: 10px;">创建对话项目</a-button>
        <a-input-search
          v-model:value="keyword"
          placeholder="根据名称搜索"
          style="width: 250px;margin-left: 5px;"
          allowClear
          @search="onSearch"
          @change="onSearchChange"
        />
      </div>
    </div>

<!--    <div class="bg-container p-base rounded-b-lg rounded-tr-lg pt-8 flex items-end justify-end" style="background-color: transparent!important;">-->
<!--      <a-select-->
<!--          style="width: 15%;margin-right: 10px;"-->
<!--          placeholder="应用类型"-->
<!--          :options="searchReleaseIds"-->
<!--          allowClear-->
<!--          @change="handleReleaseChange"-->
<!--      ></a-select>-->
<!--    </div>-->

    <div style="margin-top: 10px;">
      <a-table
        v-if="account.is_superuser && fabuRadioValue === '1' || !account.is_superuser"
        :columns="tableColumns"
        :data-source="tableData"
        :pagination="false"
        :defaultExpandAllRows="false"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column.dataIndex === 'operation'">
            <!-- <a style="font-weight: normal;" @click="updateApp(record)">编辑</a>
            <a-divider type="vertical" /> -->
            <a style="font-weight: normal;" @click="edit(record)">编排</a>
            <a-divider type="vertical"/>
            <a style="font-weight: normal;" @click="appCopy(record)">复制</a>
            <a-divider type="vertical"/>
            <a style="font-weight: normal;" @click="showWorkflowLogDrawer(record)" v-if="[2, 3].includes(record.mode)">日志</a>
            <a-divider type="vertical" v-if="[2, 3].includes(record.mode)"/>
            <a-upload
              accept=".yml"
              :before-upload="beforeUpload"
              :show-upload-list="false"
            >
              <a style="font-weight: normal;" @click="selectImportFile(record)" v-if="[2, 3].includes(record.mode)">导入</a>
            </a-upload>
            <a-divider type="vertical" v-if="[2, 3].includes(record.mode)"/>
            <a style="font-weight: normal;" @click="exportWorkflow(record)" v-if="[2, 3].includes(record.mode)">导出</a>
            <a-divider type="vertical" v-if="[2, 3].includes(record.mode)"/>
            <a style="font-weight: normal;" @click="appAssign(record)" v-if="account.is_superuser">分发</a>
            <a-divider type="vertical" v-if="account.is_superuser"/>
            <a style="font-weight: normal;" v-if="record.mode == 2" @click="publishToQingxiaoda(record)">{{ record.qingxiaoda_published ? '更改发布清小搭' : '发布到清小搭' }}</a>
            <a-divider type="vertical" v-if="record.mode == 2"/>
            <a style="color: red;font-weight: normal;" @click="showDeleteConfirm(record)">删除</a>
          </template>
          <template v-else-if="column.dataIndex === 'name'">
            <div v-html="searchResultHighlight(text , keyword)" />
          </template>
          <template v-else-if="column.dataIndex === 'update_time'">
            {{ text?dayjs(text).format("YYYY-MM-DD HH:mm"):'-' }}
          </template>
          <template v-else-if="column.dataIndex === 'mode'">
            {{ text === 1?'对话服务(基础编排)':text === 2?'对话服务(工作流编排)':'工作流' }}
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <div class="grid grid-cols-12 gap-6">
            <a-list size="large"
              :grid="{ gutter: 4, column: 2}"
              :data-source="listData"
              class="card col-span-12"
              >
              <template #renderItem="{ item }">
                <a-list-item key="item.title">
                  <a-card>
                    <template #actions>
                      <span v-for="{ icon, text, code } in item.actions" :key="icon">
                        <a target="_blank" @click="releaseAction(code, record)">
                          <component :is="icon" style="margin-right: 4px" />
                          {{ text }}
                        </a>
                      </span>
                    </template>

                    <a-list-item-meta :description="item.description">
                      <template #title>
                        {{ item.title }}
                      </template>
                      <template #avatar>
                        <a-avatar :style="{'background-color': item.avatar_bg}">
                          <component :is="item.avatar" />
                        </a-avatar>
                      </template>
                      <!-- <template #avatar><component :is="item.avatar" color="red"/></template> -->
                    </a-list-item-meta>
                    {{ item.content }}
                  </a-card>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </template>
        <template #expandColumnTitle>
          <span style="color: red">More</span>
        </template>
      </a-table>
      <a-table
        v-if="account.is_superuser && fabuRadioValue === '2'"
        :columns="otherTableColumns"
        :data-source="otherTableData"
        :pagination="false"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column.dataIndex === 'operation'">
            <!-- <a style="font-weight: normal;" @click="updateApp(record)">编辑</a>
            <a-divider type="vertical" /> -->
            <a style="font-weight: normal;" @click="edit(record)">编排</a>
            <a-divider type="vertical"/>
            <a style="font-weight: normal;" @click="appCopy(record)">复制</a>
            <a-divider type="vertical"/>
            <a style="font-weight: normal;" @click="showWorkflowLogDrawer(record)" v-if="[2, 3].includes(record.mode)">日志</a>
            <a-divider type="vertical" v-if="[2, 3].includes(record.mode)"/>
            <a-upload
              accept=".yml"
              :before-upload="beforeUpload"
              :show-upload-list="false"
            >
              <a style="font-weight: normal;" @click="selectImportFile(record)" v-if="[2, 3].includes(record.mode)">导入</a>
            </a-upload>
            <a-divider type="vertical" v-if="[2, 3].includes(record.mode)"/>
            <a style="font-weight: normal;" @click="exportWorkflow(record)" v-if="[2, 3].includes(record.mode)">导出</a>
            <a-divider type="vertical" v-if="[2, 3].includes(record.mode)"/>
            <!-- <a style="font-weight: normal;" @click="appAssign(record)">分发</a>
            <a-divider type="vertical"/> -->
            <a style="font-weight: normal;" v-if="record.mode == 2" @click="publishToQingxiaoda(record)">{{ record.qingxiaoda_published ? '更改发布清小搭' : '发布到清小搭' }}</a>
            <a-divider type="vertical" v-if="record.mode == 2"/>
            <a style="color: red;font-weight: normal;" @click="showDeleteConfirm(record)">清空</a>
          </template>
          <template v-else-if="column.dataIndex === 'update_time'">
            {{ text?dayjs(text).format("YYYY-MM-DD HH:mm"):'-' }}
          </template>
          <template v-else-if="column.dataIndex === 'name'">
            <div v-html="searchResultHighlight(text , keyword)" />
          </template>
          <template v-else-if="column.dataIndex === 'mode'">
            {{ text === 1?'对话服务(基础编排)':text === 2?'对话服务(工作流编排)':'工作流' }}
          </template>
        </template>
      </a-table>
    </div>
    <ReleaseModal
      :formData="editRecord"
      :showFormModalVisible="showFormModalVisible"
      :fabuRadioValue="fabuRadioValue"
      :isWorkflowMode="isWorkflowMode"
      @setEditRecord="setEditRecord"
      @setShowFormModalVisible="setShowFormModalVisible"
      v-if="showFormModalVisible"
    />
    <a-modal
      :visible=showCopyLinkVisible
      :title="`复制链接`"
      :footer="false"
      @cancel="changeShowCopyLinkVisible(false)"
    >
      <div>
        <p style="padding-bottom: 1px"><h4>URL链接</h4></p>
        <p>{{ chatBotUrl }}<a-button type="link" @click="copyUrl(chatBotUrl)">复制</a-button></p>
        <p><h4>二维码</h4></p>
        <p style="padding-bottom: 1px">
          <QRCodeVue3
            :key="chatBotUrl"
            :width="150"
            :height="150"
            :value="chatBotUrl"
            :qrOptions="{ typeNumber: 0, mode: 'Byte', errorCorrectionLevel: 'L' }"
            :imageOptions="{ hideBackgroundDots: true, imageSize: 0.4, margin: 0 }"
            :dotsOptions="{
              type: 'square',
              color: '#000',
              gradient: {
                type: 'linear',
                rotation: 0,
                colorStops: [
                  { offset: 0, color: '#000000' },
                  { offset: 1, color: '#000000' },
                ],
              },
            }"
            :backgroundOptions="{ color: '#ffffff' }"
            :cornersSquareOptions="{ type: 'square', color: '#000000' }"
            :cornersDotOptions="{ type: undefined, color: '#000000' }"
            fileExt="png"
            ButtonName=""
            :download="true"
            downloadButton="download-button"
            :downloadOptions="{ name: '应用项目-' + releaseNameForDownload + '-QRCode', extension: 'png' }"
          />
        </p>
      </div>
    </a-modal>
    <a-modal
      :visible=showApiKeyVisible
      :title="`查看秘钥`"
      :footer="false"
      @cancel="changeShowApiKeyVisible(false)"
    >
      <div>
        <!-- <p>查看秘钥(ApiKey)：{{ useApiKey }}<a-button type="link" @click="copyUrl(useApiKey)">复制</a-button></p> -->
        <p>应用唯一标识：{{ useIdentifier }}<a-button type="link" @click="copyUrl(useIdentifier)">复制</a-button></p>
      </div>
    </a-modal>
    <a-drawer title="JS嵌入"
      :closable="false"
      size="large"
      :visible="showJsEmbVisible"
      :destroyOnClose="true"
      @close="changeJsEmbVisible(false)"
    >
      <template #extra>
        <a-button type="text" @click="changeJsEmbVisible(false)"><close-outlined /></a-button>
      </template>
      <p>JS代码</p>
      <p>对话机器人代码，请将此iframe添加到您的html代码中</p>
      <p><MarkdownIt :model-value="jsEmbContent"></MarkdownIt></p>
      <p>添加聊天气泡，请复制添加到您的html中</p>
      <p><MarkdownIt :model-value="jsEmbContent1"></MarkdownIt></p>
    </a-drawer>
    <a-drawer :title="`日志记录`"
      :closable="false"
      width="90%"
      :visible="showLogVisible"
      :destroyOnClose="true"
      @close="changeShowLogVisible(false)"
      :bodyStyle="{ 'overflow-y': 'hidden' }"
    >
      <template #extra>
        <a-button type="text" @click="changeShowLogVisible(false)"><close-outlined /></a-button>
      </template>
      <iframe
        id="workflowLog"
        :src="workflowRunLogUrl"
        allow="clipboard-read; clipboard-write; microphone *; autoplay;"
        style="width: 100%; height: 100%; border: none;"
      ></iframe>
    </a-drawer>
    <a-modal
      :title="appModalTitle"
      :visible=showCreateAppVisible
      :okButtonProps="{ loading }"
      width="540px"
      ok-text="确认" cancel-text="取消"
      @ok="submit"
      @cancel="showCreateAppVisible = false"
    >
      <div>
        <a-form
          ref="form"
          :model="formData"
          :labelCol="{ span: 5 }"
          :wrapperCol="{ span: 16, offset: 1 }"
        >
<!--          <a-form-item required name="appMode" label="应用类型">-->
<!--            <a-radio-group v-model:value="formData.appMode" :disabled="formData.id > 0">-->
<!--              <a-radio-button value="1">-->
<!--                <div style="display: flex;align-items: center;justify-content: center;" :style="{'color': formData.appMode == 1 ? '#1890ff' : ''}">-->
<!--                  <img src="@/assets/icon/liaotianjiqiren.png" style="width: 20px;height: 20px;margin-right: 10px;"/>-->
<!--                  聊天助手-->
<!--                </div>-->
<!--              </a-radio-button>-->
<!--              <a-radio-button value="2">-->
<!--                <div style="display: flex;align-items: center;justify-content: center;" :style="{'color': formData.appMode == 2 ? '#1890ff' : ''}">-->
<!--                  <img src="@/assets/icon/gongzuoliu.png" style="width: 20px;height: 20px;margin-right: 10px;"/>-->
<!--                  工作流-->
<!--                </div>-->
<!--              </a-radio-button>-->
<!--            </a-radio-group>-->
<!--          </a-form-item>-->
          <!-- <a-form-item name="chatOrchestration" label="编排方法" v-if="formData.appMode === '1'">
            <a-radio-group v-model:value="formData.chatOrchestration" :disabled="formData.id > 0">
              <a-radio-button value="1">
                <div style="display: flex;align-items: center;justify-content: center;" :style="{'color': formData.chatOrchestration == 1 ? '#1890ff' : ''}">
                  <img src="@/assets/icon/jichushezhi.png" style="width: 20px;height: 20px;margin-right: 1px;"/>
                  基础编排
                  <span style="font-size: 10px;color: #999;margin-left: 2px;">新手适用</span>
                </div>
              </a-radio-button>
              <a-radio-button value="2">
                <div style="display: flex;align-items: center;justify-content: center;" :style="{'color': formData.chatOrchestration == 2 ? '#1890ff' : ''}">
                  <img src="@/assets/icon/gongzuoliu2.png" style="width: 20px;height: 20px;margin-right: 1px;"/>
                  工作流编排
                  <span style="font-size: 10px;color: #999;margin-left: 2px;">进阶用户</span>
                </div>
              </a-radio-button>
            </a-radio-group>
          </a-form-item> -->
          <a-form-item required name="name" label="应用名称"
            :rules="[{ required: true, message: '请输入应用名称' }]"
          >
            <a-input v-model:value="formData.name" placeholder="应用名称" show-count :maxlength="50" />
          </a-form-item>
          <a-form-item required name="apikey_id" label="选择Apikey"
            :rules="[{ required: true, message: '请选择要使用的Apikey' }]"
          >
            <a-select
              placeholder="选择要使用的Apikey"
              v-model:value="formData.apikey_id"
              :options="apikeyList.map(item => {
                  return {
                    label: `${item.name}`,
                    value: `${item.id}`,
                  }
                })"
            ></a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
    <a-modal
      title="复制应用"
      :visible=showCopyAppVisible
      :okButtonProps="{ loading }"
      width="540px"
      ok-text="确认" cancel-text="取消"
      @ok="appCopySubmit"
      @cancel="showCopyAppVisible = false"
    >
      <div>
        <a-form
          ref="formCopy"
          :model="formDataCopy"
          :labelCol="{ span: 5 }"
          :wrapperCol="{ span: 16, offset: 1 }"
        >
          <a-form-item required name="name" label="应用名称"
            :rules="[{ required: true, message: '请输入应用名称' }]"
          >
            <a-input v-model:value="formDataCopy.name" placeholder="应用名称" show-count :maxlength="50" />
          </a-form-item>
          <a-form-item name="team_id" label="选择团队">
            <a-select
              v-model:value="formDataCopy.team_id"
              placeholder="请选择团队（可选）"
              :loading="teamLoading"
              :options="teamList"
              allowClear
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
    <a-modal
      :title="isUpdateMode ? '更改发布清小搭' : '发布到清小搭'"
      :visible=showPublishToQingxiaodaVisible
      :okButtonProps="{ loading: publishLoading }"
      width="540px"
      :ok-text="isUpdateMode ? '更新' : '发布'" cancel-text="取消"
      @ok="publishToQingxiaodaSubmit"
      @cancel="handlePublishModalCancel"
    >
      <div>
        <a-form
          ref="publishForm"
          :model="publishFormData"
          :labelCol="{ span: 5 }"
          :wrapperCol="{ span: 16, offset: 1 }"
        >
          <a-form-item required name="name" label="名称"
            :rules="[{ required: true, message: '请输入名称' }]"
          >
            <a-input v-model:value="publishFormData.name" placeholder="请输入名称" show-count :maxlength="100" />
          </a-form-item>
          <a-form-item required name="icon" label="图标"
            :rules="[{ required: true, message: '请上传图标' }]"
          >
            <a-upload list-type="picture-card" accept=".png,.jpg,.jpeg,.webp,.gif"
                :show-upload-list="false"
                :customRequest="customRequest"
                :before-upload="beforeUploadIcon"
                @change="handleIconChange"
              >
              <div>
                <img v-if="publishFormData.icon" :src="publishFormData.icon" alt="icon" style="width: 100px;height: 100px;"/>
                <div v-else>
                  <loading-outlined v-if="publishLoading"></loading-outlined>
                  <plus-outlined v-else></plus-outlined>
                  <div class="ant-upload-text">上传图标</div>
                </div>
              </div>
            </a-upload>
          </a-form-item>
          <a-form-item required name="description" label="描述"
            :rules="[{ required: true, message: '请输入描述' }]"
          >
            <a-textarea v-model:value="publishFormData.description" placeholder="请输入描述" show-count :maxlength="500" :rows="4" />
          </a-form-item>
          <a-form-item required name="labels" label="标签"
            :rules="[{ required: true, message: '请添加至少一个标签' }]"
          >
            <div>
              <template v-for="(tag, index) in tags" :key="tag">
                <a-tag
                  :closable="true"
                  @close="handleClose(tag)"
                  style="margin-bottom: 8px;"
                >
                  {{ tag }}
                </a-tag>
              </template>
              <a-input
                v-if="inputVisible"
                ref="inputRef"
                v-model:value="inputValue"
                type="text"
                size="small"
                style="width: 78px; margin-bottom: 8px;"
                @blur="handleInputConfirm"
                @keyup.enter="handleInputConfirm"
              />
              <a-tag v-else style="background: #fff; border-style: dashed; margin-bottom: 8px;" @click="showInput">
                <plus-outlined />
                添加标签
              </a-tag>
            </div>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
  <template>
    <ReleaseAssignManage 
      :appAssignDrawer="appAssignDrawer" 
      :record="appAssignRecord"
      @setAppAssignDrawer="setAppAssignDrawer"
      v-if="account.is_superuser"
    />
  </template>
</template>
<script lang="ts" setup>
import { createVNode, computed, ref, onMounted, onBeforeMount, watch, nextTick} from 'vue';
import { storeToRefs } from 'pinia';
import { CopyOutlined, EyeOutlined, FileTextOutlined,
  DesktopOutlined, SelectOutlined, BranchesOutlined, ExclamationCircleOutlined, ReconciliationOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { 
  getReleaseList, 
  getAllReleaseList, 
  deleteRelease, 
  saveRelease, 
  copyReleaseApp,
  exportReleaseWorkflow,
  importReleaseWorkflow,
  publishToQingxiaoda as publishToQingxiaodaApi,
  updatePublishToQingxiaoda as updatePublishToQingxiaodaApi,
  getQingxiaodaPublish
} from "@/api/release";
import { getTeamList } from "@/api/team";
import ReleaseModal from './ReleaseModal.vue';
import EnterQaModal from './EnterQaModal.vue';
import ReleaseAssignManage from './ReleaseAssignManage.vue';
import { Modal, FormInstance } from 'ant-design-vue';
import dayjs from 'dayjs';

import { message } from 'ant-design-vue';
import { useClipboard } from '@vueuse/core'
import MarkdownIt from '@/components/markdown/markdown-it.vue';
import QRCodeVue3 from "qrcode-vue3";
import { useAccountStore } from '@/store';
import { useApikeyStore } from '@/store/apikey';
import { useBaseStore } from '@/store/base';
import { searchResultHighlight } from '@/utils/helpers';
import OSS from 'ali-oss';
import http from '@/store/http';
import { OssStsTokenProps } from '@/store/thirdparty';
import { Response } from '@/types';
import { v4 as uuidv4 } from 'uuid';
import type { UploadProps, UploadChangeParam } from 'ant-design-vue';

const baseStore = useBaseStore();
const { headerAddWorkflowActive, headerAddChatbotActive } = storeToRefs(baseStore);


async function registerCopyHandel(e) {

  const { copy } = useClipboard({ legacy: true })

  if (e?.target?.className === '__code-block-copy-button__') {
    let text = e.target?.parentElement?.innerText ?? ''
    // 删除复制按钮自身的内容
    text = text.slice(5)
    await copy(text)
    message.success('复制成功')
  }
}

const apikeyStore = useApikeyStore();
const accountStore = useAccountStore();
const {account} = accountStore;

const { apikeyList } = storeToRefs(apikeyStore);

const appModalTitle = ref("创建应用");
const loading = ref(false);
const fabuRadioValue = ref('1');
const qaModalVisible = ref(false);
const showFormModalVisible = ref(false);
const showCreateAppVisible = ref(false);
const showCopyLinkVisible = ref(false);
const showApiKeyVisible = ref(false);
const showJsEmbVisible = ref(false);
const showLogVisible = ref(false);
const showCopyAppVisible = ref(false);
const appAssignDrawer = ref(false);
const appAssignRecord = ref({});
const showPublishToQingxiaodaVisible = ref(false);
const publishLoading = ref(false);
const isUpdateMode = ref(false);

const keyword = ref("");
const qaEditRecord = ref({});
const editRecord = ref({});
const readRecord = ref({});
const useApiKey = ref("");
const useIdentifier = ref("");
const currClickDid = ref("");
const currImportReleaseId = ref(0);
const releaseNameForDownload = ref("");
const { copy } = useClipboard({ legacy: true })
let ossStsToken = ref<OssStsTokenProps>();
let fileDict = ref({});
const baseFilePath = ref<string>("file/release/qingxiaoda/icon");

const chatBotUrl = computed(() => {
  const url = import.meta.env.VITE_CHATBOT;
  if([1, 2].includes(readRecord.value.mode)){
    return url + "/" + useIdentifier.value;
  }else{
    const base = url.replaceAll("/q/chat", "")
    return base + `/f/workflow/${readRecord.value.code}`;
  }
});

const workflowRunLogUrl = computed(() => {
  const url = import.meta.env.VITE_CHATBOT;
  const base = url.replaceAll("/q/chat", "")
  return base + `/f/app/${currClickDid.value}/logs`;
});

const jsEmbContent = ref("");
const jsEmbContent1 = ref("");

const isWorkflowMode = computed(() => {
  return editRecord.value && editRecord.value.mode == 3;
});

function setShowFormModalVisible(visible: boolean, refresh: boolean) {
  console.log(visible, refresh);
  showFormModalVisible.value = visible;
  if (refresh === true){
    refreshReleaseList();
  }
}

function setEditRecord(record){
  editRecord.value = record;
}

function setAppAssignDrawer(visible){
  appAssignDrawer.value = visible;
}

function openQaModalVisible(record) {
  let tmp = {}
  tmp["from"] = 1
  tmp["release_id"] = `${record.id}`
  tmp["source_release_id"] = record.id
  tmp["question"] = ''
  tmp["answer"] = ''
  qaEditRecord.value = tmp
  setQaModalVisible(true);
}

function setQaModalVisible(visible: boolean) {
  qaModalVisible.value = visible;
}

watch(headerAddWorkflowActive, (val) => {
  if (val === true) {
    createRelease('workflow');
    headerAddWorkflowActive.value = false;
  }
})
watch(headerAddChatbotActive, (val) => {
  if (val === true) {
    createRelease('chatbot');
    headerAddChatbotActive.value = false;
  }
})
watch(fabuRadioValue, (val) => {
  if (val === '1') {
    refreshReleaseList();
  }else{
    getOtherReleaseList();
  }
})

function createRelease(type) {
  appModalTitle.value = type === 'workflow' ? "创建工作流": type === 'basic-chat'?"创建基础编排对话":"创建工作流编排对话";
  
  // 找到name为"default"的apikey的id
  const defaultApikey = apikeyList.value.find(item => item.name === 'default');
  const defaultApikeyId = defaultApikey ? `${defaultApikey.id}` : null;
  
  let tmp = {}
  tmp["id"] = 0
  tmp["name"] = ""
  tmp["apikey_id"] = defaultApikeyId
  tmp["welcome"] = ""
  editRecord.value = tmp;

  let chatOrchestration = type === 'basic-chat'?"1":"2";

  formData.value = {
    'id': 0,
    'name': '',
    'mode': 0,
    'appMode': type === 'workflow' ? '2' : '1',
    'chatOrchestration': chatOrchestration,
    'apikey_id': defaultApikeyId,
  };
  // showFormModalVisible.value = true;
  showCreateAppVisible.value = true;

}

function changeJsEmbVisible(visible){
  showJsEmbVisible.value = visible;
}

function changeShowLogVisible(visible){
  showLogVisible.value = visible;
}

async function copyUrl(url){
  await copy(url)
  message.success("复制成功")
}

const previewRun = (url) => {
  window.open(url, '_blank')
}

function changeShowCopyLinkVisible(visible){
  showCopyLinkVisible.value = visible;
}

function changeShowApiKeyVisible(visible){
  showApiKeyVisible.value = visible;
}

function updateApp(record){
  appModalTitle.value = "编辑应用";
  formData.value.id = record.id;
  formData.value.name = record.name;
  formData.value.appMode = record.mode === 3?'2':'1';
  formData.value.chatOrchestration = record.mode === 2?'2':'1';
  if (!record.api_key){
    formData.value.apikey_id = null
  }else{
    const apikey_ids = apikeyList.value.map(item => item.id)
    if(apikey_ids.includes(record["apikey_id"])){
      formData.value.apikey_id = `${record["apikey_id"]}`
    }else{
      formData.value.apikey_id = null
    }
  }
  showCreateAppVisible.value = true;
}

function edit(record) {
  showFormModalVisible.value = true;
    if (!record.api_key){
      record["apikey_id"] = null
    }else{
      record["apikey_id"] = `${record["apikey_id"]}`
    }
    if(!record.chatbot_quiescent_page_refresh_interval){
      record["chatbot_quiescent_page_refresh_interval"] = null
    }
    editRecord.value = record;
}

function generateRandomString(len) {
  var chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
  var randomStr = "";
  for (var i = 0; i <= len; i++) {
    var randomNumber = Math.floor(Math.random() * chars.length);
    randomStr += chars.substring(randomNumber, randomNumber +1);
  }
  return randomStr
}

function appAssign(record){
  appAssignDrawer.value = true;
  appAssignRecord.value = record;
}

async function getTeamListData(){
  teamLoading.value = true;
  try {
    const response = await getTeamList({});
    const { code, data } = response;
    if (code === 0) {
      teamList.value = data.map(item => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (e) {
    console.error('获取团队列表失败:', e);
  } finally {
    teamLoading.value = false;
  }
}

function appCopy(record){
  const randomStr = generateRandomString(4)
  showCopyAppVisible.value = true;
  formDataCopy.value.id = record.id;
  formDataCopy.value.name = `${record.name}_copy_${randomStr}`;
  formDataCopy.value.team_id = null;
  // 获取团队列表
  getTeamListData();
}

function appCopySubmit(){
  formCopy.value
    ?.validate()
    .then(() => {
      const params = {
        release_id: formDataCopy.value.id,
        name: formDataCopy.value.name,
        team_id: formDataCopy.value.team_id
      }
      copyReleaseApp(params).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          message.success(`复制成功`);
          showCopyAppVisible.value = false;
          refreshReleaseList();
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

function remove(record) {
  console.log(record);
  const opt = fabuRadioValue.value === '2'?"清空":"删除";
  deleteRelease(record).then((response) => {
    console.log(response);
    const { code } = response;
    if (code === 0){
      message.success(`${opt}成功`)
    }else{
      message.error(`${opt}失败`)
    }
  }).catch((e) => {
    console.error(e);
    message.error(`${opt}失败`)
  }).finally(() => {
    if (fabuRadioValue.value === '2'){
      getOtherReleaseList();
    }else{
      refreshReleaseList();
    }
  });
}

const formData = ref({
  'id': 0,
  'name': '',
  'mode': 0,
  'appMode': '1',
  'chatOrchestration': '1',
  'apikey_id': null,
});

const formDataCopy = ref({
  'id': 0,
  'name': '',
  'team_id': null,
});

const teamList = ref([]);
const teamLoading = ref(false);

const publishFormData = ref({
  'release_id': 0,
  'name': '',
  'icon': '',
  'description': '',
  'openingQuestion': '',
  'labels': '',
});

// 动态标签相关状态
const tags = ref<string[]>([]);
const inputVisible = ref(false);
const inputValue = ref('');
const inputRef = ref();

const tableColumns = [
  { title: '应用名称', dataIndex: 'name', key: 'name'},
  { title: '应用类型', dataIndex: 'mode', key: 'mode', filters: [
      {
        text: '对话服务(工作流编排)',
        value: 2,
      },
      {
        text: '对话服务(基础编排)',
        value: 1,
      },
      {
        text: '工作流',
        value: 3,
      },
    ],
    filterMultiple: false,
    onFilter: (value, record) => record.mode == value,
  },
  // { title: 'API KEY 名称', dataIndex: 'api_key_name', key: 'api_key_name' },
  // { title: 'Agent助手', dataIndex: 'agent', key: 'agent', },
  // { title: '标准问答', dataIndex: 'sqa', key: 'sqa', },
  {
    title: '对话次数',
    dataIndex: 'chat_record_count',
    key: 'chat_record_count',
    sorter: {
      compare: (a, b) => a.chat_record_count - b.chat_record_count,
      multiple: 1,
    },
  },
  { title: '发布时间', dataIndex: 'update_time', key: 'update_time'},
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 450,
    fixed: 'right',
  },
];

const otherTableColumns = [
  { title: '应用名称', dataIndex: 'name', key: 'name'},
  { title: '应用类型', dataIndex: 'mode', key: 'mode', },
  // { title: 'API KEY 名称', dataIndex: 'api_key_name', key: 'api_key_name' },
  // { title: 'Agent助手', dataIndex: 'agent', key: 'agent', },
  // { title: '标准问答', dataIndex: 'sqa', key: 'sqa', },
  {
    title: '对话次数',
    dataIndex: 'chat_record_count',
    key: 'chat_record_count',
    sorter: {
      compare: (a, b) => a.chat_record_count - b.chat_record_count,
      multiple: 1,
    },
  },
  { title: '发布时间', dataIndex: 'update_time', key: 'update_time'},
  { title: '创建人', dataIndex: 'account_name', key: 'account_name'},
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 400,
    fixed: 'right',
  },
];

const tableData = ref([]);
const otherTableData = ref([]);
const listData: Record<string, any>[] = [
  {
    title: `网页`,
    avatar: DesktopOutlined,
    avatar_bg: "#beaefe",
    description: '用户在此链接可以直接和您的机器人聊天',
    content: '  ',
    actions: [
      { icon: CopyOutlined, text: '复制链接', code: 1 },
      { icon: EyeOutlined, text: '预览体验', code: 2 },
    ]
  },
  {
    title: `JS嵌入`,
    avatar: SelectOutlined,
    avatar_bg: "#b3e19d",
    description: '可添加到网站的任何位置，将此 iframe 添加到 html 代码中',
    content: '  ',
    actions: [
      // { icon: PlusCircleOutlined, text: '创建站点', code: 10 },
      { icon: EyeOutlined, text: '查看代码', code: 11 },
    ]
  },
  {
    title: `API调用`,
    avatar: BranchesOutlined,
    avatar_bg: "#fab6b6",
    description: '通过API，可直接进行调用或发出请求',
    content: '   ',
    actions: [
      { icon: EyeOutlined, text: '接口秘钥', code: 20 },
      { icon: FileTextOutlined, text: '接口文档', code: 21 },
    ]
  }
];

const getOtherReleaseList = () => {
  const params = {keyword: keyword.value}
  getAllReleaseList(params).then((response) => {
    console.log(response);
    const { data } = response;
    otherTableData.value = data;
  }).catch((e) => {
    console.error(e);
  });
}

const refreshReleaseList = () => {
  const params = {keyword: keyword.value}
  getReleaseList(params).then((response) => {
    console.log(response);
    const { data } = response;
    tableData.value = data;
  }).catch((e) => {
    console.error(e);
  });
}

function releaseAction(code, record){
  console.log(code, record);
  useApiKey.value = record.api_key;
  useIdentifier.value = record.identifier;
  readRecord.value = record;
  if (code === 1){
    changeShowCopyLinkVisible(true);
    releaseNameForDownload.value = record.name;
  }else if (code === 2){
    if([1, 2].includes(record.mode)){
      previewRun(chatBotUrl.value);
    }else if(record.mode === 3){
      previewRun(`/f/workflow/${record.code}`);
    }
  }else if (code === 20){
    changeShowApiKeyVisible(true);
  }else if (code === 21){
    const url = import.meta.env.VITE_API_URL;
    const isTestEnv = url.search("isapientia") === -1;
    // 正式环境
    let apiUrl = "https://apifox.com/apidoc/shared-5d959a41-426c-49c0-8300-0140f07d1b28";
    // 测试环境
    if (isTestEnv){
      apiUrl = "https://apifox.com/apidoc/shared-b50692a1-d05e-4512-a09b-c624bc198f30";
    }
    previewRun(apiUrl);
  }else if(code === 11){
    jsEmbContent.value = `<iframe \nsrc="${chatBotUrl.value}" \nwidth="408px" \nheight="594px" \nframeborder="0">\n</iframe>`;
    jsEmbContent1.value = `<script src="https://res.isapientia.com/js/bot.messenger.js"><\/script>\
    \n\n<iframe \nsrc="${chatBotUrl.value}?b=1" \nid="sapientiabot" \nframeborder="0" \nallowtransparency="true" \nstyle="position:fixed;right: 28px;bottom: 28px;">\n</iframe>`;
    changeJsEmbVisible(true);
  }
}

const showWorkflowLogDrawer = (item) => {
  currClickDid.value = item.d_app_id
  showLogVisible.value = true;
}

function selectImportFile(record){
  currImportReleaseId.value = record.id;
}

function importWorkflow(fileContent){
  const params = {
    release_id: currImportReleaseId.value,
    yaml_content: fileContent,
  }
  importReleaseWorkflow(params).then((response) => {
    console.log(response);
    const {code, data} = response;
    if (code === 0){
      message.success(`导入成功`);
    }
  }).catch((e) => {
    console.error(e);
  });
}

const fileContent = ref();

// 上传前的处理函数
const beforeUpload = (file) => {
  const reader = new FileReader();

  reader.onload = (e) => {
    fileContent.value = e.target.result;
    console.log("fileContent:", fileContent.value)
    importWorkflow(fileContent.value)
  };
  reader.readAsText(file);
  // 阻止默认的上传行为
  return false;
};

function downloadYml(filename, textContent) {
  // 创建一个Blob实例，类型为纯文本
  const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
  // 创建一个指向Blob的URL
  const url = URL.createObjectURL(blob);
  // 创建一个a标签用于下载
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename + '.yml');
  document.body.appendChild(link);
  // 触发下载
  link.click();
  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

function exportWorkflow(record){
  const params = {
    release_id: record.id,
  }
  exportReleaseWorkflow(params).then((response) => {
    console.log(response);
    const {code, data} = response;
    if (code === 0){
      downloadYml(record.name, data?.data)
      message.success(`导出成功`);
    }
  }).catch((e) => {
    console.error(e);
  });
}

const showDeleteConfirm = (item) => {
  const opt = fabuRadioValue.value === '2'?"清空":"删除";
  Modal.confirm({
    title: `确认要${opt}用于"${item.name}"的发布吗?`,
    icon: createVNode(ExclamationCircleOutlined),
    // content: createVNode('div', { style: 'color:red;' }, '知识库下对应的文档数据会一并删除，请谨慎操作'),
    content: `${opt}后相应应用项目将无法发起对话访问，请谨慎操作`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      console.log('Ok');
      remove(item);
    },
    onCancel() {
      console.log('Cancel');
    },
  });
};

const form = ref<FormInstance>();
const formCopy = ref<FormInstance>();
const publishForm = ref<FormInstance>();

function submit() {
  loading.value = true;
  if(formData.value.appMode === '1'){
    formData.value.mode = formData.value.chatOrchestration === '1'?1:2;
  }else{
    formData.value.mode = 3;
  }
  form.value
    ?.validate()
    .then(() => {
      saveRelease(formData.value).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          message.success(`保存成功`);
          showCreateAppVisible.value = false;
          refreshReleaseList();
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

const onSearchChange = (v: string) => {
  console.log('change use value', v);
  onSearch('')
};

const onSearch = (searchValue: string) => {
  console.log('use value', searchValue);
  console.log('or use value', keyword.value);
  if (fabuRadioValue.value === '1'){
    refreshReleaseList(); 
  }else{
    getOtherReleaseList();
  }
};

function publishToQingxiaoda(record) {
  isUpdateMode.value = record.qingxiaoda_published || false;
  
  if (isUpdateMode.value) {
    // 更改发布时，先获取详情信息
    publishLoading.value = true;
    getQingxiaodaPublish({ release_id: record.id }).then((response) => {
      console.log(response);
      const { code, data } = response;
      if (code === 0) {
        // 回显老内容
        publishFormData.value = {
          'release_id': record.id,
          'name': data.name || '',
          'icon': data.icon || '',
          'description': data.description || '',
          'openingQuestion': data.openingQuestion || record.opening_statement || '',
          'labels': data.labels || '',
        };
        // 回显标签
        tags.value = data.labels ? data.labels.split(',').filter(tag => tag.trim()) : [];
      } else {
        // 如果获取失败，使用默认值
        publishFormData.value = {
          'release_id': record.id,
          'name': '',
          'icon': '',
          'description': '',
          'openingQuestion': record.opening_statement || '',
          'labels': '',
        };
        tags.value = [];
      }
      showPublishToQingxiaodaVisible.value = true;
    }).catch((e) => {
      console.error(e);
      // 如果请求失败，使用默认值
      publishFormData.value = {
        'release_id': record.id,
        'name': '',
        'icon': '',
        'description': '',
        'openingQuestion': record.opening_statement || '',
        'labels': '',
      };
      tags.value = [];
      showPublishToQingxiaodaVisible.value = true;
    }).finally(() => {
      publishLoading.value = false;
    });
  } else {
    // 首次发布时，使用默认值
    publishFormData.value = {
      'release_id': record.id,
      'name': '',
      'icon': '',
      'description': '',
      'openingQuestion': record.opening_statement || '',
      'labels': '',
    };
    tags.value = [];
    showPublishToQingxiaodaVisible.value = true;
  }
}

// 动态标签处理函数
const handleClose = (removedTag: string) => {
  const newTags = tags.value.filter(tag => tag !== removedTag);
  tags.value = newTags;
  // 更新表单数据
  publishFormData.value.labels = newTags.join(',');
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    inputRef.value?.focus();
  });
};

const handleInputConfirm = () => {
  const newInputValue = inputValue.value.trim();
  let newTags = [...tags.value];
  if (newInputValue && newTags.indexOf(newInputValue) === -1) {
    newTags = [...newTags, newInputValue];
  }
  tags.value = newTags;
  inputVisible.value = false;
  inputValue.value = '';
  // 更新表单数据
  publishFormData.value.labels = newTags.join(',');
  // 手动触发表单验证来清除错误提示
  publishForm.value?.validateFields(['labels']);
};

function publishToQingxiaodaSubmit() {
  publishLoading.value = true;
  
  // 检查标签
  if (tags.value.length === 0) {
    message.error('请添加至少一个标签');
    publishLoading.value = false;
    return;
  }
  
  // 更新labels字段
  publishFormData.value.labels = tags.value.join(',');
  
  publishForm.value
    ?.validate()
    .then(() => {
      const params = {
        release_id: publishFormData.value.release_id,
        name: publishFormData.value.name,
        icon: publishFormData.value.icon,
        description: publishFormData.value.description,
        openingQuestion: publishFormData.value.openingQuestion,
        labels: publishFormData.value.labels
      };
      
      const apiCall = isUpdateMode.value ? updatePublishToQingxiaodaApi : publishToQingxiaodaApi;
      const successMessage = isUpdateMode.value ? '更新成功' : '发布成功';
      
      apiCall(params).then((response) => {
        console.log(response);
        const {code, data} = response;
        if (code === 0){
          message.success(successMessage);
          showPublishToQingxiaodaVisible.value = false;
          // 刷新列表以更新qingxiaoda_published状态
          if (fabuRadioValue.value === '1') {
            refreshReleaseList();
          } else {
            getOtherReleaseList();
          }
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      publishLoading.value = false;
    });
}

function handlePublishModalCancel() {
  showPublishToQingxiaodaVisible.value = false;
  // 重置表单验证状态
  publishForm.value?.resetFields();
  // 清空文件字典
  fileDict.value = {};
  // 清空标签状态
  tags.value = [];
  inputVisible.value = false;
  inputValue.value = '';
}

function getOssStsToken() {
  return http
    .request<OssStsTokenProps, Response<OssStsTokenProps>>('/thirdparty/oss/sts_token?public=1', 'GET')
    .then((res) => {
      console.log(res);
      const { data } = res;
      ossStsToken.value = data;
      return data;
    })
    .finally(() => (console.log("end")));
}

const beforeUploadIcon: UploadProps['beforeUpload'] = async file => {
  const fileExtName = file.name.substring(file.name.lastIndexOf(".") + 1);
  const fileName = uuidv4() + "." + fileExtName;
  const oss_key = baseFilePath.value + '/' + fileName;

  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    message.error('单个图片最大1MB!');
    return false;
  }

  const fileData = {name: file.name, oss_key: oss_key, size: file.size, status: 0, url: ""};
  console.log(fileData);
  fileDict.value[file.uid] = fileData;
  return true;
}

const handleIconChange = async (info: UploadChangeParam) => {
  const status = info.file.status;
  console.log(info)
  console.log(fileDict.value)

  if (info.file.status === 'uploading') {
    publishLoading.value = true;
    return;
  }
  if (info.file.status === 'done') {
    publishFormData.value.icon = fileDict.value[info.file.uid]["url"];
    // 手动触发表单验证来清除错误提示
    publishForm.value?.validateFields(['icon']);
  }
  if (info.file.status === 'error') {
    publishLoading.value = false;
    message.error('upload error');
  }

  if(status === 'removed') {
    delete fileDict.value[info.file.uid];
    publishFormData.value.icon = '';
    // 移除图片后也需要触发验证
    publishForm.value?.validateFields(['icon']);
  }
};

async function customRequest(info) {
  console.log(info);
  const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
  const client = new OSS({
    region: ossStsToken.value.region,
    accessKeyId: ossStsToken.value.access_key_id,
    accessKeySecret: ossStsToken.value.access_key_secret,
    stsToken: ossStsToken.value.security_token,
    bucket: ossStsToken.value.bucket_name,
    endpoint: ossStsToken.value.endpoint,
    useFetch: true,
    cname: use_cname,
    secure: true,
  });

  const oss_key = fileDict.value[info.file.uid]["oss_key"];
  if(info.file){
    console.log(oss_key)
    await client.multipartUpload(oss_key, info.file, {
      parallel: 4,
      partSize: 100 * 1024,
      progress: function (percent) {
        console.log("progress is: ", percent * 100);
        publishLoading.value = true;
        if (percent === 1){
          publishLoading.value = false;
        }
      },
    }).then(res => {
      console.log('结果:', res);
      fileDict.value[info.file.uid]["status"] = 1;
      fileDict.value[info.file.uid]["url"] = ossStsToken.value.endpoint + '/' + oss_key;
      fileDict.value = fileDict.value;
      publishFormData.value.icon = fileDict.value[info.file.uid]["url"];
      // 手动触发表单验证来清除错误提示
      publishForm.value?.validateFields(['icon']);
    }).catch((err) => {
      console.log(err);
    });
  }
}

onMounted(() => {
  window.addEventListener('click', registerCopyHandel);
  refreshReleaseList();
  if(account.is_superuser){
    getOtherReleaseList();
  }
  getOssStsToken();
  // 获取API Key列表，确保创建工作流时能选择API Key
  apikeyStore.getApikeyList();
});

onBeforeMount(() => {
  window.removeEventListener('click', registerCopyHandel)
})
</script>
<style lang="less">
.download-button {
  color: var(--ant-primary-color);
  border-color: transparent;
  background: transparent;
  box-shadow: none;
  cursor: pointer;
}
</style>
