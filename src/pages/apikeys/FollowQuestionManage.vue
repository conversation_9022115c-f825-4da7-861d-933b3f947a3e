<template>
  <div style="padding-left: 20px; padding-right: 20px;">
    <div style="margin-bottom: 15px;" class="flex justify-between">
      <a-typography-text type="secondary">维护回归测试的问题列表，方便每次对助手调试升级后的回归检查。</a-typography-text>
      <div>
        <a-button style="margin-right: 5px;" type="primary" @click="openFollowQuestionImportModalVisible">
          <ImportOutlined class="text-lg"/>批量导入
        </a-button>
        <a-button type="primary" @click="openFollowQuestionModalVisible">
          <template #icon>
            <PlusOutlined />
          </template>
          新增问题
        </a-button>
      </div>
    </div>
    <div style="margin-bottom: 15px;" class="flex justify-between">
      <div>
        <div style="display:inline;font-size: 14px;">
          <span>共 <font color="#ff9640">{{questionTotal}}</font> 个问题</span>
          <span v-if="questionRunning > 0">，有 <font color="#ff9640">{{questionRunning}}</font> 个正在执行</span>
        </div>
      </div>
      <div>
        <a-button 
          size="small" style="margin-right: 5px;" type="primary" ghost 
          :loading="runAllStatus"
          @click="showRunAllConfirm()"
        >全部执行</a-button>
        <a-button 
          size="small" type="primary" ghost 
          :loading="runAllStatus"
          @click="exportRecord"
        >
        导出</a-button>
      </div>
    </div>
    <FollowQuestionTable 
      :key="tableKey"
      :source="source"
      :biz_id="biz_id"
      :tablePageData="tablePageData"
      @setEditRecord="setEditRecord"
      @refreshPage="refreshPage"
      @setRunTestFollowQuestion="setRunTestFollowQuestion"
    />
  </div>
  <FollowQuestionModal 
    :source="source"
    :biz_id="biz_id"
    :editRecordData="editRecordData"
    :modalVisible="followQuestionModalVisible"
    @setFollowQuestionModalVisible="setFollowQuestionModalVisible"
  />
  <FollowQuestionImportModal 
    :source="source"
    :biz_id="biz_id"
    :modalVisible="followQuestionImportModalVisible"
    @setFollowQuestionImportModalVisible="setFollowQuestionImportModalVisible"
  />
</template>
<script lang="ts" setup>
import { onMounted, createVNode, ref} from 'vue';
import FollowQuestionModal from './FollowQuestionModal.vue';
import FollowQuestionImportModal from './FollowQuestionImportModal.vue';
import FollowQuestionTable from './FollowQuestionTable.vue';
import { 
  getFollowQuestionPage, 
  runTestFollowQuestion 
} from "@/api/release";
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import * as XLSX from 'xlsx'


const followQuestionImportModalVisible = ref(false);

const followQuestionModalVisible = ref(false);
const tableKey = ref(1);
const editRecordData = ref({});
const runAllStatus = ref(false);
const questionTotal = ref(0);
const questionRunning = ref(0);
let timer = ref<any | null>(null);

const props = defineProps({
  source: Number,
  biz_id: Number,
});

function startTimer() {
  if (timer.value) {
    console.log("startTimer:", timer.value);
    return;
  }
  timer.value = setInterval(() => {
    console.log('Timer executed!')
    runTest(null, "get_run_result");
  }, 5000)
}

function stopTimer() {
  if (timer.value) {
    clearInterval(timer.value)
    console.log('Timer clearInterval')
    timer.value = null
  }
}

function setFollowQuestionModalVisible(visible: boolean, refresh: boolean) {
  followQuestionModalVisible.value = visible;
  if(refresh === true){
    tableKey.value = tableKey.value + 1;
    console.log(tableKey.value);
    editRecordData.value = {}
    questionTotal.value = questionTotal.value + 1;
  }
}

function setFollowQuestionImportModalVisible(visible: boolean){
  followQuestionImportModalVisible.value = visible;
  if (visible === false){
    const params = {
      source: props.source,
      biz_id: props.biz_id,
    }
    refreshPage(params);
    runTest(null, "get_run_result");
  }
}

function openFollowQuestionModalVisible(){
  setFollowQuestionModalVisible(true, false);
}

function openFollowQuestionImportModalVisible(){
  setFollowQuestionImportModalVisible(true);
}

function setEditRecord(record){
  editRecordData.value = record;
  openFollowQuestionModalVisible();
}

function setRunTestFollowQuestion(question_id){
  runTest(question_id, null);
}

function runTest(question_id, get_run_result){
  const params = {
    source: props.source,
    biz_id: props.biz_id,
    question_id: question_id,
    run_result: get_run_result,
  }
  runTestFollowQuestion(params).then((response) => {
    console.log(response);
    const {code, data} = response;
    if (code === 0){
      const {run_status, total, running} = data;
      if(question_id === null){
        runAllStatus.value = run_status;
      }
      questionTotal.value = total;
      questionRunning.value = running;
      if(run_status === true){
        startTimer()
      }else{
        stopTimer()
      }
      tableKey.value = tableKey.value + 1;
    }
  }).catch((e) => {
    console.error(e);
  });
}

function showRunAllConfirm(){
  Modal.confirm({
    title: `确认要回归测试所有问题吗?`,
    icon: createVNode(ExclamationCircleOutlined),
    content: '会将所有问题加入到执行队列中异步处理，每 5s 自动刷新一下执行结果',
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      console.log('Ok');
      runTest(null, null);
    },
    onCancel() {
      console.log('Cancel');
    },
  }); 
}

const tablePageData = ref({});

function refreshPage(params){
  const data = getFollowQuestionPage(params).then((res) => {
    const { data } = res;
    console.log(data);
    tablePageData.value = data;
    return data;
  });
  return data;
}

function genRemark(params){
  if (!params){
    return "-";
  }
  if(params.answer_from === "agent" && params.agent){
    return "来自: " + params.agent.name
  }else if(params.answer_from === "menu_qa"){
    return "来自: 菜单"
  }else if (params.answer_from === "standard_qa"){
    return "来自: 标准问答"
  }else if (params.answer_from === "workflow_chat"){
    return "来自: 工作流"
  }else{
    "-"
  }
}

const exportRecord = () => {
  const params = {
    source: props.source,
    biz_id: props.biz_id,
    page_size: tablePageData.value.count + 1,
  }
  getFollowQuestionPage(params).then((res) => {
    const { data } = res;
    console.log(data);

    let head = {
      question: '问题',
      answers: '最近一次回答',
      update_time: '完成时间',
      remark: '备注',
    }
    // console.log(tablePageData.value);
    const {results} = data;
    const excelData = results.map(item => {
      const obj = {}
      for (const k in item) {
        if (head[k]) {
          if (["update_time"].includes(k)){
            obj[head[k]] = dayjs(item[k]).format('YYYY-MM-DD HH:mm')
          }else if (k === "answers"){
            obj[head["answers"]] = item[k].length > 0?item[k][0].answer:'-'
            obj[head["remark"]] = item[k].length > 0?genRemark(item[k][0].params):'-';
          }else{
            obj[head[k]] = item[k]
          }
        } 
      }
      return obj
    })
    // 创建工作表
    const dataExcel = XLSX.utils.json_to_sheet(excelData)
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    // 将工作表放入工作簿中
    XLSX.utils.book_append_sheet(wb, dataExcel, '回归测试结果')
    // 生成文件并下载
    XLSX.writeFile(wb, `回归测试结果${dayjs().format('YYYY-MM-DD HH:mm')}.xlsx`)  
  })
}

onMounted(() => {
  console.log("onMounted")
  runTest(null, "get_run_result");
});
</script>