<template>
  <a-row :gutter="[16, 16]">
    <a-col :span="spanWidth || 7">
      <a-card title="页面风格">
        <div style="margin-bottom: 15px; margin-top: 15px;">
          <a-row :gutter="16">
            <a-col :span="24" style="margin-bottom: 30px;">
              <a-button type="primary" @click="showPageStyleModal = true">
                选择页面风格
              </a-button>
            </a-col>
            <a-col :span="24" v-if="modelValue.style_preset && !showPageStyleModal">
              <a-card 
                :bordered="true" 
                title="当前选择的风格" 
                :headStyle="{ fontSize: '16px', fontWeight: 'bold' }"
                :bodyStyle="{ padding: '16px', backgroundColor: modelValue.style?.backgroundColor || '#f1f5f9' }"
              >
                <a-row :gutter="16">
                  <a-col :span="16">
                    <a-card :bordered="true">
                      <template #title>
                        <div style="text-align: center; font-weight: bold;">{{ modelValue.style_preset }}</div>
                      </template>
                      <div :style="{ 
                        backgroundColor: modelValue.style?.backgroundColor || '#f1f5f9', 
                        padding: '10px', 
                        borderRadius: '6px' 
                      }">
                        <div :style="{ 
                          backgroundColor: modelValue.style?.primaryColor || '#3b82f6', 
                          color: '#fff', 
                          padding: '8px', 
                          borderRadius: '4px',
                          marginBottom: '8px',
                          textAlign: 'center'
                        }">
                          主题色
                        </div>
                        <div :style="{ 
                          color: modelValue.style?.textColor || '#1e293b',
                          padding: '6px'
                        }">
                          文本内容
                        </div>
                        <div :style="{
                          backgroundColor: modelValue.style?.accentColor || '#2563eb',
                          color: '#fff',
                          padding: '5px 10px',
                          borderRadius: '4px',
                          display: 'inline-block',
                          marginTop: '8px',
                          fontSize: '12px'
                        }">
                          强调色
                        </div>
                      </div>
                    </a-card>
                  </a-col>
                  <a-col :span="8">
                    <div style="height: 100%; display: flex; flex-direction: column; justify-content: center;">
                      <h3>风格预览</h3>
                      <p>主题色: <span :style="{ color: modelValue.style?.primaryColor || '#3b82f6', fontWeight: 'bold' }">{{ modelValue.style?.primaryColor || '#3b82f6' }}</span></p>
                      <p>文本色: <span :style="{ color: modelValue.style?.textColor || '#1e293b', fontWeight: 'bold' }">{{ modelValue.style?.textColor || '#1e293b' }}</span></p>
                      <p>背景色: <span :style="{ color: modelValue.style?.backgroundColor || '#f1f5f9', fontWeight: 'bold', border: '1px solid #ddd' }">{{ modelValue.style?.backgroundColor || '#f1f5f9' }}</span></p>
                      <p>强调色: <span :style="{ color: modelValue.style?.accentColor || '#2563eb', fontWeight: 'bold' }">{{ modelValue.style?.accentColor || '#2563eb' }}</span></p>
                    </div>
                  </a-col>
                </a-row>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </a-col>

    <a-col :span="spanWidth || 8" style="margin-left: 10px;">
      <a-card title="背景图">
        <div style="margin-bottom: 15px; margin-top: 15px;">
          <a-button type="primary" @click="showBackgroundModal = true" style="margin-bottom: 16px;">
            选择背景图
          </a-button>
          <div v-if="(modelValue.background_preset || modelValue.background_type === 'custom') && !showBackgroundModal" style="margin-top: 16px;">
            <a-card 
              :bordered="true" 
              title="当前选择的背景" 
              :headStyle="{ fontSize: '16px', fontWeight: 'bold' }"
              :bodyStyle="{ padding: '16px' }"
            >
              <a-row :gutter="16">
                <a-col :span="16">
                  <a-card :bordered="true">
                    <template #title>
                      <div style="text-align: center; font-weight: bold;">{{ modelValue.background_preset }}</div>
                    </template>
                    <div style="height: 100px; overflow: hidden; position: relative;">
                      <div style="position: absolute; inset: 0;"
                        :style="modelValue.background_type === 'custom' ? {
                          backgroundImage: `url(${modelValue.background_image_url})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center'
                        } : {
                          background: modelValue.background_css || '#ffffff',
                          backgroundSize: modelValue.background_css?.includes('radial-gradient') ? '20px 20px' : 
                                        modelValue.background_css?.includes('linear-gradient(rgba') ? '40px 40px' : 
                                        'cover',
                          backgroundPosition: modelValue.background_css?.includes('radial-gradient') ? '0 0, 10px 10px' : 
                                            modelValue.background_css?.includes('linear-gradient(rgba') ? '0 0, 20px 20px, 0 0, 20px 20px' : 
                                            'center'
                        }"
                      ></div>
                    </div>
                  </a-card>
                </a-col>
                <a-col :span="8">
                  <div style="height: 100%; display: flex; flex-direction: column; justify-content: center;">
                    <h3>背景预览</h3>
                    <p>类型: <span style="font-weight: bold;">{{ modelValue.background_type === 'custom' ? '自定义图片' : '预设背景' }}</span></p>
                    <p>名称: <span style="font-weight: bold;">{{ modelValue.background_preset }}</span></p>
                  </div>
                </a-col>
              </a-row>
            </a-card>
          </div>
        </div>
      </a-card>
    </a-col>

    <a-col :span="spanWidth || 8" style="margin-left: 10px;">
      <a-card title="字体设置">
        <div style="margin-bottom: 15px; margin-top: 15px;">
          <a-button type="primary" @click="showFontSettingDialog" style="margin-bottom: 16px;">
            设置字体
          </a-button>
          <div v-if="modelValue.font_settings && !showFontSettingsModal" style="margin-top: 16px;">
            <a-card 
              :bordered="true" 
              title="当前字体设置" 
              :headStyle="{ fontSize: '16px', fontWeight: 'bold' }"
              :bodyStyle="{ padding: '16px' }"
            >
              <a-row :gutter="16">
                <a-col :span="16">
                  <a-card :bordered="true">
                    <template #title>
                      <div style="text-align: center; font-weight: bold;">字体预览</div>
                    </template>
                    <div style="padding: 16px;">
                      <div :style="{
                        fontSize: modelValue.font_settings?.titleSize + 'px',
                        color: modelValue.font_settings?.titleColor || '#1e293b',
                        marginBottom: '10px',
                        fontWeight: 'bold'
                      }">
                        标题文字大小: {{ modelValue.font_settings?.titleSize || 24 }}px
                      </div>
                      <div :style="{
                        fontSize: modelValue.font_settings?.descriptionSize + 'px',
                        color: modelValue.font_settings?.descriptionColor || '#475569',
                        marginBottom: '10px'
                      }">
                        说明文字大小: {{ modelValue.font_settings?.descriptionSize || 16 }}px
                      </div>
                      <div :style="{
                        fontSize: modelValue.font_settings?.userMsgSize + 'px',
                        color: modelValue.font_settings?.userMsgColor || '#000000',
                        marginBottom: '10px'
                      }">
                        用户消息大小: {{ modelValue.font_settings?.userMsgSize || 16 }}px
                      </div>
                      <div :style="{
                        fontSize: modelValue.font_settings?.botMsgSize + 'px',
                        color: modelValue.font_settings?.botMsgColor || '#1e293b',
                        marginBottom: '10px'
                      }">
                        机器人消息大小: {{ modelValue.font_settings?.botMsgSize || 16 }}px
                      </div>
                      <div :style="{
                        fontSize: modelValue.font_settings?.buttonTextSize + 'px',
                        color: modelValue.font_settings?.buttonTextColor || '#ffffff',
                        backgroundColor: '#3b82f6',
                        padding: '4px 8px',
                        borderRadius: '4px',
                        display: 'inline-block'
                      }">
                        按钮文字大小: {{ modelValue.font_settings?.buttonTextSize || 14 }}px
                      </div>
                    </div>
                  </a-card>
                </a-col>
                <a-col :span="8">
                  <div style="height: 100%; display: flex; flex-direction: column; justify-content: center;">
                    <h3>字体颜色</h3>
                    <p>标题颜色: <span :style="{ color: modelValue.font_settings?.titleColor || '#1e293b', fontWeight: 'bold' }">{{ modelValue.font_settings?.titleColor || '#1e293b' }}</span></p>
                    <p>说明文字颜色: <span :style="{ color: modelValue.font_settings?.descriptionColor || '#475569', fontWeight: 'bold' }">{{ modelValue.font_settings?.descriptionColor || '#475569' }}</span></p>
                    <p>用户消息颜色: <span :style="{ color: modelValue.font_settings?.userMsgColor || '#000000', fontWeight: 'bold' }">{{ modelValue.font_settings?.userMsgColor || '#000000' }}</span></p>
                    <p>机器人消息颜色: <span :style="{ color: modelValue.font_settings?.botMsgColor || '#1e293b', fontWeight: 'bold' }">{{ modelValue.font_settings?.botMsgColor || '#1e293b' }}</span></p>
                    <p>按钮文字颜色: <span :style="{ color: modelValue.font_settings?.buttonTextColor || '#ffffff', fontWeight: 'bold', backgroundColor: '#3b82f6', padding: '0 4px' }">{{ modelValue.font_settings?.buttonTextColor || '#ffffff' }}</span></p>
                  </div>
                </a-col>
              </a-row>
            </a-card>
          </div>
        </div>
      </a-card>
    </a-col>
  </a-row>

  <!-- Style Modal -->
  <page-style-modal 
    v-model:visible="showPageStyleModal"
    v-model:style-data="localStyleData"
    @update:visible="showPageStyleModal = false"
    @confirm="confirmPageStyle"
  />

  <!-- Background Modal -->
  <background-modal
    v-model:visible="showBackgroundModal"
    v-model:background-data="localBackgroundData"
    @update:visible="showBackgroundModal = false"
    :oss-request="ossRequestObject"
    @confirm="confirmBackground"
  />

  <!-- Font Settings Modal -->
  <font-settings-modal
    v-model:visible="showFontSettingsModal"
    v-model:font-settings="localFontSettings"
    @update:visible="showFontSettingsModal = false"
    @confirm="confirmFontSettings"
  />
</template>

<script>
import { defineComponent, ref, computed, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import PageStyleModal from './modals/PageStyleModal.vue';
import BackgroundModal from './modals/BackgroundModal.vue';
import FontSettingsModal from './modals/FontSettingsModal.vue';
import OSS from 'ali-oss';
import http from '@/store/http';

export default defineComponent({
  name: 'StyleSettings',
  
  components: {
    PageStyleModal,
    BackgroundModal,
    FontSettingsModal
  },
  
  props: {
    modelValue: {
      type: Object,
      required: true
    },
    spanWidth: {
      type: Number,
      default: null
    },
    ossRequest: {
      type: Object,
      default: null
    },
    saveSettings: {
      type: Function,
      default: async () => true
    }
  },
  
  emits: ['update:modelValue', 'settings-updated'],
  
  setup(props, { emit }) {
    // Modals visibility
    const showPageStyleModal = ref(false);
    const showBackgroundModal = ref(false);
    const showFontSettingsModal = ref(false);
    
    // Local data copies
    const localStyleData = ref({});
    const localBackgroundData = ref({});
    const localFontSettings = ref({});
    
    // OSS-related state
    const ossStsToken = ref(null);
    const baseFilePath = ref("file/release/background");
    
    // OSS functions
    const getOssStsToken = async () => {
      try {
        const res = await http.request('/thirdparty/oss/sts_token?public=1', 'GET');
        console.log('OSS token response:', res);
        const { data } = res;
        ossStsToken.value = data;
        return data;
      } catch (error) {
        console.error("Failed to get OSS token:", error);
        message.error('获取上传凭证失败');
        return null;
      }
    };
    
    const uploadFileToOSS = async ({ file, oss_key, onProgress }) => {
      try {
        // Make sure we have the OSS token
        if (!ossStsToken.value) {
          const token = await getOssStsToken();
          if (!token) {
            throw new Error('无法获取上传凭证');
          }
        }
        
        // Initialize OSS client
        const token = ossStsToken.value;
        const use_cname = !token.endpoint.includes(".aliyuncs.com");
        const client = new OSS({
          region: token.region,
          accessKeyId: token.access_key_id,
          accessKeySecret: token.access_key_secret,
          stsToken: token.security_token,
          bucket: token.bucket_name,
          endpoint: token.endpoint,
          useFetch: true,
          cname: use_cname,
          secure: true,
        });

        // Upload the file to OSS
        const result = await client.multipartUpload(oss_key, file, {
          parallel: 4,
          partSize: 100 * 1024,
          progress: (percent) => {
            if (onProgress) {
              onProgress(percent * 100);
            }
          },
        });
        
        return {
          url: token.endpoint + '/' + oss_key
        };
      } catch (error) {
        console.error('上传失败:', error);
        throw error;
      }
    };
    
    // Create OSS request object to pass to child components
    const internalOssRequest = {
      getOssStsToken,
      uploadFile: uploadFileToOSS
    };
    
    // Use either the provided ossRequest or our internal implementation
    const ossRequestObject = computed(() => {
      return props.ossRequest || internalOssRequest;
    });
    
    // Fetch OSS token on component mount
    getOssStsToken();
    
    // Watch for external changes to props.modelValue
    watch(() => props.modelValue, (newVal) => {
      // Update local data when modelValue changes
      if (newVal.style) {
        localStyleData.value = {
          style_preset: newVal.style_preset,
          style: { ...newVal.style }
        };
      }
      
      if (newVal.background_type || newVal.background_preset) {
        localBackgroundData.value = {
          background_type: newVal.background_type,
          background_preset: newVal.background_preset,
          background_css: newVal.background_css,
          background_image_url: newVal.background_image_url
        };
      }
      
      if (newVal.font_settings) {
        localFontSettings.value = { ...newVal.font_settings };
      }
    }, { deep: true, immediate: true });
    
    // Dialog display functions
    const showFontSettingDialog = () => {
      showFontSettingsModal.value = true;
      nextTick(() => {
        // Logic to ensure sliders render properly can be added here if needed
      });
    };
    
    // Confirmation handlers
    const confirmPageStyle = async () => {
      const updatedData = {
        ...props.modelValue,
        style_preset: localStyleData.value.style_preset,
        style: localStyleData.value.style
      };
      
      emit('update:modelValue', updatedData);
      message.success(`已选择 ${localStyleData.value.style_preset} 风格`);
      
      try {
        await props.saveSettings(updatedData);
        emit('settings-updated', 'style');
      } catch (error) {
        console.error('Failed to save page style settings:', error);
        message.error('保存风格设置失败');
      }
      
      showPageStyleModal.value = false;
    };
    
    const confirmBackground = async () => {
      const updatedData = {
        ...props.modelValue,
        background_type: localBackgroundData.value.background_type,
        background_preset: localBackgroundData.value.background_preset,
        background_css: localBackgroundData.value.background_css,
        background_image_url: localBackgroundData.value.background_image_url
      };
      
      emit('update:modelValue', updatedData);
      message.success(`已选择 ${localBackgroundData.value.background_preset} 背景`);
      
      try {
        await props.saveSettings(updatedData);
        emit('settings-updated', 'background');
      } catch (error) {
        console.error('Failed to save background settings:', error);
        message.error('保存背景设置失败');
      }
      
      showBackgroundModal.value = false;
    };
    
    const confirmFontSettings = async () => {
      const updatedData = {
        ...props.modelValue,
        font_settings: { ...localFontSettings.value }
      };
      
      emit('update:modelValue', updatedData);
      message.success('字体设置已保存');
      
      try {
        await props.saveSettings(updatedData);
        emit('settings-updated', 'font');
      } catch (error) {
        console.error('Failed to save font settings:', error);
        message.error('保存字体设置失败');
      }
      
      showFontSettingsModal.value = false;
    };
    
    return {
      showPageStyleModal,
      showBackgroundModal,
      showFontSettingsModal,
      localStyleData,
      localBackgroundData,
      localFontSettings,
      showFontSettingDialog,
      confirmPageStyle,
      confirmBackground,
      confirmFontSettings,
      ossRequestObject
    };
  }
});
</script>

<style scoped>
.style-card {
  transition: transform 0.3s, box-shadow 0.3s;
}

.style-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.style-card.style-card-selected {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.bg-card {
  transition: transform 0.3s, box-shadow 0.3s;
}

.bg-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.bg-card.bg-card-selected {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style> 