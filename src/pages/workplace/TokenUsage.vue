<template>
  <div class="sales-overview card p-lg">
    <OverviewTitle title="近7日Token用量 (k)" up />
    <line-chart 
      :list=apikeyUsageList 
      :key="Math.random() * 100000000000000000"
    />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import LineChart from '@/components/chart/LineChart.vue';
  import OverviewTitle from '@/components/statistic/OverviewTitle.vue';

  export default defineComponent({
    components: { LineChart, OverviewTitle },
    name: 'TokenUsage',
    props:{
      apikeyUsageList:{
          type: Object
      }
    },
    setup(props, { attrs, slots, emit }) {
      const change = '+6%';
      const apikeyUsageList = props.apikeyUsageList;
      return {change, apikeyUsageList};
    },
  });
</script>
<style scoped lang="less">
  .sales-overview {
    :deep(.line-chart) {
      @apply -mt-10;
    }
  }
</style>
