<template>
  <a-modal
    width="100%"
    height="100%"
    wrap-class-name="full-modal"
    :visible=documentModalVisible
    :title="`${knowledgeBaseNamespace} - 文档管理`"
    :footer="null"
    @cancel="setModalVisible(false)"
    :destroyOnClose="true"
  >
  <a-tabs v-model:activeKey="activeKey" type="card">
    <a-tab-pane key="1" tab="上传文档" :disabled="kbShareTab === '2'">
      <a-form
        :model="formState"
        name="validate_other"
        v-bind="formItemLayout"
        @finishFailed="onFinishFailed"
        @finish="onFinish"
        :style="{'--upload-list-height': uploadListHeight}"
      >
        <a-form-item label="选择文件">
          <a-form-item name="dragger" no-style>
            <!-- accept=".doc,.docx,.pdf,.txt,.md,.json,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document" -->
            <a-upload-dragger 
              accept=".txt,.pdf,.ppt,.pptx,.doc,.docx,.xls,.xlsx"
              v-model:fileList="formState.dragger"
              :multiple="true"
              :customRequest="customRequest"
              :progress="progress"
              :before-upload="beforeUpload"
              @change="handleChange"
              style=""
            >
              <p class="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p class="ant-upload-text">将文件拖到此处或点击上传</p>
              <p class="ant-upload-hint">可批量上传，支持格式txt,pdf,docx,pptx,xlsx</p>
            </a-upload-dragger>
          </a-form-item>
        </a-form-item>
        <!-- <a-form-item label="分割间隔">
          <a-form-item name="split-number" no-style>
            <a-input-number v-model:value="formState['split-number']" :min="1" :max="1000000" />
          </a-form-item>
          <span class="ant-form-text">字符</span>
        </a-form-item> -->
        <a-form-item name="use_separator_split" :wrapper-col="{ span: 12, offset: 2 }">
          <a-checkbox v-model:checked="formState.use_separator_split">使用“空行”对文本内容自定义分段</a-checkbox>
          <a-popover placement="top">
            <template #content>
              段落1描述xxx<br><br>段落2描述xxx-l1,<br>段落2描述xxx-l2<br><br>段落3描述xxx
            </template>
            <a-button type="link">参考样例</a-button>
          </a-popover>
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 12, offset: 2 }">
          <a-button 
            type="primary" 
            html-type="submit"
            :loading="loading"
            :disabled="submitDisabled"
            >提交</a-button>
        </a-form-item>
      </a-form>
    </a-tab-pane>
    <a-tab-pane key="3" :disabled="kbShareTab === '2'">
      <template #tab>
        <span>
          手动录入
          <edit-outlined v-if="formDataInput.id > 0"/>
        </span>
      </template>
      <a-form 
        name="input_source_2"
        :model="formDataInput" 
        @finish="onFinishInput"
        >
        <a-form-item required name="title" 
          :rules="[{ required: true, message: '请输入文本标题' }]"
        >
          <a-input v-model:value="formDataInput.title" placeholder="请输入文本标题" show-count :maxlength=100 />
        </a-form-item>
        <a-form-item required name="description" 
          :rules="[{ required: true, message: '请输入文本内容' }]"
        >
          <a-textarea v-model:value="formDataInput.description" placeholder="请输入100000字以内的文本内容" 
          :rows="14" :maxlength=100000 show-count/>
        </a-form-item>
        <a-form-item required name="use_separator_split">
          <a-checkbox v-model:checked="formDataInput.use_separator_split">使用“空行”对文本内容自定义分段</a-checkbox>
          <a-popover placement="top">
            <template #content>
              段落1描述xxx<br><br>段落2描述xxx-l1,<br>段落2描述xxx-l2<br><br>段落3描述xxx
            </template>
            <a-button type="link">参考样例</a-button>
          </a-popover>
        </a-form-item>
        <a-form-item 
          :wrapper-col="{
            xs: { span: 24, offset: 0 },
            sm: { span: 16, offset: 11 },
          }"
        >
          <a-button 
            type="primary" 
            :loading="loading"
            html-type="submit"
            :disabled="!(formDataInput.title !== '' && formDataInput.description !== '')"
            >提交</a-button>
        </a-form-item>
      </a-form>
    </a-tab-pane>
    <a-tab-pane key="4" tab="网页抓取" :disabled="kbShareTab === '2'">
      <p>
        <a-typography-text type="danger">* 请避免非法抓取他人网站的侵权行为，保证链接可公开访问，且网站内容可复制</a-typography-text>
      </p>
      <a-form
        :model="formDataFetchPageInput" 
        @finish="onFinishFetchPageInput"
      >
        <a-form-item required name="description"
          :rules="[{ required: true, message: '请输入要爬取的网页地址' }]"
        >
          <a-textarea v-model:value="formDataFetchPageInput.description" placeholder="请输入要爬取的网页地址，多个网页地址可回车换行" :rows="14" :maxlength=5000 />
        </a-form-item>
        <a-form-item :wrapper-col="
          {
          xs: { span: 24, offset: 0 },
          sm: { span: 16, offset: 11 },
          }"
        >
          <a-button 
            :loading="loading"
            type="primary" 
            html-type="submit"
            :disabled="formDataFetchPageInput.description === ''"
            >提交</a-button>
        </a-form-item>
      </a-form>
    </a-tab-pane>
    <a-tab-pane key="2" tab="文档列表">
      <div style="padding-bottom: 20px;" class="flex justify-between items-center">
        <div style="padding-left: 5px;">共 {{documentPage?.count}} 个文档</div>
        <a-space direction="horizontal">
          <a-alert
            :message="processingTip"
            type="warning"
            style="background-color: #fffbe6; border: 1px solid #ffe58f;"
            v-if="processingTip"
          />
          <a-input-search
            v-model:value="documentKeyword"
            placeholder="多个关键字用空格分隔"
            allowClear
            style="width: 250px"
            @search="onDocumentSearch"
            @change="onDocumentSearchChange"
          />
        </a-space>
      </div>
      <a-table 
        size="small" 
        :columns="columns" 
        :dataSource="documentPage.results" 
        :pagination="{ pageSize: documentPage.page_size, current: documentPage.page_number, total: documentPage.count }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column.dataIndex === 'operation'">
            <!-- <a-button 
            v-if="record.input_source === 1"
            type="link" 
            size="small" 
            :disabled="record.status === 0"
            @click="previewFile(record)">预览</a-button> -->
            <a-button 
            type="link" 
            size="small" 
            :disabled="record.creator !== account.id"
            @click="renameDocumentName(record)">重命名</a-button>
            <!-- <a-button 
            v-if="record.input_source === 1"
            type="link" 
            size="small" 
            @click="downloadFile(record)">下载</a-button> -->
            <a-popconfirm 
              :okButtonProps="{ loading: loading }"
              placement="topLeft" title="确认删除吗？" 
              @confirm="remove(record)" 
              ok-text="确认" cancel-text="取消"
            >
              <a-button 
              type="link" 
              danger 
              v-auth:delete 
              :disabled="record.creator !== account.id"
              size="small">删除</a-button>
            </a-popconfirm>
          </template>
          <template v-else-if="column.dataIndex === 'name'">
            <a-avatar style="margin-right: 5px;" shape="square" size="small"
              :class="[{ 'bg-txt': record.input_source === 2}, { 'bg-web': record.input_source === 3}, { 'bg-file': record.input_source === 1}]">
              <template #icon>
                <file-text-outlined v-if="record.input_source === 2"/>
                <bug-outlined v-else-if="record.input_source === 3"/>
                <file-pdf-outlined v-else-if="record.oss_key.split('.').pop().search('pdf') != -1"/>
                <file-word-outlined v-else-if="record.oss_key.split('.').pop().search('doc') != -1"/>
                <file-ppt-outlined v-else-if="record.oss_key.split('.').pop().search('ppt') != -1"/>
                <file-excel-outlined v-else-if="record.oss_key.split('.').pop().search('xls') != -1"/>
                <file-text-outlined v-else/>
              </template>
            </a-avatar>
            <!-- <a-popover placement="top">
              <template #content>
                文档ID:{{record.id}} <span v-html="text?searchResultHighlightForDocument(text):''" /><br>{{record.status !== 0 && record.web_path?record.web_path:''}}
              </template>
              <span v-html="text?searchResultHighlightForDocument(text):''" />
            </a-popover> -->
            <span v-html="text?searchResultHighlightForDocument(text):''" />
            <a-popover placement="top">
              <template #content>
                点击预览
              </template>
              <FileSearchOutlined 
                @click="previewFile(record)"
                style="margin-left: 4px; color: #2B59E6;"
              />
            </a-popover>
          </template>
          <template v-else-if="column.dataIndex === 'size'">
            {{ calcFileSize(record.input_source, text) }}
          </template>
          <template v-else-if="column.dataIndex === 'creator'">
            {{ record.create_account.name }}
          </template>
          <template v-else-if="column.dataIndex === 'dataset_total'">
            <div v-if="record.status === 0">
              <a @click="gotoDataset(record)">{{ text + " / " + record.chunk_total }}</a><EditOutlined @click="gotoDataset(record)" style="margin-left: 4px; color: #2B59E6;"/>
            </div>
            <div v-else-if="record.status === 2 && record.chunk_total > 0">
              <a @click="gotoDataset(record)">{{ text + " / " + record.chunk_total }}</a><EditOutlined @click="gotoDataset(record)" style="margin-left: 4px; color: #2B59E6;"/>
            </div>
            <div v-else>
              <a @click="gotoDataset(record)">{{ text }}</a><EditOutlined @click="gotoDataset(record)" style="margin-left: 4px; color: #2B59E6;"/>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'create_time'">
            {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <span v-if="text === 0">正在处理&nbsp;<sync-outlined :spin="true" /></span>
            <span v-else-if="text === 1">处理完成</span>
            <span v-else>
              处理失败
              <a-popover placement="right" overlay-class-name="limit-popover-width">
                <template #content>
                  <span>{{record.reason?record.reason:"处理失败"}}</span>
                </template>
                <info-circle-outlined style="color: red;"/>
              </a-popover>
            </span>
          </template>
        </template>
      </a-table>
    </a-tab-pane>
    <a-tab-pane key="5" :tab="`${searchDocumentName} 数据集`">
      <div>
        <!-- <a-typography-title mark :level="5" v-if="searchDocumentId > 0">文档：{{searchDocumentName}}</a-typography-title> -->
        <div style="padding-bottom: 10px;" class="flex justify-between items-center">
          <div style="padding-left: 0px;">
            <a-pagination size="small" 
              v-model:current="datasetCurrent" 
              v-model:pageSize="datasetPageSize"
              :page-size-options="pageSizeOptions"
              :total="datasetTotal" 
              show-less-items 
              show-quick-jumper
              :show-size-changer="true"
              :show-total="total => `共 ${total} 组`"
              @change="onShowSizeChange"
            >
              <template #buildOptionText="props">
                {{ props.value }}组/页 
              </template>
            </a-pagination>
          </div>
          <a-space direction="horizontal">
            <a-input-search
              v-model:value="keyword"
              :placeholder="testChecked?'输入要搜索的问题': '多个关键字用空格分隔'"
              allowClear
              style="width: 250px"
              @search="onSearch"
              @change="onSearchChange"
            />
            <a-input-number id="inputNumber" v-model:value="topK" :min="1" :max="50" placeholder="topK" v-if="testChecked">
              <template #upIcon>
                <ArrowUpOutlined />
              </template>
              <template #downIcon>
                <ArrowDownOutlined />
              </template>
            </a-input-number>
            <a-checkbox v-model:checked="testChecked">
              向量搜索
            </a-checkbox>
          </a-space>
        </div>
        <!-- <div 
          style="height: 75vh;overflow-y: auto;overflow-x: hidden;" 
          ref="scrollWrapper" 
          @scroll="loadMoreHistory"
        > -->
        <div 
          style="height: 75vh;overflow-y: auto;overflow-x: hidden;" 
        >
          <a-empty :image="simpleImage" v-if="datasets.length === 0"/>
          <a-row :gutter="[8,8]" :wrap="true" v-if="datasets.length > 0">
            <a-col
              v-for="item in datasets"
              :key="item.pk.toString()"
              :span="6"
            >
              <a-card size="small">
                <template #title>
                  第 {{ item.page }} 页   # {{ item.seq }} <span v-if="testChecked && keyword">&nbsp;&nbsp;&nbsp;&nbsp;Score: {{item.distance}}</span><FileImageTwoTone v-if="item.attachments.length > 0"/>
                </template>
                <template #extra>
                  <div v-if="item.creator === account.id">
                    <a-button 
                      type="link" 
                      style="color: #1890ff;padding: 0 5px 0 5px;"
                      @click="openEditChunkModal(item)"
                      ><EditOutlined/>
                    </a-button>
                    <a-popconfirm placement="topLeft" 
                      title="确认删除吗？" 
                      @confirm="deleteDatasetRecord(item)" 
                      :okButtonProps="{ loading: loading }"
                      ok-text="确认" 
                      cancel-text="取消"
                    >
                      <a-button 
                        type="link" 
                        style="color:rgba(255, 77, 0, 0.622);padding: 0 5px 0 0;"
                        ><DeleteOutlined/>
                      </a-button>
                    </a-popconfirm>
                  </div>
                </template>
                <div v-if="item.text.length > 100">
                  <span v-html='searchResultHighlight(item.text.slice(0, 100))' />
                  <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width">
                    <template #content>
                      <div class="dataset-card">
                        <div v-html="searchResultHighlight(item.text)"/>
                      </div>
                    </template>
                    <a>&nbsp;&nbsp;...</a>
                  </a-popover>
                </div>
                <div v-if="item.text.length <= 100" v-html="searchResultHighlight(item.text)" class="dataset-card"/>
                <div class="flex justify-between">
                  <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width">
                    <template #content>
                      文档: {{ item.document_name }}
                    </template>
                    <a-typography-text v-if="searchDocumentId === 0" 
                      style="font-size: 12px;width: 50%;" 
                      ellipsis
                      :tooltip="item.document_name"
                      type="secondary">
                      <a @click="gotoDocList(item.document_name)">{{ item.document_name }}</a>
                    </a-typography-text>
                  </a-popover>
                  <a-typography-text 
                    style="font-size: 12px;" 
                    type="secondary"
                  >字符: {{item.text.length}} 命中: {{item.vector_hint_cnt}}
                  </a-typography-text>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-tab-pane>
    <template #rightExtra>
      <a-button type="primary" v-if="activeKey === '5' && searchDocumentId > 0" @click="openEditChunkModal({id: 0, text: null, attachments: [], document_id: searchDocumentId})"><PlusOutlined/>添加分段</a-button>
    </template>
  </a-tabs>
</a-modal>
<template> 
  <a-drawer 
    title="文档预览" 
    :closable="true" 
    size="large" 
    :visible="previewDocumentModal" 
    @close="colsePreviewDocumentModal()"
    :bodyStyle="{ 'overflow-y': 'hidden' }"
  >
    <template #extra>
      <a-button type="primary" style="margin-right: 8px" @click="downloadFile(previewDocument)">下载</a-button>
      <a-button style="margin-right: 8px" @click="colsePreviewDocumentModal()">取消</a-button>
    </template>
    <div style="height: 100%;" v-if="previewDocument.input_source === 1">
      <!-- Loading 动画 -->
      <div v-if="loading" class="iframe_loading">
        <LoadingOutlined />
      </div>
      <iframe
        id="previewDoc"
        :src="previewDocumentUrl"
        frameborder="0"
        style="width: 100%; height: 100%;"
        @load="loading = false"
      ></iframe> 
    </div>
    <div style="height: 100%;overflow-y: auto;" v-else>
      <a-typography-paragraph v-for="(item, index) in previewDatasetList">
        <blockquote>{{ item.text }}</blockquote>
      </a-typography-paragraph>
    </div>
  </a-drawer>
</template>
<EditChunkModal 
  :loading="loading"
  :editChunkVisible="editChunkVisible" 
  :editChunkRecord="editChunkRecord" 
  @setEditChunkVisible="setEditChunkVisible"
  @onFixTextFinishInput="onFixTextFinishInput"
  v-if="editChunkVisible"
/>
<RenameDocumentModal
  :loading="loading"
  :renameVisible="renameVisible"
  :renameRecord="renameRecord" 
  @setRenameVisible="setRenameVisible"
  @saveRenameDocument="saveRenameDocument"
  v-if="renameVisible"
/>
</template>
<script lang="ts" setup>
import { watch, computed, ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import type { UploadProps, UploadChangeParam } from 'ant-design-vue';
import { OssStsTokenProps } from '@/store/thirdparty';
import { useWindowSize } from '@vueuse/core'
import { KnowledegBaseDocumentStore, KnowledegBaseDocumentProps } from '@/store/document';
import { storeToRefs } from 'pinia';
import OSS from 'ali-oss';
import http from '@/store/http';
import { Response } from '@/types';
import dayjs from 'dayjs';
import { 
  getDocumentProcessingProgress, 
  getDocumentPreviewUrl, 
  documentExist, 
  getDatasetPage, 
  saveDataset, 
  deleteDataset 
} from "@/api/document";
import { message, Upload } from 'ant-design-vue';
import getMD5 from "@/utils/getMD5";
import { v4 as uuidv4 } from 'uuid';
import { Empty } from 'ant-design-vue';

import { useAuthStore } from '@/plugins';
import { useAccountStore } from '@/store';
import EditChunkModal from './EditChunkModal.vue';
import RenameDocumentModal from './RenameDocumentModal.vue';

const documentStore = KnowledegBaseDocumentStore();

const { getDocumentPage, addDocument, renameDocument, removeDocument } = documentStore;
const { loading, documentPage } = storeToRefs(documentStore);

const baseFilePath = ref<string>("file/knowledge_base/");
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const { useAuth } = useAuthStore();
const accountStore = useAccountStore();
const {account} = accountStore;

const datasetCurrent = ref(1);
const datasetPageSize = ref(60);
const pageSizeOptions = ref<string[]>(['20', '60', '120', '200']);
const datasets = ref([]);
const datasetTotal = ref(0);
const offset = ref(0);
const limit = ref(60);
const keyword = ref("");
const documentKeyword = ref("");
const searchDocumentId = ref(0);
const searchDocumentName = ref("");
// const loading = ref(false);
const gotoDatasetFlag = ref(false);
const testChecked = ref(false);
const topK = ref(10);
const previewDocumentModal = ref<boolean>(false);
const previewDocument = ref({});
const previewDocumentUrl = ref("");
const previewDatasetList = ref([]);

const docProcessingProgress = ref({});

const subAccountKbUploadDocTotal = ref(0);

const scrollWrapper = ref();

const editChunkVisible = ref(false);
const editChunkRecord = ref({});

const renameVisible = ref(false);
const renameRecord = ref({});

const { height } = useWindowSize();

const onShowSizeChange = (current: number, pageSize: number) => {
  datasetCurrent.value = current;
  datasetPageSize.value = pageSize;
  console.log(current, pageSize);
  refreshDatasetPageData();
};

const loadMoreHistory = () => {
  const scrollWrapperRef = scrollWrapper.value;
  const {scrollTop, clientHeight, scrollHeight} = scrollWrapperRef;
  if (scrollTop + clientHeight >= scrollHeight - 1) {
    refreshDatasetPage(true);
  }
}

const processingTip = computed(() => {
  const unprocessed_file_count = docProcessingProgress.value?.unprocessed_file_count;
  const unprocessed_chunk_count = docProcessingProgress.value?.unprocessed_chunk_count;
  if (unprocessed_file_count > 0){
    const unprocessedParallel = unprocessed_chunk_count / 16 / 4;
    let waitTime;
    if (unprocessedParallel < 60){
      waitTime = `${Math.round(unprocessedParallel)}秒`
    }else if (unprocessedParallel < 3600){
      waitTime = `${Math.round(unprocessedParallel/60)}分钟`
    }else{
      waitTime = `${Math.round(unprocessedParallel/60/60)}小时`
    }
    return `在你之前还有 ${unprocessed_file_count} 个文件等待处理，需要耗时${waitTime}`
  }
  return null
})

function calcDocProcessingProgress(knowledge_base_id){
  getDocumentProcessingProgress(knowledge_base_id).then((response) => {
    console.log(response);
    const { data } = response;
    docProcessingProgress.value = data
  }).catch((e) => {
    console.error(e);
  })
}

const emit = defineEmits(['setDocumentModalVisible'])

const props = defineProps({
  documentModalVisible: Boolean,
  knowledegBaseId: Number,
  knowledgeBaseNamespace: String,
  tabActiveKey: String,
  kbShareTab: String,
});

const activeKey = ref<string>(props.tabActiveKey);

let fileDict = ref({});
let timer = ref<any | null>(null);

function startTimer() {
  if (timer.value) {
    console.log("startTimer:", timer.value);
    return;
  }
  timer.value = setInterval(() => {
    console.log('Timer executed!')
    calcDocProcessingProgress(props.knowledegBaseId);
    getDocumentPage(props.knowledegBaseId);
  }, 5000)
}

function stopTimer() {
  if (timer.value) {
    clearInterval(timer.value)
    console.log('Timer clearInterval')
    timer.value = null
  }
}

function gotoDataset(record){
  activeKey.value = '5';
  gotoDatasetFlag.value = true;
  searchDocumentId.value = record.id;
  searchDocumentName.value = record.name;
}

const progress: UploadProps['progress'] = {
  // strokeColor: {
  //   '0%': '#108ee9',
  //   '100%': '#87d068',
  // },
  strokeWidth: 3,
  format: percent => `${parseInt(percent)}%`,
};

const formState = reactive<Record<string, any>>({
  'use_separator_split': false,
});

const formDataInput = reactive<Record<string, any>>({
  'id': 0,
  'title': `文本：${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
  'description': '',
  'use_separator_split': false,
});

const formDataFetchPageInput = reactive<Record<string, any>>({
  'description': '',
});

const formItemLayout = {
  labelCol: { span: 2 },
  wrapperCol: { span: 12},
};

const submitDisabled = ref(true);

const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo);
};

const renameDocumentName = (record) => {
  renameRecord.value = record;
  setRenameVisible(true);
}

const previewFile = (record) => {
  console.log(record);
  if (record.input_source === 1){
    getDocumentPreviewUrl(record.id).then((response) => {
      console.log(response);
      const { data } = response;
      const { preview_url } = data;
      // window.open(preview_url, '_blank')
      previewDocumentModal.value = true;
      previewDocumentUrl.value = preview_url;
      previewDocument.value = record;
      loading.value = true;
    }).catch((e) => {
      console.error(e);
      message.error("预览失败");
    })
  }else{
    previewDocumentModal.value = true;
    previewDocument.value = record;
    getPreviewDatasetData(record.id);
  }
} 

function downloadTxt(filename, textContent) {
  // 创建一个Blob实例，类型为纯文本
  const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
  // 创建一个指向Blob的URL
  const url = URL.createObjectURL(blob);
  // 创建一个a标签用于下载
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename + '.txt');
  document.body.appendChild(link);
  // 触发下载
  link.click();
  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

const downloadFile = (record) => {
  console.log(record);
  if(record.input_source === 1){
    const filename = record.name;
    const oss_key = record.oss_key;
    
    const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
    const client = new OSS({
      region: ossStsToken.value.region,
      accessKeyId: ossStsToken.value.access_key_id,
      accessKeySecret: ossStsToken.value.access_key_secret,
      stsToken: ossStsToken.value.security_token,
      bucket: ossStsToken.value.bucket_name,
      endpoint: ossStsToken.value.endpoint,
      useFetch: true,
      cname: use_cname,
      secure: true,
    });

    // 配置响应头实现通过URL访问时自动下载文件，并设置下载后的文件名。
    const response = {
      "content-disposition": `attachment; filename=${encodeURIComponent(
        filename
      )}`,
    };
    // 填写Object完整路径。Object完整路径中不能包含Bucket名称。
    const url = client.signatureUrl(oss_key, { response });
    console.log(url);
    window.open(url, '_blank')
  }else{
    const downloadContent = previewDatasetList.value.map(item=>item.text).join("\n");
    downloadTxt(record.name, downloadContent);
  }
}

function colsePreviewDocumentModal() {
  previewDocumentModal.value = false;
  previewDocumentUrl.value = "";
  previewDocument.value = {};
  previewDatasetList.value = [];
}

const calcFileSize = (input_source, val) => {
  if (input_source !== 1){
    return `${val} 字符`
  }
  if (val === 0) return '0 B';
  var k = 1024;
  var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]
  let i = Math.floor(Math.log(val) / Math.log(k));
  return (val / Math.pow(k, i)).toPrecision(3) + " " + sizes[i];
}

const onFinish = async (values: any) => {
  loading.value = true;
  console.log('Success:', values, fileDict.value, props.knowledegBaseId);
  let files = [];
  Object.keys(fileDict.value).forEach(function(key){
    const f = fileDict.value[key];
    delete f["status"];
    files.push(f);
  });
  console.log(files);
  const data = {
    input_source: 1,
    use_separator_split: formState.use_separator_split,
    knowledge_base_id: props.knowledegBaseId,
    files: files,
  };
  await addDocument(data);
  activeKey.value = '2';
  formState.dragger = [];
  fileDict.value = {};
  submitDisabled.value = true;
  // 定时查看处理状态
  startTimer();
};

const onFinishInput = (values: any) => {
  console.log('Success:', values, props.knowledegBaseId);
  const data = {
    input_source: 2,
    knowledge_base_id: props.knowledegBaseId,
    id: formDataInput.id,
    title: formDataInput.title,
    description: formDataInput.description,
    use_separator_split: formDataInput.use_separator_split,
  };
  addDocument(data).then((response) => {
    console.log(response);
    const { code } = response;
    if (code === 0){
      activeKey.value = '2';
      formDataInput.id = 0
      formDataInput.title = ''
      formDataInput.description = ''
      formDataInput.use_separator_split = false
      // 定时查看处理状态
      startTimer();
    }else{
      message.success("提交失败")
    }
  }).catch((e) => {
    console.error(e);
    message.success("提交失败")
  }).finally(() => {
    loading.value = false;
  });
};

const onFinishFetchPageInput = (values: any) => {
  console.log('Success:', values, props.knowledegBaseId);
  const data = {
    input_source: 3,
    knowledge_base_id: props.knowledegBaseId,
    description: formDataFetchPageInput.description,
  };
  addDocument(data).then((response) => {
    console.log(response);
    const { code } = response;
    if (code === 0){
      activeKey.value = '2';
      formDataFetchPageInput.description = ''
      // 定时查看处理状态
      startTimer();
    }else{
      message.success("提交失败")
    }
  }).catch((e) => {
    console.error(e);
    message.success("提交失败")
  }).finally(() => {
    loading.value = false;
  });
};

const remove = useAuth('delete', async function (record: KnowledegBaseDocumentProps) {
  await removeDocument(record.knowledge_base_id, record.id);
}); 

// 列表当前页更改
const handleTableChange = async (page, filters, sorter) => {
  console.log(page);
  await getDocumentPage(props.knowledegBaseId, page.current, page.pageSize);
};

const columns = [
    {
      title: '文档名称',
      align: 'left',
      // width: 600,
      dataIndex: 'name',
      key: 'name',
      // ellipsis: true,
    },
    {
      title: '字符数',
      align: 'center',
      dataIndex: 'dataset_character_count',
      key: 'dataset_character_count',
      width: 120,
      ellipsis: true,
    },
    {
      title: '召回次数',
      align: 'center',
      dataIndex: 'dataset_hint_cnt',
      key: 'dataset_hint_cnt',
      width: 120,
    },
    {
      title: '上传时间',
      align: 'center',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 160,
    },
    {
      title: '创建者',
      align: 'center',
      dataIndex: 'creator',
      key: 'creator',
      width: 120,
      ellipsis: true,
    },
    {
      title: '处理状态',
      align: 'left',
      dataIndex: 'status',
      key: 'status',
      width: 100,
    },
    {
      title: '数据集',
      align: 'center',
      dataIndex: 'dataset_total',
      key: 'dataset_total',
      width: 180,
    },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'operation',
      key: 'operation',
      width: 120,
    },
];

const setModalVisible = (visible: boolean) => {
  emit('setDocumentModalVisible', visible);
};

async function customRequest(info) {
  console.log(info);
  const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
  const client = new OSS({
    region: ossStsToken.value.region,
    accessKeyId: ossStsToken.value.access_key_id,
    accessKeySecret: ossStsToken.value.access_key_secret,
    stsToken: ossStsToken.value.security_token,
    bucket: ossStsToken.value.bucket_name,
    endpoint: ossStsToken.value.endpoint,
    useFetch: true,
    cname: use_cname,
    secure: true,
  });
  const oss_key = fileDict.value[info.file.uid]["oss_key"];
  if(info.file){
    await client.multipartUpload(oss_key, info.file, {
      parallel: 4,
      partSize: 100 * 1024,
      progress: function (percent) {
        const fileIdx = formState.dragger.findIndex(obj => obj.uid === info.file.uid);
        formState.dragger[fileIdx].percent = percent * 100;
        if (percent === 1){
          formState.dragger[fileIdx].status = "success";
        }
      },
    }).then(res => {
      console.log('结果:', res);
      fileDict.value[info.file.uid]["status"] = 1;
    }).catch((err) => {
      console.log(err);
    });
  }
}

const handleChange = async (info: UploadChangeParam) => {
  // console.log(props.knowledegBaseId, info);
  // console.log(fileDict.value);
  const status = info.file.status;
  
  if(status === 'removed') {
    const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
    const oss_key = fileDict.value[info.file.uid]["oss_key"];
    const client = new OSS({
      region: ossStsToken.value.region,
      accessKeyId: ossStsToken.value.access_key_id,
      accessKeySecret: ossStsToken.value.access_key_secret,
      stsToken: ossStsToken.value.security_token,
      bucket: ossStsToken.value.bucket_name,
      endpoint: ossStsToken.value.endpoint,
      cname: use_cname,
      secure: true,
    });
    let result = await client.delete(oss_key);
    console.log("removed file:", oss_key, result);
    delete fileDict.value[info.file.uid];
  }
};

const fileExist = async (knowledegBaseId, md5) => {
  return documentExist(knowledegBaseId, md5).then((response) => {
    console.log(response);
    const { data } = response;
    const { exist, total } = data;
    subAccountKbUploadDocTotal.value = total;
    return exist;
  }).catch((e) => {
    console.error(e);
    message.error("预览失败");
  });
}

const beforeUpload: UploadProps['beforeUpload'] = async file => {
  const fileSizeLimit = 200; // KB
  const isGtFileSizeLimit = file.size / 1024 > fileSizeLimit;
  if (!account.is_superuser && isGtFileSizeLimit) {
    message.warn(`单个文件最大${fileSizeLimit}KB!`);
    return Upload.LIST_IGNORE;
  }
  let md5 = await getMD5(file);
  let sameFiles = [];
  console.log("beforeUpload", md5);
  Object.keys(fileDict.value).forEach(function(key){
    const f = fileDict.value[key];
    console.log("inner", f.md5);
    if (f.md5 === md5){
      sameFiles.push(f.name);
    }
  });
  console.log(sameFiles);
  if (sameFiles.length > 0){
    delete fileDict.value[file.uid];
    message.warn(`与"${sameFiles[0]}"文件内容相同，不用重复上传!`);
    return Upload.LIST_IGNORE;
  }
  const fileExtName = file.name.substring(file.name.lastIndexOf(".") + 1);
  const fileName = uuidv4() + "." + fileExtName;
  const oss_key = baseFilePath.value+props.knowledegBaseId+'/'+fileName;

  const fileData = {name: file.name, oss_key: oss_key, size: file.size, status: 0, md5};
  console.log(fileData);
  fileDict.value[file.uid] = fileData;
  
  const exist = await fileExist(props.knowledegBaseId, md5);
  console.log(exist);
  if (exist) {
    delete fileDict.value[file.uid];
    message.warn(`${file.name}已上传过，不需要再次上传!`);
    return Upload.LIST_IGNORE;
  }
  const accountMaxUploadCountLimit = 5;
  // const uploadTotal = Object.keys(fileDict.value).length + subAccountKbUploadDocTotal.value
  const uploadTotal = Object.keys(fileDict.value).length
  if (!account.is_superuser && uploadTotal > accountMaxUploadCountLimit) {
    delete fileDict.value[file.uid];
    message.warn(`每次最多可上传${accountMaxUploadCountLimit}个文件!`);
    return Upload.LIST_IGNORE;
  }
  return true;
};

let ossStsToken = ref<OssStsTokenProps>();

async function getOssStsToken() {
  return http
    .request<OssStsTokenProps, Response<OssStsTokenProps>>('/thirdparty/oss/sts_token', 'GET')
    .then((res) => {
      console.log(res);
      const { data } = res;
      ossStsToken.value = data;
      return data;
    })
    .finally(() => (console.log("end")));
}

const onDocumentSearch = (searchValue: string) => {
  // documentKeyword.value = searchValue;
  getDocumentPage(props.knowledegBaseId, 1, 50, documentKeyword.value)
};

const onDocumentSearchChange = (v: string) => {
  onDocumentSearch('');
}

const onSearch = (searchValue: string) => {
  // keyword.value = searchValue;
  offset.value = 0;
  limit.value = 60;
  datasets.value = [];
  refreshDatasetPage(false)
};

const onSearchChange = (v: string) => {
  console.log('change use value', v);
  onSearch('')
};

const onFixTextFinishInput = (item) => {
  loading.value = true;
  const params = {
    id: item.id,
    text: item.text,
    document_id: item.document_id,
    attachments: item.attachments,
  };
  saveDataset(params).then((response) => {
    console.log(response);
    const { code, data } = response;
    if (code === 0){
      if (params.id > 0){
        datasets.value.forEach(dataset => {
          if(dataset.id === params.id){
            dataset.text = params.text;
            dataset.attachments = params.attachments;
          }
        });
      }else{
        if(datasets.value.length === 0){
          data["seq"] = 1
        }else{
          const seq = datasets.value.slice(-1)[0].seq
          data["seq"] = seq + 1
        }
        console.log(data);
        datasets.value.push(data);
      }
      message.success("保存成功")
    }else{
      message.error("保存失败")
    }
  }).catch((e) => {
    console.error(e);
    message.error("保存失败")
  }).finally(() => (loading.value = false));
};

const saveRenameDocument = (item) => {
  const data = {
    knowledge_base_id: props.knowledegBaseId,
    id: item.id,
    name: item.name,
  };
  renameDocument(data).then((response) => {
    console.log(response);
    const { code } = response;
    if (code === 0){
      activeKey.value = '2';
    }else{
      message.error("重命名失败")
    }
  }).catch((e) => {
    console.error(e);
    message.error("重命名失败")
  }).finally(() => {
    loading.value = false;
  });
}

function deleteDatasetRecord(item){
  loading.value = true;
  console.log(item.id);
  deleteDataset({id: item.id}).then((response) => {
    console.log(response);
    const { data } = response;
    console.log(data);
    datasets.value = datasets.value.filter(dataset => dataset.id !== item.id)
    datasetTotal.value = datasetTotal.value - 1
  }).catch((e) => {
    console.error(e);
    message.error("删除数据集失败");
  }).finally(() => (loading.value = false));
}

function refreshDatasetPage(scrollLoad){
  const params = {
    knowledge_base_id: props.knowledegBaseId, 
    offset: offset.value, limit: limit.value, keyword: keyword.value, 
    document_id: searchDocumentId.value, vector: testChecked.value, 
    topK: topK.value,
  }
  return getDatasetPage(params).then((response) => {
    console.log(response);
    const { data } = response;
    console.log(data);
    const { total, results } = data;
    if(total === datasets.value.length){
      datasetTotal.value = total;
      return
    }
    results.forEach(item => {
      datasets.value.push(item);
    });
    if (scrollLoad === true){
      offset.value = offset.value + limit.value;
    }else{
      offset.value = 0;
      limit.value = 60;
    }
    datasetTotal.value = total;
    return data;
  }).catch((e) => {
    console.error(e);
    message.error("数据集加载失败");
  });
}

function getPreviewDatasetData(document_id){
  const offset = 0;
  const params = {
    knowledge_base_id: props.knowledegBaseId, offset: offset, 
    limit: 10000, document_id: document_id
  }
  return getDatasetPage(params).then((response) => {
    console.log(response);
    const { data } = response;
    console.log(data);
    const { results } = data;
    previewDatasetList.value = results;
    return results;
  }).catch((e) => {
    console.error(e);
    message.error("数据集加载失败");
  });
}

function refreshDatasetPageData(){
  let offset;
  if (datasetCurrent.value === 0){
    offset = 0
  }else{
    offset = (datasetCurrent.value-1) * datasetPageSize.value
  }
  const params = {
    knowledge_base_id: props.knowledegBaseId, 
    offset: offset, limit: datasetPageSize.value, keyword: keyword.value, 
    document_id: searchDocumentId.value, vector: testChecked.value, 
    topK: topK.value,
  }
  return getDatasetPage(params).then((response) => {
    console.log(response);
    const { data } = response;
    console.log(data);
    const { total, results } = data;
    datasets.value = results;
    datasetTotal.value = total;
    return data;
  }).catch((e) => {
    console.error(e);
    message.error("数据集加载失败");
  });
}

const searchResultHighlight = (content) => {
  if(!keyword.value){
    return content;
  }
  const keywords = keyword.value.split(" ")
  keywords.forEach((item) => {
    if(item){
      const reg = new RegExp(`(${item})`, "ig");
      content = content.replace(
        reg,
        `<span style="background-color:#ffe58f">$1</span>`
      );
    }
  });
  return content;
}

const searchResultHighlightForDocument = (content) => {
  if(!documentKeyword.value){
    return content;
  }
  const keywords = documentKeyword.value.split(" ")
  keywords.forEach((item) => {
    if(item){
      const reg = new RegExp(`(${item})`, "ig");
      content = content.replace(
        reg,
        `<span style="color:#1377FF">$1</span>`
      );
    }
  });
  return content;
}

const gotoDocList = (doc_name) => {
  activeKey.value = '2';
  onDocumentSearch(doc_name);
}

function setEditChunkVisible(visible){
  editChunkVisible.value = visible;
}

function setRenameVisible(visible){
  renameVisible.value = visible;
}

function openEditChunkModal(record) {
  // let tmp = {}
  // tmp["id"] = record.id
  // tmp["text"] = record.text
  // tmp["image_urls"] = []
  editChunkRecord.value = record
  setEditChunkVisible(true);
}

watch(documentPage, () => {
  const results = documentPage.value?.results || [];
  let waitList = results.filter((doc)=> doc.status === 0);
  console.log(waitList);
  if (waitList.length === 0){
    stopTimer();
  }else{
    startTimer();
  }
});

watch(
    fileDict,
    (newValue, oldValue) => {
      console.log(fileDict.value);
      if (Object.keys(fileDict.value).length === 0){
        submitDisabled.value = true;
        return
      }
      let uploadingFiles = [];
      Object.keys(fileDict.value).forEach(function(key){
        const f = fileDict.value[key];
        if (f.status === 0){
          uploadingFiles.push(f.uid);
        }
      });
      if(uploadingFiles.length > 0){
        submitDisabled.value = true;
        return
      }
      submitDisabled.value = false;
    },
    {
      deep: true
    }
  )

watch(activeKey, async (val) => {
  if(val === '5'){
    offset.value = 0;
    limit.value = 60;
    if (gotoDatasetFlag.value === false){
      searchDocumentId.value = 0;
      searchDocumentName.value = "";
    }
    datasetTotal.value = 0;
    datasets.value = [];
    refreshDatasetPageData();
  }else if (val === '2'){
    gotoDatasetFlag.value = false;
    searchDocumentId.value = 0;
    searchDocumentName.value = "";
    await getDocumentPage(props.knowledegBaseId)
  }else{
    gotoDatasetFlag.value = false;
    searchDocumentId.value = 0;
    searchDocumentName.value = "";
  }
})

watch(datasetPageSize, () => {
  console.log('datasetPageSize: ', datasetPageSize.value);
});

const uploadListHeight = computed(() => {
  if (formState.dragger){
    return `${formState.dragger.length * 42}px`  
  }
  return '0px'
})

onMounted(async () => {
  await getOssStsToken();
  await getDocumentPage(props.knowledegBaseId);
  refreshDatasetPageData();
  calcDocProcessingProgress(props.knowledegBaseId);
});

onBeforeUnmount(() => {
  stopTimer();
});
</script>
<style lang="less">
.iframe_loading{
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 20px;
  color: #666;
  background-color: #f0f0f0;
}

.ant-upload-list {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: var(--color-text-1);
  font-size: var(--font-size);
  font-variant: tabular-nums;
  list-style: none;
  font-feature-settings: 'tnum';
  line-height: 1.5715;
  height: var(--upload-list-height);
  max-height: 40vh;
  overflow-y: auto;
}

.bg-txt {
  background-color: #87d068;
}

.bg-file {
  background-color: #1890ff;
}

.bg-web {
  background-color: #f56a00;
}

.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }
  .ant-modal-body {
    flex: 1;
  }
}

.limit-popover-width {
  max-width: 50%;
  word-wrap: break-word;
}

.limit-popover-width1 {
  max-width: 70%;
}
</style>
<style lang="less" scoped>
.dataset-card{
  :deep(img){
    max-width: 100%;
  }
}
</style>