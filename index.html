<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="<%- VITE_SITE_ICON %>" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%- VITE_SITE_TITLE %></title>
  </head>
  <body>
    <!-- <script src="https://res.isapientia.com/js/bot.messenger.js"></script> -->
    <div id="stepin-app">
      <style>
        body {
          height: 100vh;
          text-align: center;
          align-items: center;
          background-color: #003f8c;
          display: flex;
          justify-content: center;
        }
        .loader {
          color: rgb(124, 124, 124);
          font-family: "Poppins",sans-serif;
          font-weight: 500;
          font-size: 25px;
          -webkit-box-sizing: content-box;
          box-sizing: content-box;
          height: 40px;
          padding: 10px 10px;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          border-radius: 8px;
        }

        .words {
          overflow: hidden;
        }

        .word {
          display: block;
          height: 100%;
          padding-left: 6px;
          color: #ffca29;
          animation: spin_4991 4s infinite;
        }

        @keyframes spin_4991 {
          10% {
            -webkit-transform: translateY(-105%);
            transform: translateY(-105%);
          }

          25% {
            -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
          }

          35% {
            -webkit-transform: translateY(-205%);
            transform: translateY(-205%);
          }

          50% {
            -webkit-transform: translateY(-200%);
            transform: translateY(-200%);
          }

          60% {
            -webkit-transform: translateY(-305%);
            transform: translateY(-305%);
          }

          75% {
            -webkit-transform: translateY(-300%);
            transform: translateY(-300%);
          }

          85% {
            -webkit-transform: translateY(-405%);
            transform: translateY(-405%);
          }

          100% {
            -webkit-transform: translateY(-400%);
            transform: translateY(-400%);
          }
        }
      </style>
      <img style="margin-bottom: 8px;" width="64px" src="https://res.isapientia.com/img/tsinghua_icenter_icon.png" />
      <div class="loader">
        <div>loading</div>
        <div class="words">
            <span class="word">人工智能</span>
            <span class="word">创新创业</span>
            <span class="word">引领示范</span>
            <span class="word">大语言模型</span>
            <span class="word">知识库管理</span>
            <!-- <span class="word">智能问答</span> -->
        </div>
      </div>
      <div class="" style="color: rgba(255, 255, 255, 0.35)">加载中...</div>
    </div>
    <%- injectScript %>
    <script type="module" src="/src/main.ts"></script>
    <script>
      if (!global) {
        var global = globalThis;
      }
    </script>
  </body>
</html>
