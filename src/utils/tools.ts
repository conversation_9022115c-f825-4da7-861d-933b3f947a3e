import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
// import 'highlight.js/styles/github.css';
// import 'highlight.js/styles/github-dark.css';
import 'highlight.js/styles/monokai.css';

export const md = new MarkdownIt(
  {
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,        // Convert '\n' in paragraphs into <br>
    highlight: function (str, lang) {
      console.log(lang);
      if (lang && hljs.getLanguage(lang)) {
        try {
          return '<pre class="hljs"><section class="__code-block-copy-button__">复制代码</section><code>' +
            hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
            '</code></pre>';
          // return '<pre class="hljs"><code>' + hljs.highlight(str, { language: lang, ignoreIllegals: true }).value + '</code></pre>';
        } catch (__) { }
      }
      return '<pre class="hljs"><section class="__code-block-copy-button__">复制代码</section><code>' + md.utils.escapeHtml(str) + '</code></pre>'
      // return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
    }
  }
);

export function mdStr2html(str, model = 'render') {
  // return md.render(str)
  return '<pre class="hljs"><section class="__code-block-copy-button__">复制代码</section><code><br>' +
            hljs.highlight(str, { language: "html", ignoreIllegals: true }).value +
            '</code></pre>';
}