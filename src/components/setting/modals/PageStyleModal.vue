<template>
  <a-modal 
    :visible="visible" 
    title="选择页面风格" 
    @ok="handleClose"
    @cancel="handleClose"
    @close="handleClose"
    width="900px"
    :footer="null"
  >
    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
      <a-card 
        v-for="(style, index) in stylePresets" 
        :key="index"
        class="style-card"
        :class="{ 'style-card-selected': styleData.style_preset === style.name }"
        hoverable
        :bordered="styleData.style_preset === style.name"
        @click="selectPageStyle(style)"
        :style="{
          borderColor: styleData.style_preset === style.name ? style.primaryColor : '#ebedf0',
          borderWidth: styleData.style_preset === style.name ? '2px' : '1px',
          transition: 'all 0.3s',
        }"
      >
        <template #title>
          <div style="text-align: center; font-weight: bold; font-size: 15px;">{{ style.name }}</div>
        </template>
        <div :style="{ backgroundColor: style.backgroundColor, padding: '6px', borderRadius: '6px' }">
          <div :style="{ 
            backgroundColor: style.primaryColor, 
            color: '#fff', 
            padding: '6px', 
            borderRadius: '4px',
            marginBottom: '6px',
            textAlign: 'center',
            fontSize: '13px'
          }">
            主题色按钮
          </div>
          <div :style="{ 
            color: style.textColor,
            padding: '6px',
            fontSize: '12px'
          }">
            文本内容示例
          </div>
          <div :style="{
            backgroundColor: style.accentColor,
            color: '#fff',
            padding: '4px 8px',
            borderRadius: '4px',
            display: 'inline-block',
            marginTop: '6px',
            fontSize: '11px'
          }">
            强调色
          </div>
        </div>
      </a-card>
    </div>
    <div style="margin-top: 20px; text-align: right;">
      <a-button type="primary" @click="confirm">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';

export default defineComponent({
  name: 'PageStyleModal',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    styleData: {
      type: Object,
      default: () => ({
        style_preset: '',
        style: {
          primaryColor: '#3b82f6',
          secondaryColor: '#f8fafc',
          textColor: '#1e293b',
          backgroundColor: '#f1f5f9',
          accentColor: '#2563eb'
        }
      })
    }
  },
  
  emits: ['update:visible', 'update:styleData', 'confirm'],
  
  setup(props, { emit }) {
    const stylePresets = [
      {
        name: "清华紫",
        primaryColor: "#73407e",
        secondaryColor: "#f5f3f7",
        textColor: "#2d1b35",
        backgroundColor: "#faf9fb",
        accentColor: "#5b2c68",
      },
      {
        name: "浅海蓝",
        primaryColor: "#60a5fa",
        secondaryColor: "#e2e8f0",
        textColor: "#1e293b",
        backgroundColor: "#f8fafc",
        accentColor: "#3b82f6",
      },
      {
        name: "深空蓝",
        primaryColor: "#2b59e6",
        secondaryColor: "#f1f4fd",
        textColor: "#1a202c",
        backgroundColor: "#f8faff",
        accentColor: "#1e40af",
      },
      {
        name: "清新绿",
        primaryColor: "#10b981",
        secondaryColor: "#d1fae5",
        textColor: "#1e293b",
        backgroundColor: "#f0fdf4",
        accentColor: "#059669",
      },
      {
        name: "温暖橙",
        primaryColor: "#f97316",
        secondaryColor: "#ffedd5",
        textColor: "#1e293b",
        backgroundColor: "#fff7ed",
        accentColor: "#ea580c",
      },
      {
        name: "商务灰",
        primaryColor: "#64748b",
        secondaryColor: "#f1f5f9",
        textColor: "#1e293b",
        backgroundColor: "#f8fafc",
        accentColor: "#475569",
      },
      {
        name: "活力红",
        primaryColor: "#ef4444",
        secondaryColor: "#fee2e2",
        textColor: "#1e293b",
        backgroundColor: "#fef2f2",
        accentColor: "#dc2626",
      },
      {
        name: "沉稳黑",
        primaryColor: "#787878",
        secondaryColor: "#e2e8f0",
        textColor: "#f8fafc",
        backgroundColor: "#282828",
        accentColor: "#1e293b",
      },
      {
        name: "简约白",
        primaryColor: "#6b7280",
        secondaryColor: "#f3f4f6",
        textColor: "#1f2937",
        backgroundColor: "#ffffff",
        accentColor: "#4b5563",
      },
    ];
    
    const selectedStyle = ref(null);
    
    // When the modal opens, find the currently selected style
    watch(() => props.visible, (isVisible) => {
      if (isVisible && props.styleData.style_preset) {
        selectedStyle.value = stylePresets.find(
          style => style.name === props.styleData.style_preset
        ) || stylePresets[0];
      }
    });
    
    // Select a style
    const selectPageStyle = (style) => {
      selectedStyle.value = style;
      
      // Update the style data
      emit('update:styleData', {
        style_preset: style.name,
        style: {
          primaryColor: style.primaryColor,
          secondaryColor: style.secondaryColor,
          textColor: style.textColor,
          backgroundColor: style.backgroundColor,
          accentColor: style.accentColor
        }
      });
    };
    
    // Confirm selection
    const confirm = () => {
      emit('confirm');
      emit('update:visible', false);
    };
    
    const handleClose = () => {
      emit('update:visible', false);
    };
    
    return {
      stylePresets,
      selectedStyle,
      selectPageStyle,
      confirm,
      handleClose
    };
  }
});
</script>

<style scoped>
.style-card {
  min-width: 0;
  width: 100%;
  max-width: 220px;
  min-height: 120px;
  font-size: 13px;
  margin: 0 auto;
  box-sizing: border-box;
}

.style-card :deep(.ant-card-body) {
  padding: 10px !important;
}

.style-card-selected {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style> 