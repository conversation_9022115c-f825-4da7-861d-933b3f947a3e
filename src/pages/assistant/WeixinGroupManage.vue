<template>
  <a-table 
    size="small"
    :row-selection="rowSelection"
    :showSorterTooltip="false"
    :columns="columns" 
    :dataSource="groupPage.results"
    :pagination="{ pageSize: groupPage.page_size, current: groupPage.page_number, total: groupPage.count }"
    @change="handleTableChange"
    :scroll="{y: (height - 290)}"
    >
    <template #title>
      <div class="text-subtext text-sm flex justify-between items-center">
        <span style="font-size:14px;">{{accountLabel}}对话管理，对话的自动回复及群发消息。</span>
        <div>
          <a-select
            style="margin-right: 10px;"
            placeholder="对话区分"
            :options="categoryOptions"
            allowClear
            @change="handleCategoryChange"
          ></a-select>
          <a-input-search
            v-model:value="keyword"
            placeholder="搜索群，多个关键字用空格分隔"
            style="width: 300px;"
            allowClear
            @search="onSearch"
          />
          <a-button style="margin-left: 10px;" type="primary" @click="groupSendNotice()">
            <SendOutlined class="text-lg" />群发消息
          </a-button>
          <a-button style="margin-left: 10px;" type="primary" @click="addListenGroup()" v-if="accountType === 2">
            <PlusOutlined class="text-lg" />添加对话
          </a-button>
        </div>
      </div>
    </template>
    <template #headerCell="{ column }">
      <template v-if="column.key === 'app_name'">
        关联应用项目
        <a-popover title="" trigger="hover" placement="right">
          <template #content>
            群助手在群中被@的提问，回复时会@发送人回复；<br>
            如是个人微信的提问会直接回复。
          </template>
          <QuestionCircleOutlined />
        </a-popover>
      </template>
    </template>
    <template #bodyCell="{ text, record, index, column }">
      <template v-if="column.key === 'nickname'">
        <span v-html="text?searchResultHighlight(text, keyword):''" />
        <span style="margin-left: 10px;">
          <a-tag color="green" v-if="record.category === 3">外部</a-tag>
          <a-tag color="error" v-if="record.delete_flag">已解散</a-tag>
        </span>
        <a-popover placement="right" overlay-class-name="limit-popover-width">
          <template #content>
            <span>当前对话名称在助手客户端中未找到，请检查名称是否填写正确或真实存在。</span>
          </template>
          <info-circle-outlined style="color: red;margin-left: 4px;" v-if="!record.exist"/>
        </a-popover>
      </template>
      <template v-else-if="column.dataIndex === 'category'">
        {{record.category === 2?"个人微信":"微信群"}}
      </template>
      <template v-else-if="column.dataIndex === 'app_name'">
        <a-dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            <span v-if="record.release_id === 0">
              请选择
            </span>
            <span v-if="record.release_id > 0 && releaseAppList.length > 0">
              {{ releaseAppList.filter((item) => parseInt(item.value) === record.release_id)[0]['label'] }}
            </span>
            <DownOutlined />
          </a>
          <template #overlay>
            <a-menu v-if="releaseAppList.length > 0">
              <a-menu-item v-for="item in releaseAppList.filter((item) => parseInt(item.value) !== record.release_id)">
                <a @click="updateGroupRelease(record, item.value)">{{ item.label }}</a>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-tooltip placement="topLeft">
          <template #title>
            <span>解除关联的应用项目</span>
          </template>
          <span style="margin-left: 4px;cursor: pointer;" v-if="record.release_id > 0">
            <CloseCircleOutlined @click="updateGroupRelease(record, '0')"/>
          </span>
        </a-tooltip>
      </template>
      <template v-else-if="column.dataIndex === 'account'">
        {{ record.weixin_account?.account }}
      </template>
      <template v-else-if="column.dataIndex === 'create_time'">
        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
      <template v-else-if="column.dataIndex === 'last_answer_time'">
        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
      <template v-else-if="column.dataIndex === 'operation'">
        <a style="font-weight: normal;" @click="editListenGroup(record)" v-if="accountType === 2 || (accountType === 1 && record.category !== 2)">修改</a>
        <a-divider type="vertical" v-if="accountType === 2 || (accountType === 1 && record.category !== 2)"/>
        <a style="font-weight: normal;" @click="viewGroupMessage(record)">对话消息</a>
      </template>
    </template>
  </a-table>
  <a-modal :okButtonProps="{ loading }" width="540px"
    v-model:visible="addGroupVisible" :title="`${addListenGroupFormData.id?'修改对话':'添加对话'}`" 
      @ok="addListenGroupSubmit" 
      @cancel="changeAddListenGroup(false)"
      ok-text="确认" cancel-text="取消"
    >
    <a-form ref="form" :model="addListenGroupFormData" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16, offset: 1 }">
      <a-form-item required name="weixin_account_id" :label="`所属${accountLabel}账号`"
        :rules="[{ required: true, message: `请选择所属${accountLabel}账号` }]"
        v-if="accountType === 2"
      >
        <a-select
          :placeholder="`选择所属${accountLabel}账号`"
          v-model:value="addListenGroupFormData.weixin_account_id"
          :options="groupAssistantList"
        ></a-select>
      </a-form-item>
      <a-form-item required name="nickname" label="对话名称"
        :rules="[{ required: true, message: '请输入对话名称' }]"
        v-if="accountType === 2"
      >
        <a-input v-model:value="addListenGroupFormData.nickname" placeholder="请输入对话名称" show-count :maxlength="100" />
      </a-form-item>
      <a-form-item required name="category" label="对话区分" v-if="accountType === 2">
        <a-radio-group v-model:value="addListenGroupFormData.category">
          <a-radio value="1">微信群</a-radio>
          <a-radio value="2">个人微信</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item 
          help="群助手在当前群里的昵称"
          name="bot_nickname" 
          label="群里的昵称" 
          v-if="addListenGroupFormData.category !== '2'">
        <a-input 
        v-model:value="addListenGroupFormData.bot_nickname" 
        placeholder="请输入群里的昵称" 
        show-count :maxlength="100" />
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal 
    :okButtonProps="{ saveLoading }" 
    width="540px" 
    v-model:visible="groupSendVisible" 
    title="群发消息"
    @ok="groupSendSubmit" 
    @cancel="changeGroupSendNotice(false);"
        ok-text="确认" cancel-text="取消"
    >
    <a-radio-group v-model:value="msgTypeRadio" style="margin: 8px">
      <a-radio-button value="1">文本</a-radio-button>
      <a-radio-button value="3">图片</a-radio-button> 
    </a-radio-group>
    <div style="margin-left: 8px;">
      <a-textarea v-model:value="sendContent" 
        placeholder="请填写发送消息的内容" :rows="14" :maxlength=3000 show-count 
        v-if="msgTypeRadio === '1'"
      />
      <a-upload list-type="picture-card" accept=".png,.jpg,.jpeg,.webp,.gif"
          v-model:fileList="formDataInput.image_urls"
          :show-upload-list="false"
          :customRequest="customRequest"
          :before-upload="beforeUpload"
          @change="handleChange"
          class="avatar-uploader"
          v-if="msgTypeRadio === '3'"
        >
        <div>
          <img v-if="imageUrl" :src="imageUrl" alt="picture" style="width: 248px;height: 248px;"/>
          <div v-else>
            <loading-outlined v-if="loading"></loading-outlined>
            <plus-outlined v-else></plus-outlined>
            <div class="ant-upload-text">上传</div>
          </div>
        </div>
      </a-upload>
    </div>
    <div style="margin-left: 8px;margin-top: 8px;">
      <a-date-picker 
        v-model:value="expectSendTime"
        show-time 
        placeholder="请选择发送时间" 
        format="YYYY-MM-DD HH:mm"
      ></a-date-picker>
    </div>
  </a-modal>
  <a-drawer 
    title="对话消息"
    :closable="true" 
    size="large" 
    :visible="groupMessageDrawer" 
    @close="colseGroupMessageDrawer()"
  >
    <template #extra>
      <a-range-picker v-model:value="searchDate"
        :disabled-date="disabledDate"
        :placeholder="['开始日期', '结束日期']"
        allowClear
        @calendarChange="onCalendarChange"
      />
      <a-button style="margin-right: 8px;margin-left: 8px;" @click="exportData()">导出</a-button>
    </template>
    <a-list
      class="demo-loadmore-list"
      item-layout="horizontal"
      :data-source="groupMessageList"
    >
      <template #renderItem="{ item }">
        <a-list-item>
          <a-skeleton avatar :title="false" :loading="!!item.loading" active>
            <a-list-item-meta>
              <template #title>
                <span>{{ item.sender_name }}</span>
                <span style="margin-left: 10px;color: var(--color-text-3);">{{ item.create_time?dayjs(item.create_time).format('YYYY-MM-DD HH:mm:ss'):'-' }}</span>
              </template>
              <template #description>
                <!-- text -->
                <div style="color: var(--color-text-1);" v-html="replaceUrlsWithAnchors(item.content)" v-if="[11041, 11051].includes(item.msg_type)"/>
                <!-- image -->
                <a-image
                  :width="200"
                  :src="item.content" v-if="item.msg_type === 11042 && item.content"
                />
                <!-- voice -->
                <div v-if="item.msg_type === 11044 && item.file_url">
                  <a-button type="dashed" @click="playAudio(item.id)">
                    <img src="@/assets/image/tts_playing.gif" class="sound" v-if="(!currPlayAudioMsgId || currPlayAudioMsgId === item.id) && isPlaying"/>
                    <img src="@/assets/image/not_playing.png" class="sound" v-else/>
                    <audio ref="audioPlayer" :src="item.file_url" @ended="onAudioEnded"></audio>
                    <span style="margin-left: 16px;">{{item.duration}}″</span>
                    <span style="width: 40px;"></span>
                  </a-button>
                  <div style="margin-top: 4px;color: var(--color-text-1);">{{ item.content }}</div>
                </div>
                <!-- video -->
                <div v-if="item.msg_type === 11043 && item.content">
                  <div class="video-player">
                    <video ref="videoPlayer" class="video-js" controls>
                      <source :src="item.content" type="video/mp4">
                      Your browser does not support the video tag.
                    </video>
                  </div>
                </div>
                <!-- file -->
                <div v-if="item.msg_type === 11045 && item.content">
                  <div class="doc-item" @click.stop="downloadFile(item.content, getFileName(item.content))">
                    <div class="doc-icon">
                      <img src="@/assets/icon/pdf.png" v-if="item.content.indexOf('.pdf') > -1"/>
                      <img src="@/assets/icon/word.png" v-else-if="item.content.indexOf('.doc') > -1"/>
                      <img src="@/assets/icon/ppt.png" v-else-if="item.content.indexOf('.ppt') > -1"/>
                      <img src="@/assets/icon/excel.png" v-else-if="item.content.indexOf('.xls') > -1 || item.content.indexOf('.csv') > -1"/>
                      <img src="@/assets/icon/pic.png" v-else-if="item.content.indexOf('.png') > -1 || item.content.indexOf('.jpg') > -1 || item.content.indexOf('.jpeg') > -1"/>
                      <img src="@/assets/icon/file.png" v-else />
                    </div>
                    <div class="doc-content">
                      <div class="doc-name">{{getFileName(item.content)}}</div>
                      <!-- <div class="doc-size">{{calcFileSize(item?.cdn?.size)}}</div> -->
                    </div>
                  </div>
                </div>
                <!-- location -->
                <div v-if="item.msg_type === 11046">
                  <img src="@/assets/icon/location.png" class="location-icon"/>
                  <span style="margin-left: 4px;font-size: 16px;color: var(--color-text-1);">{{item.title}}</span>
                  <span style="margin-left: 5px;font-size: 14px;">{{item.address}}</span>
                </div>
                <!-- link -->
                <div v-if="item.msg_type === 11047 && item.file_url">
                  <a :href="item.file_url" target="_blank">
                    <a-card hoverable style="width: 600px">
                      <a-card-meta :title="item.title">
                        <template #description>{{item.desc}}</template>
                      </a-card-meta>
                    </a-card>
                  </a>
                </div>
                <!-- emoji -->
                <div v-if="item.msg_type === 11048 && item.file_url">
                  <a-image 
                    :width="item.width"
                    :height="item.height"
                    :src="item.file_url" 
                  />
                </div>
                <!-- hongbao -->
                <div v-if="item.msg_type === 11049">
                  <div class="doc-item">
                    <div class="doc-icon">
                      <img src="@/assets/icon/hongbao.png" class="doc-icon"/>
                    </div>
                    <div class="doc-content">
                      <div class="doc-name">{{item?.desc}}</div>
                      <div class="doc-size">
                        <span>{{item?.remark}} 金额：{{ item.money/100 }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- card -->
                <div v-if="item.msg_type === 11050">
                  <a-card style="width: 240px">
                    <a-card-meta :title="item.nickname">
                      <template #avatar>
                        <a-avatar :src="item.avatar" />
                      </template>
                    </a-card-meta>
                  </a-card>
                </div>
              </template>
              <template #avatar>
                <a-avatar :src="item.sender_avatar?item.sender_avatar:'https://pub-oss-ai.moseeker.com/image/avatar-default.png'" />
              </template>
            </a-list-item-meta>
          </a-skeleton>
        </a-list-item>
      </template>
    </a-list>
  </a-drawer>
</template>
<script lang="ts" setup>
import { computed, reactive, watch, onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import { getReleaseList } from "@/api/release";
import {weixinGroupPage, 
  saveWeixinGroup,
  weixinGroupAssistantPage,
  weixinGroupSendMessage, 
  weixinGroupChangeRelaeseId, 
  weixinGroupMessageList
} from "@/api/group_assistant";
import { message } from 'ant-design-vue';
import { useWindowSize } from '@vueuse/core'
import { searchResultHighlight } from '@/utils/helpers';
import http from '@/store/http';
import OSS from 'ali-oss';
import { OssStsTokenProps } from '@/store/thirdparty';
import { Response } from '@/types';
import { v4 as uuidv4 } from 'uuid';
import * as XLSX from 'xlsx'

const { height } = useWindowSize();
let ossStsToken = ref<OssStsTokenProps>();
let fileDict = ref({});

const keyword = ref("");
const loading = ref(false);
const saveLoading = ref(false);
const releaseAppList = ref([]);
const groupMessageList = ref([]);
const groupPage = ref([]);
const groupMessageDrawer = ref(false);
const audioPlayer = ref(null);
const isPlaying = ref(false);
const currPlayAudioMsgId = ref(0);
const clickGroupId = ref(0);
const clickGroupName = ref("");
const expectSendTime = ref(null);

const msgTypeRadio = ref('1');
const sendContent = ref('');
const imageUrl = ref("");
const groupSendVisible = ref(false)
const baseFilePath = ref<string>("file/weixin/send/image");

const props = defineProps({
  accountType: Number,
});

const accountLabel = computed(() => {
  return props.accountType === 1?"企业微信": "微信";
})

const addGroupVisible = ref(false);

const searchDate = ref<[Dayjs, Dayjs]>();

const disabledDate = (current: Dayjs) => {
  // if (!searchDate.value || (searchDate.value as any).length === 0) {
  //   return false;
  // }
  // const tooLate = current.diff(dayjs(), 'days') >= 0;
  // // console.log(current.diff(dayjs(), 'days'))
  // // console.log(current.diff(dayjs().subtract(29, 'day'), 'days'))
  // // const tooEarly = current.diff(dayjs().subtract(29, 'day'), 'days') < 0;
  // const tooLate = searchDate.value[1] && dayjs().diff(searchDate.value[1], 'days') <= 0;
  // // const tooEarly = searchDate.value[1] && searchDate.value[1].diff(current, 'days') > 1;
  return false;
};

const addListenGroupFormData = reactive({
  id: undefined,
  weixin_account_id: null,
  nickname: null,
  bot_nickname: null,
  category: "1",
});

const form = ref<FormInstance>();

const onCalendarChange = (val: RangeValue) => {
  console.log(val);
  if(val === null || val[0] === null || val[1] === null){
    return;
  }
  searchDate.value = [dayjs(val[0]), dayjs(val[1])];

  refreshWeixinGroupMessageList();
};

const category = ref(null);

const categoryOptions = ref([
  {
    label: "全部",
    value: null,
  },
  {
    label: "微信群",
    value: 1,
  },
  {
    label: "个人微信",
    value: 2,
  },
]);

const handleCategoryChange: SelectProps['onChange'] = value => {
  category.value = value;
};

const formDataInput = ref({
  'image_urls': [],
});

const columns = [
  {
    title: '对话名称',
    dataIndex: 'nickname',
    key: 'nickname',
  },
  {
    title: '对话区分',
    dataIndex: 'category',
    key: 'category',
  },  
  // {
  //   title: '群成员数量',
  //   dataIndex: 'total',
  //   key: 'total',
  //   sorter: {
  //     compare: (a, b) => a.total - b.total,
  //     multiple: 1,
  //   },
  // },
  {
    title: '关联应用项目',
    dataIndex: 'app_name',
    key: 'app_name',
  },
  {
    title: `所属${accountLabel.value}账号`,
    dataIndex: 'account',
    key: 'account',
  },
  {
    title: '群里的昵称',
    dataIndex: 'bot_nickname',
    key: 'bot_nickname',
  },
  {
    title: '已完成回答',
    key: 'answer_count',
    dataIndex: 'answer_count',
    sorter: {
      compare: (a, b) => a.answer_count - b.answer_count,
      multiple: 1,
    },
  },
  {
    title: '最后回答时间',
    key: 'last_answer_time',
    dataIndex: 'last_answer_time',
  },
  {
    title: '创建时间',
    key: 'create_time',
    dataIndex: 'create_time',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
  },
];

const stateSelectedRowKeysDict = ref({});
const stateSelectedRowKeys = ref([]);

const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: stateSelectedRowKeys,
  onChange: (selectedRowKeys: string[], selectedRows: DataType[]) => {
    console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    stateSelectedRowKeysDict.value = {
      ...stateSelectedRowKeysDict.value,
      [groupPage.value.page_number]: selectedRowKeys
    }
  },
  getCheckboxProps: (record: DataType) => ({
    disabled: record.delete_flag, // Column configuration not to be checked
    key: record.key,
  })
};

// 列表当前页更改
const handleTableChange = async (page, filters, sorter) => {
  console.log(page);
  getWeixinGroupPage(
    {
      account_type: props.accountType,
      page_size: page.pageSize, 
      page_number: page.current, 
      keyword: keyword.value,
      category: category.value,
    }
  )
};

function getReleaseAppList(){
  getReleaseList().then((response) => {
    console.log(response);
    const { data } = response;
    data?.forEach(item => {
      if(item.mode === 3){
        return;
      }
      releaseAppList.value.push({
        label: `${item.name}`,
        value: `${item.id}`,
      });
    })
  }).catch((e) => {
    console.error(e);
  });
}

const onSearch = (searchValue: string) => {
  keyword.value = searchValue;
  getWeixinGroupPage(
    {
      account_type: props.accountType,
      keyword: searchValue
    }
  )
};

const updateGroupRelease = (record, release_id: string) => {
  console.log(record, release_id);
  const weixin_group_id = record.id;
  const releaseId = parseInt(release_id);
  console.log(groupPage.value);
  if (record.release_id === releaseId){
    return
  }
  const params = {
    weixin_group_id: weixin_group_id,
    release_id: releaseId,
  }
  weixinGroupChangeRelaeseId(params).then((res) => {
    const { code, data } = res;
    console.log(res);
    if (code === 0){
      let fd = groupPage.value.results.filter(item => weixin_group_id === item.id)[0];
      console.log(fd);
      fd["release_id"] = releaseId;
      Object.assign(groupPage.value.results.filter(item => weixin_group_id === item.id)[0], fd);
      message.success("操作成功");
    }else{
      message.error("操作失败");
    }
    return data;
  });
}

function getWeixinGroupPage(params){
  const data = weixinGroupPage(params).then((res) => {
    const { data } = res;
    console.log(data);
    groupPage.value = data;
    return data;
  });
  return data;
}

function changeGroupSendNotice(visible){
  if (visible === false){
    msgTypeRadio.value = '1';
    sendContent.value = '';
    formDataInput.value.image_urls = [];
  }
  groupSendVisible.value = visible;
  // if(!exceptSendTime.value){
  //   exceptSendTime.value = dayjs();
  // }
}

function addListenGroupSubmit() {
  saveLoading.value = true;
  form.value
    ?.validate()
    .then(() => {
      saveWeixinGroup(addListenGroupFormData).then((res) => {
        const { code, data } = res;
        console.log(res);
        if (code === 0){
          changeAddListenGroup(false);
          getWeixinGroupPage({account_type: props.accountType,});
        }
      })
    })
    .finally(() => {
      saveLoading.value = false;
    });
}

function groupSendSubmit(){
  if(msgTypeRadio.value === '1' && !sendContent.value){
    message.info("请填写发送消息的内容")
    return
  }
  if(msgTypeRadio.value === '3' && !imageUrl.value){
    message.info("请上传需要发送的图片")
    return
  }
  saveLoading.value = true;
  const weixin_group_ids = stateSelectedRowKeys.value.map((item) => {
    return parseInt(item);
  });
  if (weixin_group_ids.length > 9){
    message.info("最多可以选择9个群，请调整后再发送")
    return
  }
  console.log(weixin_group_ids);
  if(!expectSendTime.value){
    expectSendTime.value = dayjs();
  }
  const params = {
    weixin_group_ids: weixin_group_ids,
    msg_type: parseInt(msgTypeRadio.value),
    content: sendContent.value,
    image_url: imageUrl.value,
    expect_send_time: expectSendTime.value.format("YYYY-MM-DD HH:mm"),
  }
  weixinGroupSendMessage(params).then((response) => {
    console.log(response);
    const {code, data} = response;
    if (code === 0){
      message.success("操作成功")
      changeGroupSendNotice(false);
    }
  }).catch((e) => {
    console.error(e);
  }).finally(() => {
    saveLoading.value = false;
  });
}

function changeAddListenGroup(visible){
  if (visible === false){
    addListenGroupFormData.id = undefined;
    addListenGroupFormData.weixin_account_id = null;
    addListenGroupFormData.nickname = null;
    addListenGroupFormData.bot_nickname = null;
    addListenGroupFormData.category = "1"
  }
  addGroupVisible.value = visible;
}

function addListenGroup(){
  changeAddListenGroup(true);
}

function groupSendNotice(){
  console.log(stateSelectedRowKeys.value)
  if(stateSelectedRowKeys.value.length === 0){
     message.info("请先选择要发送的群")
     return
  }
  if(stateSelectedRowKeys.value.length > 9){
     message.info("最多可以选择9个群，请调整后再发送")
     return
  }
  changeGroupSendNotice(true);
}

function getOssStsToken() {
  return http
    .request<OssStsTokenProps, Response<OssStsTokenProps>>('/thirdparty/oss/sts_token?public=1', 'GET')
    .then((res) => {
      console.log(res);
      const { data } = res;
      ossStsToken.value = data;
      return data;
    })
    .finally(() => (console.log("end")));
}

const beforeUpload: UploadProps['beforeUpload'] = async file => {
  const fileExtName = file.name.substring(file.name.lastIndexOf(".") + 1);
  const fileName = uuidv4() + "." + fileExtName;
  const oss_key = baseFilePath.value + '/' + fileName;

  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    message.error('单个图片最大1MB!');
    return false;
  }

  const fileData = {name: file.name, oss_key: oss_key, size: file.size, status: 0, url: ""};
  console.log(fileData);
  fileDict.value[file.uid] = fileData;
  return true;
}

const handleChange = async (info: UploadChangeParam) => {
  const status = info.file.status;
  console.log(info)
  console.log(fileDict.value)

  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }
  if (info.file.status === 'done') {
    imageUrl.value = fileDict.value[info.file.uid]["url"];
  }
  if (info.file.status === 'error') {
    loading.value = false;
    message.error('upload error');
  }

  if(status === 'removed') {
    delete fileDict.value[info.file.uid];
  }
};

async function customRequest(info) {
  console.log(info);
  const use_cname = !ossStsToken.value.endpoint.includes(".aliyuncs.com");
  const client = new OSS({
    region: ossStsToken.value.region,
    accessKeyId: ossStsToken.value.access_key_id,
    accessKeySecret: ossStsToken.value.access_key_secret,
    stsToken: ossStsToken.value.security_token,
    bucket: ossStsToken.value.bucket_name,
    endpoint: ossStsToken.value.endpoint,
    useFetch: true,
    cname: use_cname,
    secure: true,
  });

  const oss_key = fileDict.value[info.file.uid]["oss_key"];
  if(info.file){
    console.log(oss_key)
    await client.multipartUpload(oss_key, info.file, {
      parallel: 4,
      partSize: 100 * 1024,
      progress: function (percent) {
        // console.log(info.file.uid);
        console.log("progress is: ", percent * 100);
        console.log(formDataInput.value.image_urls)
        const fileIdx = formDataInput.value.image_urls.findIndex(obj => obj.uid === info.file.uid);
        console.log(fileIdx);
        formDataInput.value.image_urls[fileIdx].percent = percent * 100;
        loading.value = true;
        if (percent === 1){
          formDataInput.value.image_urls[fileIdx].status = "success";
          loading.value = false;
        }
      },
    }).then(res => {
      console.log('结果:', res);
      fileDict.value[info.file.uid]["status"] = 1;
      fileDict.value[info.file.uid]["url"] = ossStsToken.value.endpoint + '/' + oss_key;
      fileDict.value = fileDict.value;
      imageUrl.value = fileDict.value[info.file.uid]["url"];
    }).catch((err) => {
      console.log(err);
    });
  }
}

function refreshWeixinGroupMessageList(){
  let params = {weixin_group_id: clickGroupId.value}
  if (searchDate.value){
    params["start_date"] = dayjs(searchDate.value[0]).format("YYYY-MM-DD");
    params["end_date"] = dayjs(searchDate.value[1]).format("YYYY-MM-DD");
  }
  weixinGroupMessageList(params).then((response) => {
    console.log(response);
    const { data } = response;
    groupMessageList.value = data;
  }).catch((e) => {
    console.error(e);
  });  
}

function editListenGroup(record){
  addListenGroupFormData.id = record.id;
  addListenGroupFormData.weixin_account_id = `${record.weixin_account_id}`
  addListenGroupFormData.nickname = record.nickname;
  addListenGroupFormData.bot_nickname = record.bot_nickname;
  addListenGroupFormData.category = record.category.toString();

  changeAddListenGroup(true);
}

function viewGroupMessage(record){
  console.log(record);
  groupMessageDrawer.value = true;
  clickGroupName.value = record.nickname;
  clickGroupId.value = record.id;
  
  refreshWeixinGroupMessageList();
}

function colseGroupMessageDrawer(){
  groupMessageDrawer.value = false;
}

const playAudio = (msgId) => {
  if (currPlayAudioMsgId.value && msgId !== currPlayAudioMsgId.value) {
    if (audioPlayer.value) {
      audioPlayer.value.pause();
      isPlaying.value = false;
    }
  }
  currPlayAudioMsgId.value = msgId;
  isPlaying.value = !isPlaying.value;
  if (audioPlayer.value) {
    if(isPlaying.value){
      audioPlayer.value.play();
    }else{
      audioPlayer.value.pause();
    }
  }
};

const onAudioEnded = () => {
  console.log('音频播放完毕');
  // 在这里添加你想要执行的代码，例如显示一个提示或重置播放器状态
  isPlaying.value = false;
  currPlayAudioMsgId.value = 0;
};

function calcFileSize(size){
  if (!size){
    return "0 B";
  }
  return size < 1024 ? (size + ' B') : size < 1024 * 1024 ? ((size/1024).toFixed(2) + ' KB') : ((size/(1024 * 1024)).toFixed(2) + ' MB')
}

function getFileName(filePath){
  const pathParts = filePath.split('/');
  return pathParts[pathParts.length - 1];
}

function downloadFile(url, filename) {
  const link = document.createElement('a');
  link.href = url;
  link.target = "_blank";
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

const exportData = () => {
  if (groupMessageList.value.length === 0){
    message.info("暂无数据");
    return
  }
  let head = {
    id: 'ID',
    sender_name: '发送人',
    content: '发送内容',
    msg_type: '发送类型',
    create_time: '发送时间',
  }
  const results = groupMessageList.value.map(item => {
    const obj = {}
    for (const k in item) {
      if (head[k]) {
        if (k === "create_time"){
          obj[head[k]] = dayjs(item[k]).format('YYYY-MM-DD HH:mm')
        }else if (k === "msg_type"){
          const text = parseInt(item[k])
          obj[head[k]] = text === 11041?"文本":text === 11042?"图片":text === 11043?"视频":text === 11044?"音频":text === 11045?"文件":text === 11048?"表情图片":text === 11046?"位置":text === 11047?"链接":text === 11049?"红包":text === 11050?"个人名片":"其它"
        }else if (k === "content"){
          if ([11042, 11043, 11044, 11045, 11047, 11048].includes(item["msg_type"])){
            obj[head[k]] = item["content"];
          }else if(item["msg_type"] === 11046){
            obj[head[k]] = item["title"] + item["address"];
          }else if(item["msg_type"] === 11049){
            obj[head[k]] = item["desc"] + item["remark"] + "金额：" + item["money"];
          }else if(item["msg_type"] === 11050){
            obj[head[k]] = item["nickname"]
          }else{
            obj[head[k]] = item[k];
          }
        }else{
          obj[head[k]] = item[k]
        }
      }
    }
    return obj
  })
  // 创建工作表
  const dataExcel = XLSX.utils.json_to_sheet(results)
  // 创建工作簿
  const wb = XLSX.utils.book_new()
  // 将工作表放入工作簿中
  XLSX.utils.book_append_sheet(wb, dataExcel, "群消息")
  // 生成文件并下载
  XLSX.writeFile(wb, `${clickGroupName.value}-群消息-${dayjs().format('YYYY-MM-DD HH:mm')}.xlsx`)
}

function replaceUrlsWithAnchors(text) {
  return text.replace(/https?:\/\/[^\s]+/g, function(url) {
      return `<a href="${url}" target="_blank">${url}</a>`;
  });
}

const groupAssistantList = ref([]);
function getWeixinGroupAssistantPage(){
  const data = weixinGroupAssistantPage(
    {
      account_type: props.accountType, 
      page_size: 10000
    }
  ).then((res) => {
    const { data } = res;
    console.log(data);
    groupAssistantList.value = data.results.map(
      item => {
          return {
            label: `${item.account}`,
            value: `${item.id}`,
          }
        });
    return data;
  });
  return data;
}

watch(category, (val) => {
  getWeixinGroupPage({account_type: props.accountType, category: val});
});

watch(stateSelectedRowKeysDict, (val) => {
  let result = [];
  for (let key in val) {
    result.push(...val[key]); 
  }
  stateSelectedRowKeys.value = result;
});

onMounted(() => {
  getWeixinGroupAssistantPage();
  getReleaseAppList();
  getWeixinGroupPage({account_type: props.accountType});
  getOssStsToken();
})
</script>
<style>
.avatar-uploader > .ant-upload {
  width: 248px;
  height: 248px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.sound{
  width: 12px;
  height: 16px;
}

.video-player {
  max-width: 600px;
  /* margin: 0 auto; */
}

.video-js {
  width: 100%;
  height: auto;
}

.doc-item {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 300px;
  height: 60px;
  margin-right: 10px;
  border-radius: 10px;
  background-color: #1890ff;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
}
.doc-icon {
  width: 30px;
  margin-right: 10px;
  img {
    width: 30px;
    object-fit: contain;
  }
}
.location-icon{
  width: 30px;
  img {
    width: 30px;
    object-fit: contain;
  }
}

.doc-name {
  color: #fff;
  font-size: 13px;
  font-weight: 600;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
}
.doc-size {
  color: #fff;
  font-size: 12px;
  font-weight: 400;
}
</style>