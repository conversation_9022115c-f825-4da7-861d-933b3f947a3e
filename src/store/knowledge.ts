import { defineStore } from 'pinia';
import http from './http';
import { ref } from 'vue';
import { Response } from '@/types';

export interface KnowledegBaseProps {
  id?: number;
  namespace: string;
  description?: string;
  bg?: string;
  share_scope?: string;
  create_time: string;
  creator?: number;
  last_process_time?: string;
  total?: number;
}

export const useKnowledegBaseStore = defineStore('knowledeg_base', () => {
  const knowledegBaseList = ref<KnowledegBaseProps[]>([]);

  const loading = ref(false);
  const keyword = ref("");

  async function getKnowledegBaseList() {
    loading.value = true;
    const params = {keyword: keyword.value}
    return http
      .request<KnowledegBaseProps[], Response<KnowledegBaseProps[]>>('/resource/knowledge_base', 'GET', params)
      .then((res) => {
        const { data } = res;
        knowledegBaseList.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  async function addKnowledegBase(knowledegBase: KnowledegBaseProps) {
    return http
      .request<any, Response<any>>('/resource/knowledge_base', 'POST_JSON', knowledegBase)
      .then((res) => {
        return res.data;
      })
      .finally(getKnowledegBaseList);
  }

  async function updateKnowledegBase(knowledegBase: KnowledegBaseProps) {
    return http
      .request<any, Response<any>>('/resource/knowledge_base', 'PUT_JSON', knowledegBase)
      .then((res) => {
        return res.data;
      })
      .finally(getKnowledegBaseList);
  }

  async function removeKnowledegBase(id: number) {
    return http
      .request<any, Response<any>>('/resource/knowledge_base', 'DELETE', { id })
      .then(async (res) => {
        return res.data;
      })
      .finally(getKnowledegBaseList);
  }

  return {
    loading,
    keyword,
    knowledegBaseList,
    getKnowledegBaseList,
    addKnowledegBase,
    updateKnowledegBase,
    removeKnowledegBase,
  };
});
