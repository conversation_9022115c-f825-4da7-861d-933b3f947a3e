<template>
  <a-table 
    size="small"
    :columns="columns" 
    :dataSource="recordPage.results"
    :pagination="{ pageSize: recordPage.page_size, current: recordPage.page_number, total: recordPage.count }"
    @change="handleTableChange"
    :scroll="{y: (height - 290)}"
    >
    <template #title>
      <div class="text-subtext text-sm flex justify-between items-center">
        <span style="font-size:14px;">{{accountLabel}}群发消息记录</span>
        <!-- <a-input-search
          v-model:value="keyword"
          placeholder="搜索群，多个关键字用空格分隔"
          style="width: 300px;"
          allowClear
          @search="onSearch"
        /> -->
      </div>
    </template>
    <template #bodyCell="{ text, record, index, column }">
      <template v-if="column.key === 'nickname'">
        {{ record.weixin_group?.nickname }}&nbsp;<a-tag color="green" v-if="record.weixin_group?.is_external">外部</a-tag>
        <a-tag color="cyan" v-if="record.weixin_group?.category === 1">微信群</a-tag>
        <a-tag color="green" v-if="record.weixin_group?.category === 2">个人微信</a-tag>
      </template>
      <template v-else-if="column.key === 'msg_type'">
        {{ record.msg_type === 1?"文本":"图片" }}
      </template>
      <template v-else-if="column.key === 'status'">
        <a-tag color="orange" v-if="record.status === 0">待发送</a-tag>
        <a-tag color="cyan" v-if="record.status === 1">
          <template #icon>
            <sync-outlined :spin="true" />
          </template>
          发送中
        </a-tag>
        <a-tag color="blue" v-if="record.status === 2">
          已发送
        </a-tag>
        <a-tag color="error" v-if="record.status === 3">已取消</a-tag>
      </template>
      <template v-else-if="column.key === 'nickname'">
        <a-popover placement="top" trigger="hover" overlay-class-name="limit-popover-width">
          <template #content>
            <div v-html="text" />
          </template>
          {{text}}
        </a-popover>
      </template>
      <template v-else-if="column.key === 'content'">
        <div v-if="record.msg_type === 1">
          <a-popover placement="top" trigger="hover" overlay-class-name="limit-popover-width">
            <template #content>
              <div v-html="text" />
            </template>
            {{text}}
          </a-popover>
        </div>
        <a-image :width="100" :height="100" :src="record.image_url" v-if="record.msg_type === 3"/>
      </template>
      <template v-else-if="column.key === 'account'">
        {{ record.weixin_account?.account }}
      </template>
      <template v-else-if="column.dataIndex === 'expect_send_time'">
        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
      <template v-else-if="column.dataIndex === 'actual_send_time'">
        {{ record.status === 2 && text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
      <template v-else-if="column.dataIndex === 'operation'">
        <a-popconfirm placement="topLeft" title="确认要取消发送吗？" @confirm="cancelSend(record)" ok-text="确认" cancel-text="取消">
          <a v-if="[0, 1].includes(record.status)">取消发送</a>
        </a-popconfirm>
      </template>
    </template>
  </a-table>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import { useWindowSize } from '@vueuse/core'
import { 
  weixinGroupSendMessageRecordPage, 
  deleteWeixinGroupSendTask 
} from "@/api/group_assistant";

const { height } = useWindowSize();
const keyword = ref("");
const recordPage = ref([]);

const props = defineProps({
  accountType: Number,
});

const accountLabel = computed(() => {
  return props.accountType === 1?"企业微信": "微信";
})

const columns = [
  {
    title: `所属${accountLabel.value}账号`,
    dataIndex: 'account',
    key: 'account',
    width: 140,
  },
  {
    title: '对话名称',
    dataIndex: 'nickname',
    key: 'nickname',
    width: 210,
    ellipsis: true,
  },
  {
    title: '发送类型',
    dataIndex: 'msg_type',
    key: 'msg_type',
    width: 80,
  },
  {
    title: '发送内容',
    dataIndex: 'content',
    key: 'content',
    ellipsis: true,
  },
  {
    title: '发送状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '期望发送时间',
    key: 'expect_send_time',
    dataIndex: 'expect_send_time',
    width: 150,
  },
  {
    title: '实际发送时间',
    key: 'actual_send_time',
    dataIndex: 'actual_send_time',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 100,
    fixed: 'right',
  },
];

const onSearch = (searchValue: string) => {
  const params = {
    account_type: props.accountType,
    keyword: keyword.value,
  }
  // keyword.value = searchValue;
  getWeixinGroupSendMessageRecordPage(params)
};

function cancelSend(record){
  deleteWeixinGroupSendTask({id: record.id}).then((res) => {
    const { data } = res;
    console.log(data);
    getWeixinGroupSendMessageRecordPage({account_type: props.accountType})
    return data;
  });
}

function getWeixinGroupSendMessageRecordPage(params){
  const data = weixinGroupSendMessageRecordPage(params).then((res) => {
    const { data } = res;
    console.log(data);
    recordPage.value = data;
    return data;
  });
  return data;
}

// 列表当前页更改
const handleTableChange = async (page, filters, sorter) => {
  console.log(page);
  const params = {
    account_type: props.accountType, 
    page_size: page.pageSize, 
    page_number: page.current, 
    keyword: keyword.value
  };
  getWeixinGroupSendMessageRecordPage(params)
};

onMounted(() => {
  getWeixinGroupSendMessageRecordPage(
    {
      account_type: props.accountType
    }
  );
})
</script>

<style lang="less">
.limit-popover-width {
  max-width: 50%;
  word-wrap: break-word;
}
</style>