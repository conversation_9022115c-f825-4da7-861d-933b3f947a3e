<template>
  <div class="projects rounded-xl">
    <div class="mb-base">
      <div class="font-semibold text-lg">
        <div class="flex justify-between">
          <h1>知识库</h1>
        </div>
      </div>
      <div class="text-subtext text-sm flex justify-between items-center">
        <span style="font-size:14px;">知识库文档管理</span>
        <div>
          <!-- <a-button type="link" @click="setRunModalVisible(true)">体验</a-button> -->
          <!-- <a-button type="link" @click="setRunModalVisible(true)">文档工具</a-button> -->
          <a-button type="primary" @click="add"><PlusOutlined class="text-lg" />创建知识库</a-button>
        </div>
      </div>
    </div>
    <div class="text-subtext text-sm flex justify-between items-center">
      <div style="cursor: pointer; display: flex; align-items: center;">
        <a-radio-group v-model:value="kbShareTab" :style="{ marginBottom: '16px' }" size="default">
          <a-radio-button value="0">我创建的</a-radio-button>
          <a-radio-button value="1">成员共享</a-radio-button>
          <a-radio-button value="2" v-if="account.is_superuser">其他创建</a-radio-button>
        </a-radio-group>
        <a-tooltip placement="topLeft">
          <template #title>
            <span>切换为{{tableView? '卡片' : '列表'}}</span>
          </template>
          <TableOutlined 
            :style="[
              {'fontSize': '20px'},
              colorStyle,
            ]"
            @click="switchTable()"
          />
        </a-tooltip>
      </div>
      <div style="margin-right: 5px; cursor: pointer; display: flex; align-items: center; justify-content: center;">
        <a-input-search
          v-model:value="keyword"
          :placeholder="searchInputTip"
          style="width: 250px;margin-right: 5px;"
          allowClear
          @search="onSearch"
          @change="onSearchChange"
        />
      </div>
    </div>
    <div class="justify-between" v-if="kbShareTab ==='0' && knowledegBaseCardList.length === 0 && !keyword && !tableView">
      <a-result title="您还没有创建知识库">
        <template #extra>
          <a-button key="console" type="primary" @click="add">我要创建</a-button>
        </template>
      </a-result>
    </div>
    <div class="justify-between" v-if="['1', '2'].includes(kbShareTab) && !tableView && knowledegBaseCardList.length === 0">
      <a-empty :image="simpleImage" />
    </div>
    <div style="flex-wrap: wrap;" class="project-list flex items-stretch" v-if="knowledegBaseCardList.length > 0">
      <a-card v-if="!tableView" style="width: 24%;margin-bottom: 20px;position:relative;" class="project rounded-xl shadow-lg mr-last" :bordered="false" v-for="item in knowledegBaseCardList">
        <template #cover>
          <img style="cursor: pointer;" class="w-full h-48" :src="item.bg?item.bg:defaultImgBg" @click="setDocumentModalVisible(item.id, item.namespace, true, '2')" />
          <!-- <div  
            style="position: absolute;top:10px;right: 10px;width: auto;background-color: rgba(0, 0, 0, 0.3);border-radius: 5px;"
          >
            <a-button 
              v-if="account.is_superuser || item.creator === account.id"
              type="link" 
              style="color: #1890ff;padding: 0 5px 0 5px;"
              @click="edit(item)"
              ><EditOutlined class="text-lg" />
            </a-button>
            <a-button 
              v-if="account.is_superuser || item.creator === account.id"
              type="link" 
              @click="showDeleteConfirm(item)"
              style="color:rgba(255, 77, 0, 0.622);padding: 0 5px 0 0;"
              ><DeleteOutlined class="text-lg"/>
            </a-button>
          </div> -->
        </template>
        <a-card-meta> 
          <template #title> 
            <div v-html="searchResultHighlight(item.namespace , keyword)" />
            <a-tooltip placement="right">
              <template #title>
                <span v-if="item.creator === account.id && item.share_scope === 1">已设为全员可用</span>
                <span v-else-if="item.creator === account.id && item.share_scope === 2">已设为学期课程[{{item.semester}}/{{ item.course }}]成员可用</span>
                <!-- <span v-else-if="item.creator === account.id && item.share_scope === 3">已设为课程[{{item.account_column}}]成员可用</span> -->
                <span v-else-if="item.share_scope_view === 1 && item.create_account && [1, 2].includes(item.share_scope)">成员 {{item.create_account.name}} 共享</span>
                <span v-else-if="kbShareTab === '2' && item.create_account">成员 {{item.create_account.name}} 创建</span>
              </template>
              <ShareAltOutlined v-if="[1, 2, 3].includes(item.share_scope)" style="color: #1890ff;" class="text-lg"/> 
              <UserOutlined v-if="kbShareTab === '2'" style="color: #1890ff;" class="text-lg"/>
            </a-tooltip>
          </template>
          <template #description> 
            <div class="flex justify-between items-center text-sm">
              <span>已上传 <a @click="setDocumentModalVisible(item.id, item.namespace, true, '2')">{{item.total}}</a> 个文档，共 <a @click="setDocumentModalVisible(item.id, item.namespace, true, '5')">{{item.dataset_total}}</a> 组数据集</span>
              <span>{{ dayjs(item.last_process_time).format("YYYY-MM-DD HH:mm") }}</span>
            </div>
          </template>
        </a-card-meta>
        <div class="mt-4 text-subtext text-sm" style="overflow-wrap: break-word;">        
          <div v-html="searchResultHighlight(item.description, keyword)" />
        </div>
        <div class="footer mt-8 flex justify-between items-center">
          <div class="text-subtext text-sm">
            <a-button 
              :disabled="kbShareTab === '2'"
              type="dashed" 
              @click="setDocumentModalVisible(item.id, item.namespace, true, '1')">
              <template #icon><upload-outlined /></template>
              文档管理
            </a-button>
            <a-button 
              v-if="account.is_superuser || item.creator === account.id"
              type="dashed" 
              style="color: #1890ff;margin-left: 5px;"
              @click="edit(item)"
              >
              <template #icon><edit-outlined /></template>
              设置
            </a-button>
            <a-button 
              v-if="account.is_superuser || item.creator === account.id"
              type="dashed" 
              @click="showDeleteConfirm(item)"
              style="color:rgba(255, 77, 0, 0.622);margin-left: 5px;"
              >
              <template #icon><delete-outlined /></template>
              删除
            </a-button>
          </div>
          <!-- <FileIconList :size="24" /> -->
        </div>
      </a-card>
    </div>
    <div v-if="tableView">
      <a-table :columns="tableColumns" :data-source="knowledegBaseCardList" :pagination="false" 
        :showSorterTooltip="false" 
      >
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column.dataIndex === 'id'">
            {{ index + 1 }}
          </template>
          <template v-else-if="column.dataIndex === 'last_process_time'">
            {{ dayjs(text).format("YYYY-MM-DD HH:mm") }}
          </template>
          <template v-else-if="['namespace', 'description'].includes(column.dataIndex)">
            <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width" placement="topLeft">
              <template #content>
                <div v-html="searchResultHighlight(text, keyword)" />
              </template>
              <div v-html="searchResultHighlight(text, keyword)" />
            </a-popover>
          </template>
          <!-- <template v-else-if="['description'].includes(column.dataIndex)">
            <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width" placement="topLeft">
              <template #content>
                <div v-html="text" />
              </template>
              {{text}}
            </a-popover>
          </template> -->
          <template v-else-if="column.dataIndex === 'share_scope'">
            {{ viewShareScopeName(record.share_scope, record) }}
          </template>
          <template v-else-if="column.dataIndex === 'creator'">
            <!-- {{ record.create_account?record.create_account.name:"自己"}} -->
            <span v-html="searchResultHighlight(record.create_account.name, keyword)" />
            <a-tag color="error" v-if="record.create_account.delete_flag">已删除</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'total'">
            <a @click="setDocumentModalVisible(record.id, record.namespace, true, '2')">{{ text }}</a>
          </template>
          <template v-else-if="column.dataIndex === 'dataset_total'">
            <a @click="setDocumentModalVisible(record.id, record.namespace, true, '5')">{{ text }}</a>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <div v-if="account.is_superuser || record.creator === account.id">
              <a @click="setDocumentModalVisible(record.id, record.namespace, true, '1')">文档管理</a>
              <a-divider type="vertical" />
              <a @click="edit(record)">设置</a>
              <a-divider type="vertical" />
              <a-popconfirm placement="topLeft" title="确认删除？" @confirm="showDeleteConfirm(record)" ok-text="确认" cancel-text="取消">
                <a style="color: red;">删除</a>
              </a-popconfirm>
            </div>
            <div v-else><a @click="setDocumentModalVisible(record.id, record.namespace, true, '2')">查看</a></div>
          </template>
        </template>
      </a-table>
    </div>
    <DocumentManageModal 
      :documentModalVisible=documentModalVisible 
      :knowledegBaseId=knowledgeBaseId
      :knowledgeBaseNamespace=knowledgeBaseNamespace
      :tabActiveKey="tabActiveKey"
      :kbShareTab="kbShareTab"
      @setDocumentModalVisible="setDocumentModalVisible"
      v-if="knowledgeBaseId > 0"
    />
    <RunModal 
      :runModalVisible=runModalVisible 
      :knowledegBaseList=knowledegBaseList
      @setRunModalVisible="setRunModalVisible"
      v-if="knowledegBaseList.length > 0"
    />
  </div>
</template>
<script lang="ts" setup>
  import FileIconList from '@/components/avatar/FileIconList.vue';
  import DocumentManageModal from './DocumentManage.vue';
  import RunModal from './RunModal.vue';
  import { PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';

  import dayjs from 'dayjs';
  import {onMounted, computed, createVNode, ref, watch} from 'vue'
  import { Modal } from 'ant-design-vue';
  import { Empty } from 'ant-design-vue';
  import { storeToRefs } from 'pinia';
  import { useAccountStore } from '@/store';
  import {getKnowledgeBaseOtherList} from '@/api/knowledge_base'
  import { useKnowledegBaseStore } from '@/store/knowledge';
  import { searchResultHighlight } from '@/utils/helpers';

  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  const documentModalVisible = ref<boolean>(false);
  const runModalVisible = ref<boolean>(false);
  const knowledgeBaseId = ref<number>(0);
  const knowledgeBaseNamespace = ref<string>('');
  const accountStore = useAccountStore();
  const kbShareTab = ref("0");
  const knowledgeBaseOtherList = ref([]);
  
  const {account} = accountStore;
  const knowledegBaseStore = useKnowledegBaseStore();
  const { getKnowledegBaseList } = knowledegBaseStore; 
  const { keyword } = storeToRefs(knowledegBaseStore);

  const defaultImgBg = ref<String>("https://res.isapientia.com/img/bg2.jpg");
  const tabActiveKey = ref<String>("1");
  const tableView = ref<boolean>(localStorage.getItem('kbTableViewSwitch')? JSON.parse(localStorage.getItem('kbTableViewSwitch')).switch : false);
  const colorStyle = ref(tableView.value?{ 'color': '#08c' }:{'color': '#000'});

  const props = defineProps({
    knowledegBaseList: [],
  });

  const knowledegBaseCardList = computed(() => {
    const tabKey = parseInt(kbShareTab.value);
    if ([0, 1].includes(tabKey)){
      return props.knowledegBaseList.filter(item => item.share_scope_view === tabKey)
    }
    return knowledgeBaseOtherList.value;
  });
  
  const columns = ref([
    {
      title: '序号',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'namespace',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
    },
    {
      title: '共享范围',
      dataIndex: 'share_scope',
    },
    {
      title: '#文档',
      dataIndex: 'total',
      sorter: {
        compare: (a, b) => a.total - b.total,
        // multiple: 1,
      },
    },
    {
      title: '#数据集',
      dataIndex: 'dataset_total',
      sorter: {
        compare: (a, b) => a.dataset_total - b.dataset_total,
        // multiple: 2,
      },
    },
    {
      title: '创建人',
      dataIndex: 'creator',
    },
    {
      title: '最后更新时间',
      dataIndex: 'last_process_time',
    },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'operation',
      key: 'operation',
      width: 180,
      fixed: 'right',
    },
  ]);

  const tableColumns = computed(() => {
    if(kbShareTab.value === '0'){
      return columns.value.filter(item => item.dataIndex !== 'creator')
    }
    return columns.value;
  });

  const searchInputTip = computed(() => {
    if(kbShareTab.value === '0'){
      return "根据名称/描述搜索";
    }
    return "根据名称/描述/创建人搜索";
  });

  const emit = defineEmits(['add', 'edit', 'remove']);
  const add = () => {
    emit('add');
  }
  const edit = (record) => {
    emit('edit', record);
  }
  const remove = (record) => {
    emit('remove', record);
  }

  function switchTable(){
    tableView.value = !tableView.value;
    localStorage.setItem('kbTableViewSwitch', JSON.stringify({switch: tableView.value}));
    colorStyle.value = tableView.value?{ 'color': '#08c' }:{'color': '#000'};
  }

  function viewShareScopeName(share_scope, record){
    switch(share_scope){
      case 0:
        return "仅自己可用";
      case 1:
        return "全员可用";
      case 2:
        return `学期课程[${record.semester}/${record.course}]成员可用`;
      default:
        return "";
    }
  }

  function setDocumentModalVisible(itemId: number, itemName: string, visible: boolean, docTabKey: string) {
    console.log(knowledgeBaseId, visible);
    knowledgeBaseId.value = itemId;
    knowledgeBaseNamespace.value = itemName;
    documentModalVisible.value = visible; 
    tabActiveKey.value = docTabKey;
  }

  function setRunModalVisible(visible: boolean) {
    console.log(knowledgeBaseId, visible);
    runModalVisible.value = visible; 
  }

  function getKnowledgeBaseOtherListData(){
    const params = {keyword: keyword.value};
    const data = getKnowledgeBaseOtherList(params).then((res) => {
      const { data } = res;
      console.log(data);
      knowledgeBaseOtherList.value = data;
      return data;
    });
    return data;
  }

  const showDeleteConfirm = (item) => {
    Modal.confirm({
      title: `确认要删除"${item.namespace}"的知识库吗?`,
      icon: createVNode(ExclamationCircleOutlined),
      // content: createVNode('div', { style: 'color:red;' }, '知识库下对应的文档数据会一并删除，请谨慎操作'),
      content: '知识库下对应的文档数据会一并删除，请谨慎操作',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        console.log('Ok');
        remove(item);
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  const onSearchChange = (v: string) => {
    console.log('change use value', v);
    onSearch('')
  };

  const onSearch = (searchValue: string) => {
    console.log('use value', searchValue);
    console.log('or use value', keyword.value);
    if (kbShareTab.value === '2'){
      getKnowledgeBaseOtherListData()
    }else{
      getKnowledegBaseList();
    }
  };

  watch(kbShareTab, (val) => {
    if (val === '2') {
      getKnowledgeBaseOtherListData();
    }else{
      getKnowledegBaseList();
    }
  })

  onMounted(() => {
    if(account.is_superuser){
      getKnowledgeBaseOtherListData();
    }
  });
</script>
<style lang="less" scoped>
  :deep(.ant-card) {
    &-head {
      @apply border-b-0;

      &-title {
        @apply pb-0;
      }
    }
  }

  .mr-last {
    margin-right: calc(4% / 3);
    &:nth-child(4n) {
      margin-right:0;
    }
  }

  .projects {
    .project-list {
      .project {
        // &:not(:first-child) {
        //   @apply ml-lg;
        // }

        :deep(.ant-card) {
          &-cover {
            img {
              @apply rounded-t-lg;
            }
          }

          &-body {
          }

          &-meta {
            &-detail {
              @apply flex flex-col-reverse;
            }

            &-description {
              @apply font-semibold;
            }

            &-title {
              @apply text-xl mb-0 mt-2;
            }
          }
        }
      }
    }
  }

  .limit-popover-width {
    max-width: 50%;
    word-wrap: break-word;
  }
</style>
