<template>
  <div>
    <a-tabs v-model:activeKey="activeKey" style="background-color: #fff;">
      <template #leftExtra>
        <span style="margin-left: 10px;margin-right: 10px;font-size: 14px;">助手账号类型</span>
      </template>
      <!-- <a-tab-pane key="1">
        <template #tab>
          <span>
            企业微信
          </span>
        </template>
        <a-layout has-sider>
          <a-layout-sider :style="siderStyle">
            <a-menu v-model:selectedKeys="selectedKeys">
              <a-menu-item key="1">
                <wechat-outlined />
                <span class="nav-text">群助手账号</span>
              </a-menu-item>
              <a-menu-item key="2">
                <comment-outlined />
                <span class="nav-text">群管理</span>
              </a-menu-item>
              <a-menu-item key="3">
                <send-outlined />
                <span class="nav-text">群发消息记录</span>
              </a-menu-item>
            </a-menu>
          </a-layout-sider>
          <a-layout :style="{ marginLeft: '10px'}">
            <a-layout-content :style="contentStyle">
              <div v-if="selectedKeys[0] === '1'" style="margin: 10px 10px;">
                <WeworkManage />
              </div>
              <div v-if="selectedKeys[0] === '2'" style="margin: 10px 10px;">
                <WeworkGroupManage />
              </div>
              <div v-if="selectedKeys[0] === '3'" style="margin: 10px 10px;">
                <WeworkGroupSendRecord />
              </div>
            </a-layout-content>
          </a-layout>
        </a-layout>
      </a-tab-pane> -->
      <a-tab-pane key="2">
        <template #tab>
          <span>
            企业微信(RPA)
          </span>
        </template>
        <a-layout has-sider>
          <a-layout-sider :style="siderStyle">
            <a-menu v-model:selectedKeys="selectedKeys">
              <a-menu-item key="1">
                <wechat-outlined />
                <span class="nav-text">群助手账号</span>
              </a-menu-item>
              <a-menu-item key="2">
                <comment-outlined />
                <span class="nav-text">对话管理</span>
              </a-menu-item>
              <a-menu-item key="3">
                <send-outlined />
                <span class="nav-text">群发消息记录</span>
              </a-menu-item>
            </a-menu>
          </a-layout-sider>
          <a-layout :style="{ marginLeft: '10px'}">
            <a-layout-content :style="contentStyle">
              <div v-if="selectedKeys[0] === '1'" style="margin: 10px 10px;">
                <WeixinManage :accountType="1" />
              </div>
              <div v-if="selectedKeys[0] === '2'" style="margin: 10px 10px;">
                <WeixinGroupManage :accountType="1" />
              </div>
              <div v-if="selectedKeys[0] === '3'" style="margin: 10px 10px;">
                <WeixinGroupSendRecord :accountType="1" />
              </div>
            </a-layout-content>
          </a-layout>
        </a-layout>
      </a-tab-pane>
      <a-tab-pane key="3">
        <template #tab>
          <span>
            微信(RPA)
          </span>
        </template>
        <a-layout has-sider>
          <a-layout-sider :style="siderStyle">
            <a-menu v-model:selectedKeys="selectedKeys">
              <a-menu-item key="1">
                <wechat-outlined />
                <span class="nav-text">群助手账号</span>
              </a-menu-item>
              <a-menu-item key="2">
                <comment-outlined />
                <span class="nav-text">对话管理</span>
              </a-menu-item>
              <a-menu-item key="3">
                <send-outlined />
                <span class="nav-text">群发消息记录</span>
              </a-menu-item>
            </a-menu>
          </a-layout-sider>
          <a-layout :style="{ marginLeft: '10px'}">
            <a-layout-content :style="contentStyle">
              <div v-if="selectedKeys[0] === '1'" style="margin: 10px 10px;">
                <WeixinManage :accountType="2" />
              </div>
              <div v-if="selectedKeys[0] === '2'" style="margin: 10px 10px;">
                <WeixinGroupManage :accountType="2" />
              </div>
              <div v-if="selectedKeys[0] === '3'" style="margin: 10px 10px;">
                <WeixinGroupSendRecord :accountType="2" />
              </div>
            </a-layout-content>
          </a-layout>
        </a-layout>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
import type { CSSProperties } from 'vue';
import { ref } from 'vue';
import WeworkManage from './WeworkManage.vue';
import WeworkGroupManage from './WeworkGroupManage.vue';
import WeworkGroupSendRecord from './WeworkGroupSendRecord.vue';
import WeixinManage from './WeixinManage.vue';
import WeixinGroupManage from './WeixinGroupManage.vue';
import WeixinGroupSendRecord from './WeixinGroupSendRecord.vue';

const activeKey = ref('1');
const selectedKeys = ref<string[]>(['1']);

const siderStyle: CSSProperties = {
  height: "85vh",
  left: 0,  
  top: 0,
  bottom: 0,
  color: '#001129',
  backgroundColor: '#fff',
};

const contentStyle: CSSProperties = {
  backgroundColor: '#fff',
  height: "85vh",
};
</script>

<style scoped>
#components-layout-demo-fixed-sider .logo {
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px;
}
.site-layout .site-layout-background {
  background: #fff;
}

[data-theme='dark'] .site-layout .site-layout-background {
  background: #141414;
}
</style>