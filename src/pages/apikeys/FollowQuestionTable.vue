<template>
  <a-table 
    bordered 
    size="small"
    :columns="columns" 
    :dataSource="tablePageData.results" 
    :pagination="{ pageSize: tablePageData.page_size, current: tablePageData.page_number, total: tablePageData.count }"
    :scroll="{ x: 'calc(600px + 50%)', y: 620 }"
    :defaultExpandAllRows="false"
    @change="handleTableChange"
  >
    <template #bodyCell="{ text, record, index, column }">
      <template v-if="column.dataIndex === 'operation'">
        <a-button 
          v-if="record.input_source !== 1"
          type="link" 
          size="small" 
          :disabled="record.status === 1"
          :loading="record.status === 1" 
          @click="setRunTestFollowQuestion(record.id)">{{record.status === 1?"执行中": "执行"}}</a-button>
        <a-divider type="vertical" />
        <a-button 
          type="link" 
          size="small" 
          @click="setEditRecord(record)">修改</a-button>
        <a-divider type="vertical" />
        <a-popconfirm placement="topLeft" title="确认删除吗？" @confirm="removeQuestion(record)" ok-text="确认" cancel-text="取消">
          <a-button type="link" danger size="small">删除</a-button>
        </a-popconfirm>
      </template>
      <template v-if="column.dataIndex === 'id'">
        {{tablePageData.count - ((tablePageData.page_number - 1) * tablePageData.page_size ) - index }}
      </template>
      <template v-else-if="column.dataIndex === 'last_answer'">
        <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width">
          <template #content>
            <div v-html="record.answers.length > 0 ? record.answers[0].answer : '-'" />
          </template>
          {{ record.answers.length > 0 ? record.answers[0].answer : '-' }}
        </a-popover>
      </template>
      <template v-else-if="column.dataIndex === 'update_time'">
        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
    </template>
    <template #expandedRowRender="{ record }">
      <div style="margin-left: 50px;text-align: left;" v-if="record.answers.length === 0">
        暂无记录
      </div>
      <a-table :columns="innerColumns" :data-source="record.answers" :pagination="false" v-if="record.answers.length > 0">
        <template #bodyCell="{ text, inner_record, index, column }">
          <template v-if="column.dataIndex === 'id'">
            {{record.answers.length - index }}
          </template>
          <template v-else-if="['answer'].includes(column.dataIndex)">
            <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width">
              <template #content>
                <div v-html="text" />
              </template>
              {{ text }}
            </a-popover>
          </template>
          <template v-else-if="column.key === 'remark'">
            <span v-if="source === 1 && record.answers[index]?.params?.answer_from">
              {{ genRemark(record.answers[index]?.params) }}
            </span>
            <span v-else>
              -
            </span>
          </template>
          <template v-else-if="column.key === 'create_time'">
            {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
          </template>
          <template v-else-if="column.key === 'operation'">
            <a-popconfirm placement="topLeft" title="确认删除吗？" @confirm="removeAnswer(record.answers[index])" ok-text="确认" cancel-text="取消">
              <a-button type="link" danger size="small">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </template>
  </a-table>
</template>
<script lang="ts" setup>
import { onMounted, ref} from 'vue';
import { getFollowQuestionPage, deleteFollowQuestion, deleteFollowQuestionAnswer} from "@/api/release";
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';


const props = defineProps({
  source: Number,
  biz_id: Number,
  tablePageData: Object,
});

const emit = defineEmits([
  'setEditRecord', 'setRunTestFollowQuestion', 'refreshPage'
])


function genRemark(params){
  if (!params){
    return "-";
  }
  if(params.answer_from === "agent" && params.agent){
    return "来自: " + params.agent.name
  }else if(params.answer_from === "menu_qa"){
    return "来自: 菜单"
  }else if (params.answer_from === "standard_qa"){
    return "来自: 标准问答"
  }else if (params.answer_from === "workflow_chat"){
    return "来自: 工作流"
  }else{
    "-"
  }
}

const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '问题',
      dataIndex: 'question',
      key: 'question',
      ellipsis: true,
      width: 260,
    },
    {
      title: '最近一次回答',
      dataIndex: 'last_answer',
      key: 'last_answer',
      ellipsis: true,
    },
    {
      title: '最后更新时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 220,
    },
];

const innerColumns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '回答',
      dataIndex: 'answer',
      key: 'answer',
      ellipsis: true,
    },
    {
      title: '回答时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 150,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 120,
    },
];

function setEditRecord(record){
  emit('setEditRecord', record);
}

function setRunTestFollowQuestion(question_id){
  emit('setRunTestFollowQuestion', question_id);
}

function removeQuestion(record){
  deleteFollowQuestion({id: record.id}).then((res) => {
    const { code } = res;
    if (code === 0){
      message.success(`删除成功`);
      refreshPageData();
    }
  });
}

function removeAnswer(record){
  deleteFollowQuestionAnswer({id: record.id}).then((res) => {
    const { code } = res;
    if (code === 0){
      message.success(`删除成功`);
      refreshPageData();
    }
  });
}

function refreshPageData(){
  const params = {
    source: props.source,
    biz_id: props.biz_id,
  }
  refreshPage(params);
}

function refreshPage(params){
  emit('refreshPage', params);
}

const handleTableChange = (page, filters, sorter) => {
  const params = {
    source: props.source,
    biz_id: props.biz_id,
    page_size: page.pageSize,
    page_number: page.current,
  }
  refreshPage(params);
};

onMounted(() => {
  refreshPageData();
  console.log(props)
});
</script>

<style scoped>
.limit-popover-width {
  max-width: 50%;
  word-wrap: break-word;
}
</style>