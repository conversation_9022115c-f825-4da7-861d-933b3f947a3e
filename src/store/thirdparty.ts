import { defineStore } from 'pinia';
import http from './http';
import { ref } from 'vue';
import { Response } from '@/types';

export interface OssStsTokenProps {
  access_key_id: string;
  access_key_secret: string;
  security_token: string;
  request_id?: string;
  expiration?: number;
  endpoint?: string;
  bucket_name?: string;
  region?: string;
  max_upload_size?: number;
}

export const ThirdpartyStore = defineStore('thirdparty', () => {
  const ossStsToken = ref<OssStsTokenProps>();

  const loading = ref(false);

  async function getOssStsToken() {
    loading.value = true;
    return http
      .request<OssStsTokenProps, Response<OssStsTokenProps>>('/thirdparty/oss/sts_token', 'GET')
      .then((res) => {
        const { data } = res;
        ossStsToken.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  return {
    loading,
    ossStsToken,
    getOssStsToken,
  };
});
