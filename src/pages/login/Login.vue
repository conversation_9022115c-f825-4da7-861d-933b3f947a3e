<template>
  <div class="login flex items-center justify-center">
    <login-box class="shadow-lg" @success="onLoginSuccess" />
  </div>
</template>
<script lang="ts" setup>
  import LoginBox from './LoginBox.vue';
  import { useRouter } from 'vue-router';
  import { useMenuStore } from '@/store';
  const router = useRouter();
  
  function toWorkplace(){
    // router.push('/workplace')
    router.push('/chat/demo')

    // useMenuStore().getMenuList(params.is_superuser).then((res) => {
    //   window.location.assign(window.location.origin + "/#/workplace")
    //   window.location.reload()
    // });

    // window.location.assign(window.location.origin + "/#/workplace")
    // window.location.reload()
  }
  
  function onLoginSuccess(params) {
    console.log(params)
    const url = import.meta.env.VITE_API_URL;
    const isWeworkBotEnv = url.search("chat.isapientia.com") === -1;
    useMenuStore().getMenuList(params.is_superuser, isWeworkBotEnv)
    toWorkplace()
  }
</script>
<style scoped lang="less">
  .login {
    min-height: max(100vh, 720px);
    margin-top: -78px;
  }
</style>
