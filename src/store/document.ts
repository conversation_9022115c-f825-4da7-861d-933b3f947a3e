import { defineStore } from 'pinia';
import http from './http';
import { ref } from 'vue';
import { Response } from '@/types';

export interface KnowledegBaseDocumentProps {
  id?: number;
  knowledge_base_id: number;
  name?: string;
  oss_key?: string;
  size?: number;
  status?: number;
  create_time?: string;
}

export interface KnowledegBaseDocumentPageProps {
  count?: number;
  has_next?: boolean;
  has_previous?: boolean;
  page_number?: number;
  page_size?: number;
  results?: KnowledegBaseDocumentProps[];
  total_pages?: number;
}

export interface KnowledegBaseDocumentAddProps {
  id?: number;
  name: string;
  oss_key: string;
  size: number;
  status?: number;
  create_time?: string;
}

export interface KnowledegBaseDocumentBulkAddProps {
  knowledge_base_id: number;
  input_source: number;
  files?: Array<KnowledegBaseDocumentAddProps>;
  title?: String;
  description?: String;
}

export interface RenameDocument {
  knowledge_base_id: number;
  id: number;
  name: string;
}

export const KnowledegBaseDocumentStore = defineStore('knowledeg_base_document', () => {
  const documentPage = ref<KnowledegBaseDocumentPageProps>({});

  const loading = ref(false);

  async function getDocumentPage(knowledge_base_id: number, page_number?: number, page_size?: number, keyword?: string) {
    loading.value = true;
    const params = {knowledge_base_id, page_number, page_size, keyword}
    return http
      .request<KnowledegBaseDocumentProps[], Response<KnowledegBaseDocumentPageProps>>('/resource/knowledge_base/document', 'GET', params)
      .then((res) => {
        const { data } = res;
        documentPage.value = data;
        return data;
      })
      .finally(() => (loading.value = false));
  }

  async function addDocument(knowledegBase: KnowledegBaseDocumentBulkAddProps) {
    loading.value = true;
    return http
      .request<any, Response<any>>('/resource/knowledge_base/document', 'POST_JSON', knowledegBase)
      .then((res) => {
        return res;
      })
      .finally(()=>{
        getDocumentPage(knowledegBase.knowledge_base_id);
      });
  }

  async function renameDocument(doc: RenameDocument) {
    loading.value = true;
    return http
      .request<any, Response<any>>('/resource/knowledge_base/document', 'PUT_JSON', doc)
      .then((res) => {
        return res;
      })
      .finally(()=>{
        getDocumentPage(doc.knowledge_base_id);
      });
  }

  async function removeDocument(knowledge_base_id:number, id: number) {
    loading.value = true;
    return http
      .request<any, Response<any>>('/resource/knowledge_base/document', 'DELETE', { id })
      .then(async (res) => {
        return res.data;
      })
      .finally(()=>{
        getDocumentPage(knowledge_base_id);
      });
  }

  return {
    loading,
    documentPage,
    getDocumentPage,
    addDocument,
    renameDocument,
    removeDocument,
  };
});
