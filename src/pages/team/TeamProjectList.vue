<template>
  <div class="team-projects">
    <a-card :bordered="false">
      <template #title>
        <div class="flex justify-between items-center">
          <span>团队项目</span>
          <a-button type="primary" @click="showCreateTeamModal" v-if="isAdmin">创建团队项目</a-button>
        </div>
      </template>
      <a-table
        :loading="loading"
        :columns="columns"
        :data-source="teamProjects"
        row-key="id"
      >
        <template #bodyCell="{text, column, record }">
          <template v-if="column.dataIndex === 'members'">
            {{ record.members ? record.members.join('，') : '-' }}
          </template>
          <template v-if="column.dataIndex === 'create_time'">
            {{ text ? dayjs(text).format("YYYY-MM-DD HH:mm") : '-' }}
          </template>
          <template v-if="column.key === 'action'">
            <div class="action-btns">
              <a-button type="link" @click="showEditTeamModal(record)">编辑</a-button>
              <a-button type="link" @click="navigateToTeamDetail(record.id)">管理</a-button>
              <a-popconfirm
                v-if="isAdmin"
                title="确定要删除此团队项目吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteTeam(record.id)"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建团队项目模态框 -->
    <a-modal
      v-model:visible="createTeamModalVisible"
      title="创建团队项目"
      ok-text="确认" cancel-text="取消"
      @ok="createTeam"
      :confirmLoading="createLoading"
    >
      <a-form :model="teamForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item
          label="团队名称"
          name="name"
          :rules="[{ required: true, message: '请输入团队项目名称' }]"
        >
          <a-input v-model:value="teamForm.name" placeholder="请输入团队项目名称" />
        </a-form-item>
        <a-form-item label="团队描述" name="description">
          <a-textarea
            v-model:value="teamForm.description"
            placeholder="请输入团队项目描述"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="团队成员" name="account_ids">
          <a-select
            v-model:value="teamForm.account_ids"
            mode="multiple"
            placeholder="请选择团队成员"
            :options="userOptions"
            :loading="usersLoading"
            :filter-option="filterOption"
            show-search
          ></a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑团队项目模态框 -->
    <a-modal
      v-model:visible="editTeamModalVisible"
      title="编辑团队项目"
      ok-text="确认" cancel-text="取消"
      @ok="updateTeam"
      :confirmLoading="editLoading"
    >
      <a-form :model="teamForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item
          label="团队名称"
          name="name"
          :rules="[{ required: true, message: '请输入团队项目名称' }]"
        >
          <a-input v-model:value="teamForm.name" placeholder="请输入团队项目名称" />
        </a-form-item>
        <a-form-item label="团队描述" name="description">
          <a-textarea
            v-model:value="teamForm.description"
            placeholder="请输入团队项目描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { 
  getTeamProjects, 
  createTeamProject, 
  deleteTeamProject 
} from '@/api/team';
import { getAccountPage } from '@/api/account';
import { useAccountStore } from '@/store';

const router = useRouter();
const loading = ref(false);
const createLoading = ref(false);
const editLoading = ref(false);
const usersLoading = ref(false);
const createTeamModalVisible = ref(false);
const editTeamModalVisible = ref(false);
const accountStore = useAccountStore();
const isAdmin = computed(() => accountStore.account.is_superuser === true);

// 表格列定义
const columns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    key: 'creator_name',
  },
  {
    title: '团队成员',
    dataIndex: 'members',
    key: 'members',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
  },
  {
    title: '操作',
    key: 'action',
  },
];

// 团队项目列表
const teamProjects = ref<any[]>([]);

// 用户列表
const userOptions = ref<{ label: string; value: string }[]>([]);

// 创建团队表单
const teamForm = reactive<{
  id?: string;
  name: string;
  description?: string;
  account_ids?: string[];
}>({
  name: '',
  description: '',
  account_ids: [],
});

// 获取团队项目列表
const fetchTeamProjects = async () => {
  loading.value = true;
  try {
    const params = {};
    const res = await getTeamProjects(params);
    if (res.code == 200) {
      teamProjects.value = res.data.results || [];
    } else {
      message.error(res.message || '获取团队项目列表失败');
    }
  } catch (error) {
    console.error('获取团队项目列表出错:', error);
    message.error('获取团队项目列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取用户列表
const fetchUsers = async () => {
  usersLoading.value = true;
  try {
    const res = await getAccountPage({ page_size: 99999 });
    if (res.code == 200) {
      const users = res.data.results || [];
      userOptions.value = users.map((user: any) => ({
        label: `${user.nickname || user.username} (${user.email})`,
        value: user.id,
      }));
      console.log('userOptions: ', userOptions)
    }
  } catch (error) {
    console.error('获取用户列表出错:', error);
  } finally {
    usersLoading.value = false;
  }
};

// 用于模糊搜索的过滤函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 显示创建团队模态框
const showCreateTeamModal = () => {
  // 重置表单
  teamForm.id = undefined;
  teamForm.name = '';
  teamForm.description = '';
  teamForm.account_ids = [];
  createTeamModalVisible.value = true;
  // 获取可选的用户列表
  fetchUsers();
};

// 显示编辑团队模态框
const showEditTeamModal = (record: any) => {
  // 填充表单数据
  teamForm.id = record.id;
  teamForm.name = record.name;
  teamForm.description = record.description || '';
  // 不需要设置团队成员，因为编辑时不显示
  editTeamModalVisible.value = true;
};

// 创建团队项目
const createTeam = async () => {
  if (!teamForm.name) {
    message.warning('请输入团队项目名称');
    return;
  }

  createLoading.value = true;
  try {
    const res = await createTeamProject(teamForm);
    if (res.code === 0) {
      message.success('创建团队项目成功');
      createTeamModalVisible.value = false;
      fetchTeamProjects();
    } else {
      message.error(res.message || '创建团队项目失败');
    }
  } catch (error) {
    console.error('创建团队项目出错:', error);
    message.error('创建团队项目失败');
  } finally {
    createLoading.value = false;
  }
};

// 更新团队项目
const updateTeam = async () => {
  if (!teamForm.name) {
    message.warning('请输入团队项目名称');
    return;
  }

  editLoading.value = true;
  try {
    const res = await createTeamProject(teamForm);
    if (res.code === 0) {
      message.success('更新团队项目成功');
      editTeamModalVisible.value = false;
      fetchTeamProjects();
    } else {
      message.error(res.message || '更新团队项目失败');
    }
  } catch (error) {
    console.error('更新团队项目出错:', error);
    message.error('更新团队项目失败');
  } finally {
    editLoading.value = false;
  }
};

// 删除团队项目
const deleteTeam = async (id: string) => {
  try {
    const res = await deleteTeamProject({ id });
    if (res.code === 0) {
      message.success('删除团队项目成功');
      fetchTeamProjects();
    } else {
      message.error(res.message || '删除团队项目失败');
    }
  } catch (error) {
    console.error('删除团队项目出错:', error);
    message.error('删除团队项目失败');
  }
};

// 导航到团队详情页
const navigateToTeamDetail = (id: string) => {
  router.push(`/team/detail/${id}`);
};

onMounted(() => {
  fetchTeamProjects();
});
</script>

<style scoped>
.team-projects {
  padding: 16px;
}

.action-btns {
  display: flex;
  gap: 8px;
}

</style> 