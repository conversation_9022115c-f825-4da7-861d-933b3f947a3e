<template>
  <a-drawer 
    :title="`分发应用 - ${record.name}`"
    :closable="true" 
    :width="1200"
    :visible="appAssignDrawer" 
    @close="setVisible(false)"
    :destroyOnClose="true"
  > 
    <a-table 
      :row-selection="rowSelection"
      :columns="columns" 
      :dataSource="accountPage.results.filter(item => item.is_superuser === false)" 
      :pagination="false"
      :scroll="{ x: 1200}"
      @change="handleTableChange"
    >
      <template #title>
        <div class="text-subtext text-sm flex justify-between items-center">
          <div>
            <a-input-search
              v-model:value="keyword"
              placeholder="多个关键字用空格分隔"
              style="width: 250px;"
              allowClear
              @search="onSearch"
              @change="onSearchChange"
            />
          </div>
          <div>
            <span style="font-size:14px;margin-right: 5px;">已选择: <span style="color:#ff9640;">{{ stateSelectedRowKeys.length }}</span>条记录</span>
            <a-button 
              type="primary" 
              @click="confirmAssignApp"
              :loading="loading"
              :disabled="stateSelectedRowKeys.length === 0">确认分发</a-button>
          </div>
        </div>
      </template>
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="['username', 'nickname'].includes(column.dataIndex)">
          <div v-html="text?searchResultHighlight(text):''" />
        </template>
        <template v-else-if="['student_no'].includes(column.dataIndex)">
          <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width">
            <template #content>
              <div v-html="fixStudentNo(record)?searchResultHighlight(fixStudentNo(record)):''" />
            </template>
            <div v-html="fixStudentNo(record)?(fixStudentNo(record).length > 6?searchResultHighlight(fixStudentNo(record).slice(0, 6)) + '...':searchResultHighlight(fixStudentNo(record))):''" v-if="keyword" />
            {{ keyword?"":fixStudentNo(record) }}
          </a-popover>
        </template>
        <template v-else-if="['email', 'department', 'class_name', 'mobile', 'student_type', 
        'nation',  'remark', 'semester'].includes(column.dataIndex)">
          <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width">
            <template #content>
              <div v-html="text?searchResultHighlight(text):''" />
            </template>
            <div v-html="text?searchResultHighlight(text):''" v-if="keyword"/>
            {{ keyword?"":text }}
          </a-popover>
        </template>
        <template v-else-if="column.dataIndex === 'courses'">
          <div style="display: flex;
              flex-direction: column; /* 设置为垂直排列 */
              align-items: flex-start; /* 左对齐 */
              gap: 8px; /* 设置标签之间的间距 */">
            <a-tag :color="getCourseColor(course)" v-for="course in record.courses">
              {{ course }}
            </a-tag>
            <!-- <a-tag color="#1377FF">#f50</a-tag> -->
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'usage_limit'">
          <span>{{ text && text > 0?(text-record.actual_usage) + '条': '不限制' }}</span>
          <br/>
          <span v-if="record.actual_usage > 0" style="color:#ff9640;font-size: small;">已使用: {{ record.actual_usage }} 条</span>
          <span v-else style="color:#ff9640;font-size: small;">暂未使用</span>
        </template>
        <template v-else-if="column.dataIndex === 'last_login'">
          {{ text?dayjs(text).format('YYYY-MM-DD'):'-' }}
        </template>
        <template v-else-if="column.dataIndex === 'expire_date'">
          {{ text?dayjs(text).format('YYYY-MM-DD'):'不限制' }}
        </template>
        <template v-else-if="column.dataIndex === 'create_time'">
          {{ text?dayjs(text).format('YYYY-MM-DD'):'-' }}
        </template>
        <template v-else-if="column.dataIndex === 'is_active'">
          <span v-if="record.is_superuser">主账号</span>
          <span v-else-if="record.delete_flag" style="color:red;">已删除</span>
          <span v-else-if="record.expire_flag" style="color:red;">已过期</span>
          <span v-else-if="text" style="color: green;">已启用</span>
          <span v-else>未启用</span>
        </template>
      </template>
    </a-table>
    <div style="margin-top: 10px;float: right;">
      <a-pagination
        size="small" 
        v-model:current="accountPage.page_number" 
        v-model:pageSize="accountPage.page_size"
        :page-size-options="pageSizeOptions"
        :total="accountPage.count" 
        show-less-items 
        show-quick-jumper
        :show-size-changer="true"
        @change="onShowSizeChange"
      >
        <template #buildOptionText="props">
          <span v-if="props.value !== '*********'">{{ props.value }} / page</span>
          <span v-else> 全部 </span>
        </template>
      </a-pagination>
    </div>
  </a-drawer>
</template>
<script lang="ts" setup>
import { reactive, watch, onMounted, ref } from 'vue';
import { message, notification } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { TableProps } from 'ant-design-vue';
import {
  getAccountPage,
} from "@/api/account";
import { 
  assignReleaseApp,
} from "@/api/release";

const pageSizeOptions = ref<string[]>(['*********', '10', '50', '100', '200']);
const keyword = ref('');
const loading = ref(false);

const props = defineProps({
  appAssignDrawer: Boolean,
  record: Object,
});

const emit = defineEmits(['setAppAssignDrawer'])

const setVisible = (visible: boolean) => {
  console.log(visible);
  emit('setAppAssignDrawer', visible);
};

const handleTableChange = async (page, filters, sorter) => {
  console.log(page);
  refreshPage({page_size: page.pageSize, page_number: page.current, keyword: keyword.value})
};

function fixStudentNo(record){
  if (record.student_no && record.student_card_no){
    return record.student_no + "/" + record.student_card_no
  }else if (record.student_no){
    return record.student_no
  }else if (record.student_card_no){
    return record.student_card_no
  }
  return "";
}

const searchResultHighlight = (content) => {
  if(!keyword.value){
    return content;
  }
  const keywords = keyword.value.split(" ")
  keywords.forEach((item) => {
    if(item){
      const reg = new RegExp(`(${item})`, "ig");
      content = content.replace(
        reg,
        `<span style="color:#1377FF">$1</span>`
      );
    }
  });
  return content;
}

function getCourseColor(course){
  // 如果 keyword.value 为空，直接返回蓝色
  if (!keyword.value) {
      return "blue";
  }
  // 将 keyword.value 按空格分割成关键词数组
  const keywords = keyword.value.split(" ");
  // 使用 some 方法检查是否有关键词存在于 course 中
  const existKeyword = keywords.some((item) => item && course.includes(item));
  // 如果存在关键词，返回黄色，否则返回蓝色
  return existKeyword ? "#1377FF" : "blue";
}


const stateSelectedRowKeysDict = ref({});
const stateSelectedRowKeys = ref([]);

const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: stateSelectedRowKeys,
  onChange: (selectedRowKeys: string[], selectedRows: DataType[]) => {
    console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    stateSelectedRowKeysDict.value = {
      ...stateSelectedRowKeysDict.value,
      [accountPage.value.page_number]: selectedRowKeys
    }
  },
  getCheckboxProps: (record: DataType) => ({
    disabled: record.is_superuser, // Column configuration not to be checked
    key: record.key,
  })
};

const onSearchChange = (v: string) => {
  console.log('change use value', v);
  onSearch('')
};

const onSearch = (searchValue: string) => {
  console.log('use value', searchValue);
  console.log('or use value', keyword.value);
  refreshPage({keyword: keyword.value})
};

function confirmAssignApp(){
  if(stateSelectedRowKeys.value.length === 0){
     message.error("请选择要分发的账号记录")
     return
  } 
  if(!(props.record?.id > 0)){
     message.error("无效的应用")
     return
  } 
  const account_ids = stateSelectedRowKeys.value.map((item) => {
    return parseInt(item);
  });
  loading.value = true;
  const accounts = accountPage.value.results.filter(
    item => account_ids.includes(item.id)
  ).map(item=>({
    id: item.id,
    username: item.username
  }))
  const params = {
    accounts: accounts, 
    release_id: props.record?.id,
    name: props.record?.name,
  }
  assignReleaseApp(params).then((res) => {
    const { code, data } = res;
    if (code === 0){
      const failed = data.filter(item => item.reason.includes("分发失败"));
      const total = data.length;
      const failed_total = failed.length;
      let content = `分发成功 ${total-failed_total} 条`;
      if (failed_total > 0){
        content = content + `，分发失败 ${failed_total} 条\n\n`
        content = content + failed.map(item => item.reason).join("\n")
        notification["warning"]({
          message: '分发结果',
          description: content,
          style: {
            whiteSpace: 'pre-wrap',
            width: '100%'
          },
        });
      }else{
        notification["success"]({
          message: '分发结果',
          description: content,
          style: {
            whiteSpace: 'pre-wrap',
            width: '100%'
          },
        });
      }
      keyword.value = "";
      stateSelectedRowKeysDict.value = [];
      refreshPage({});
    }
  }).finally(() => {
    loading.value = false;
  }); 
}

const columns = [
  {
    title: '账号',
    dataIndex: 'username',
    key: 'username',
    width: 150,
    fixed: 'left',
  },
  {
    title: '姓名',
    dataIndex: 'nickname',
    key: 'nickname',
    ellipsis: true,
  },
  // {
  //   title: '性别',
  //   dataIndex: 'gender',
  //   key: 'gender',
  //   width: 60,
  //   ellipsis: true,
  // },
  {
    title: '院系',
    dataIndex: 'department',
    key: 'department',
    ellipsis: true,
  },
  {
    title: '班级',
    dataIndex: 'class_name',
    key: 'class_name',
    ellipsis: true,
  },
  {
    title: '学期',
    dataIndex: 'semester',
    key: 'semester',
    ellipsis: true,
  },
  {
    title: '课程',
    dataIndex: 'courses',
    key: 'courses',
    ellipsis: true,
  },
  // {
  //   title: '学生类型',
  //   dataIndex: 'student_type',
  //   key: 'student_type',
  //   width: 100,
  //   ellipsis: true,
  // },
  // {
  //   title: '联系电话', 
  //   dataIndex: 'mobile',
  //   key: 'mobile',
  //   ellipsis: true,
  // },
  // {
  //   title: '电子邮箱',
  //   dataIndex: 'email',
  //   key: 'email',
  //   ellipsis: true,
  // },
  // {
  //   title: '国籍',
  //   dataIndex: 'nation',
  //   key: 'nation',
  //   ellipsis: true,
  // },
  // {
  //   title: '单位',
  //   dataIndex: 'partner',
  //   key: 'partner',
  //   ellipsis: true,
  // },
  // {
  //   title: '账号状态',
  //   width: 100,
  //   dataIndex: 'is_active',
  //   key: 'is_active',
  //   fixed: 'right',
  // },
  // {
  //   title: '可用量',
  //   width: 140,
  //   dataIndex: 'usage_limit',
  //   key: 'usage_limit',
  //   fixed: 'right',
  // },
  // {
  //   title: '有效期限',
  //   width: 100,
  //   dataIndex: 'expire_date',
  //   key: 'expire_date',
  //   fixed: 'right',
  // },
  // {
  //   title: '模型权限',
  //   width: 120,
  //   dataIndex: 'model_group_id',
  //   key: 'model_group_id',
  //   fixed: 'right',
  // },
  {
    title: '创建时间',
    align: 'center',
    width: 150,
    dataIndex: 'create_time',
    key: 'create_time',
    fixed: 'right',
  },
  // {
  //   title: '最后登录时间',
  //   align: 'center',
  //   width: 150,
  //   dataIndex: 'last_login',
  //   key: 'last_login',
  //   fixed: 'right',
  // },
  // {
  //   title: '备注',
  //   align: 'center',
  //   dataIndex: 'remark',
  //   key: 'remark',
  //   fixed: 'right',
  //   ellipsis: true,
  // },
];

const accountPage = ref({});

const defaultPageSize = ref(
  localStorage.getItem('PopAccountPageSize')? JSON.parse(localStorage.getItem('PopAccountPageSize')).pageSize : *********
)

function refreshPage(params){
  const {page_size} = params;
  if (page_size === undefined){
    params.page_size = defaultPageSize.value;
  }
  const data = getAccountPage(params).then((res) => {
    const { data } = res;
    console.log(data);
    accountPage.value = data;
    return data;
  });
  return data;
}

const onShowSizeChange = (current: number, pageSize: number) => {
  console.log(current, pageSize);
  localStorage.setItem('PopAccountPageSize', JSON.stringify({pageSize: pageSize}));
  refreshPage({page_size: pageSize, page_number: current, keyword: keyword.value})
};

watch(stateSelectedRowKeysDict, (val) => {
  let result = [];
  for (let key in val) {
    result.push(...val[key]); 
  }
  stateSelectedRowKeys.value = result;
});

onMounted(() => {
  refreshPage({})
})
</script>
<style>
.custom-class {
  color: red;
}
</style>