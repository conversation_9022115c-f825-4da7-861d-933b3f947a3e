<template>
  <div class="avatar-list">
    <a-avatar-group>
    <a-avatar style="background-color: #f56a00">
      <template #icon><file-image-outlined /></template>
    </a-avatar>
    <a-avatar style="background-color: #87d068">
      <template #icon><file-markdown-outlined /></template>
    </a-avatar>
    <a-avatar style="background-color: #7265e6">
      <template #icon><file-ppt-outlined /></template>
    </a-avatar>
    <a-avatar style="background-color: #ffbf00">
      <template #icon><file-word-outlined /></template>
    </a-avatar>
    <a-avatar style="background-color: #1890ff">
      <template #icon><file-pdf-outlined /></template>
    </a-avatar>
  </a-avatar-group>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

export type FileType = {
  type: string;
};

defineProps({
  source: Array as PropType<FileType[]>,
  size: {
    type: Number,
    default: 22,
    required: false,
  },
})
</script>

<style lang="less" scoped>
.avatar-list {
  :deep(.ant-avatar) {
    @apply outline-1 outline-white outline;
  }
}
</style>
