<template>
  <a-modal 
    :visible="visible" 
    title="选择背景图" 
    @ok="handleClose"
    @cancel="handleClose"
    @close="handleClose"
    width="900px"
    :footer="null"
  >
    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
      <a-card 
        v-for="(bg, index) in backgroundPresets" 
        :key="index"
        class="bg-card"
        :class="{ 'bg-card-selected': backgroundData.background_preset === bg.name }"
        hoverable
        :bordered="backgroundData.background_preset === bg.name"
        @click="selectBackground(bg)"
        :style="{
          borderColor: backgroundData.background_preset === bg.name ? '#3b82f6' : '#ebedf0',
          borderWidth: backgroundData.background_preset === bg.name ? '2px' : '1px',
          transition: 'all 0.3s',
        }"
      >
        <div style="height: 100px; overflow: hidden; position: relative;">
          <div style="position: absolute; inset: 0;"
            :style="{
              background: bg.css || '#ffffff',
              backgroundSize: bg.css.includes('radial-gradient') ? '20px 20px' : 
                            bg.css.includes('linear-gradient(rgba') ? '40px 40px' : 
                            'cover',
              backgroundPosition: bg.css.includes('radial-gradient') ? '0 0, 10px 10px' : 
                                bg.css.includes('linear-gradient(rgba') ? '0 0, 20px 20px, 0 0, 20px 20px' : 
                                'center'
            }"
          ></div>
        </div>
        <div style="padding: 10px 0; text-align: center; font-weight: bold; background-color: rgba(255, 255, 255, 0.8);">
          {{ bg.name }}
        </div>
      </a-card>
      
      <a-card 
        class="bg-card upload-bg-card"
        :class="{ 'bg-card-selected': backgroundData.background_type === 'custom' }"
        hoverable
        :bordered="backgroundData.background_type === 'custom'"
        @click="showUploadBackground = true"
        :style="{
          borderColor: backgroundData.background_type === 'custom' ? '#3b82f6' : '#ebedf0',
          borderWidth: backgroundData.background_type === 'custom' ? '2px' : '1px',
          transition: 'all 0.3s',
        }"
      >
        <div style="height: 100px; display: flex; flex-direction: column; justify-content: center; align-items: center; background-color: #f9fafb;" v-if="!backgroundData.background_image_url">
          <upload-outlined style="font-size: 24px; margin-bottom: 8px;" />
          <div>上传背景图</div>
        </div>
        <div style="height: 100px; display: flex; flex-direction: column; justify-content: center; align-items: center; background-color: #f9fafb;" v-else>
          <img :src="backgroundData.background_image_url" style="width: 100%; height: 100%; object-fit: cover;" />
        </div>
        <div style="padding: 10px 0; text-align: center; font-weight: bold; background-color: rgba(255, 255, 255, 0.8);">
          自定义背景
        </div>
      </a-card>
    </div>
    <div style="margin-top: 20px; text-align: right;">
      <a-button type="primary" @click="confirm">确定</a-button>
    </div>
  </a-modal>

  <!-- Custom Background Upload Modal -->
  <a-modal
    v-model:visible="showUploadBackground"
    title="上传背景图"
    okText="确定"
    cancelText="取消"
    @ok="confirmCustomBackground"
    @cancel="handleCloseUpload"
    @close="handleCloseUpload"
  >
    <div style="margin-bottom: 16px;">
      <p>请上传一张背景图片（支持PNG、JPG、JPEG、WEBP、GIF格式，大小不超过2MB）</p>
    </div>
    <a-upload
      list-type="picture-card"
      :file-list="customBackgroundList"
      @preview="handleBackgroundPreview"
      @change="handleBackgroundChange"
      :customRequest="customBackgroundRequest"
      :before-upload="beforeUploadBackground"
      accept=".png,.jpg,.jpeg,.webp,.gif"
      :maxCount="1"
    >
      <div v-if="customBackgroundList.length < 1">
        <loading-outlined v-if="loading" />
        <plus-outlined v-else />
        <div style="margin-top: 8px">上传</div>
      </div>
    </a-upload>
    <div v-if="customBackgroundList.length > 0 && customBackgroundList[0].status === 'uploading'" style="margin-top: 8px; color: #1890ff;">
      <loading-outlined /> 正在上传中...
    </div>
    <div v-else-if="customBackgroundList.length > 0 && customBackgroundList[0].status === 'done'" style="margin-top: 8px; color: #52c41a;">
      <check-circle-outlined /> 上传成功
    </div>
    <div v-else-if="customBackgroundList.length > 0 && customBackgroundList[0].status === 'error'" style="margin-top: 8px; color: #f5222d;">
      <close-circle-outlined /> 上传失败
    </div>
    
    <a-modal
      :visible="previewVisible"
      :title="previewTitle"
      :footer="null"
      @cancel="previewVisible = false"
    >
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { 
  UploadOutlined, 
  LoadingOutlined, 
  PlusOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined 
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'BackgroundModal',
  
  components: {
    UploadOutlined,
    LoadingOutlined,
    PlusOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined
  },
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    backgroundData: {
      type: Object,
      default: () => ({
        background_type: 'preset',
        background_preset: '无背景',
        background_css: '',
        background_image_url: ''
      })
    },
    ossRequest: {
      type: Object,
      required: true
    }
  },
  
  emits: ['update:visible', 'update:backgroundData', 'confirm'],
  
  setup(props, { emit }) {
    // Background presets
    const backgroundPresets = [
      { name: "无背景", css: "" },
      { name: "渐变蓝", css: "linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%)" },
      { name: "渐变绿", css: "linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%)" },
      { name: "渐变橙", css: "linear-gradient(135deg, rgba(249, 115, 22, 0.1) 0%, rgba(234, 88, 12, 0.1) 100%)" },
      { name: "渐变紫", css: "linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%)" },
      { name: "对角线", css: "repeating-linear-gradient(45deg, rgba(59, 130, 246, 0.03) 0px, rgba(59, 130, 246, 0.03) 2px, transparent 2px, transparent 4px)" },
      { name: "网格线", css: "linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px), linear-gradient(rgba(59, 130, 246, 0.025) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.025) 1px, transparent 1px)" },
      { name: "点阵", css: "radial-gradient(rgba(59, 130, 246, 0.1) 3px, transparent 3px), radial-gradient(rgba(59, 130, 246, 0.1) 3px, transparent 3px)" },
      { name: "波浪线", css: "linear-gradient(135deg, rgba(59, 130, 246, 0.03) 25%, transparent 25%, transparent 50%, rgba(59, 130, 246, 0.03) 50%, rgba(59, 130, 246, 0.03) 75%, transparent 75%, transparent)" },
    ];
    
    // State
    const selectedBackground = ref(null);
    const showUploadBackground = ref(false);
    const customBackgroundList = ref([]);
    const previewVisible = ref(false);
    const previewImage = ref('');
    const previewTitle = ref('');
    const loading = ref(false);
    const fileDict = ref({});
    const baseFilePath = ref("file/release/background");
    
    // When the modal opens, find the currently selected background
    watch(() => props.visible, (isVisible) => {
      if (isVisible) {
        if (props.backgroundData.background_type === 'preset' && props.backgroundData.background_preset) {
          // Find preset
          selectedBackground.value = backgroundPresets.find(
            bg => bg.name === props.backgroundData.background_preset
          ) || backgroundPresets[0];
        } else if (props.backgroundData.background_type === 'custom' && props.backgroundData.background_image_url) {
          // Setup custom background
          customBackgroundList.value = [{
            uid: '-1',
            name: '背景图片',
            status: 'done',
            url: props.backgroundData.background_image_url,
          }];
        }
      }
    });
    
    // Select a background
    const selectBackground = (bg) => {
      selectedBackground.value = bg;
      
      // Update the background data
      emit('update:backgroundData', {
        background_type: 'preset',
        background_preset: bg.name,
        background_css: bg.css,
        background_image_url: ''
      });
    };
    
    // Upload background handling
    const handleBackgroundPreview = (file) => {
      previewImage.value = file.url || file.thumbUrl;
      previewVisible.value = true;
      previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
    };
    
    const beforeUploadBackground = (file) => {
      const fileExtName = file.name.substring(file.name.lastIndexOf(".") + 1);
      const fileName = "bg_" + new Date().getTime() + "." + fileExtName;
      const oss_key = baseFilePath.value + '/' + fileName;

      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('背景图片大小不能超过2MB!');
        return false;
      }
      
      // Check for supported image format
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('请上传图片格式文件!');
        return false;
      }

      // Initialize file data in dictionary
      const fileData = {
        name: file.name, 
        oss_key: oss_key, 
        size: file.size, 
        status: 0, 
        url: ""
      };
      
      fileDict.value[file.uid] = fileData;
      return true;
    };
    
    const handleBackgroundChange = ({ fileList, file }) => {
      customBackgroundList.value = fileList;
      
      // When file is removed
      if (file.status === 'removed' && file.uid && fileDict.value[file.uid]) {
        delete fileDict.value[file.uid];
        if (customBackgroundList.value.length === 0) {
          // Reset background settings when all files are removed
          emit('update:backgroundData', {
            background_type: 'preset',
            background_preset: '无背景',
            background_css: '',
            background_image_url: ''
          });
        }
      }
      
      // When a file is uploaded successfully
      if (file.status === 'done' && file.url) {
        emit('update:backgroundData', {
          background_type: 'custom',
          background_preset: '自定义背景',
          background_css: '',
          background_image_url: file.url
        });
      }
    };
    
    const customBackgroundRequest = async (info) => {
      try {
        // Update file status to uploading
        const fileList = [...customBackgroundList.value];
        if (fileList.length > 0) {
          fileList[0].status = 'uploading';
          fileList[0].percent = 0;
          customBackgroundList.value = fileList;
        }
        
        loading.value = true;
        
        // Get OSS token and upload file
        const result = await props.ossRequest.uploadFile({
          file: info.file,
          oss_key: fileDict.value[info.file.uid]?.oss_key,
          onProgress: (percent) => {
            // Update progress in the UI
            const progressFileList = [...customBackgroundList.value];
            if (progressFileList.length > 0) {
              progressFileList[0].percent = Math.floor(percent * 100);
              customBackgroundList.value = progressFileList;
            }
          }
        });
        
        if (result && result.url) {
          // Update file status and URL
          fileDict.value[info.file.uid].status = 1;
          fileDict.value[info.file.uid].url = result.url;
          
          // Create new file list with URL
          const successFileList = [...customBackgroundList.value];
          if (successFileList.length > 0) {
            successFileList[0].status = 'done';
            successFileList[0].url = result.url;
            successFileList[0].percent = 100;
            customBackgroundList.value = successFileList;
          }
          
          // Update the background data
          emit('update:backgroundData', {
            background_type: 'custom',
            background_preset: '自定义背景',
            background_css: '',
            background_image_url: result.url
          });
          
          message.success('背景图片上传成功');
          
          // Call the onSuccess callback
          if (info.onSuccess) {
            info.onSuccess(result, info.file);
          }
        } else {
          throw new Error('上传失败');
        }
      } catch (error) {
        console.error('上传失败:', error);
        message.error('背景图片上传失败: ' + (error.message || '未知错误'));
        
        // Update file status to error
        const errorFileList = [...customBackgroundList.value];
        if (errorFileList.length > 0) {
          errorFileList[0].status = 'error';
          customBackgroundList.value = errorFileList;
        }
        
        // Call the onError callback
        if (info.onError) {
          info.onError(error, info.file);
        }
      } finally {
        loading.value = false;
      }
    };
    
    const confirmCustomBackground = () => {
      if (customBackgroundList.value.length > 0) {
        const file = customBackgroundList.value[0];
        
        // Check if the file is uploaded
        if (file.status === 'done' || file.response) {          
          message.success('已选择自定义背景图');
          showUploadBackground.value = false;
        } else if (loading.value) {
          message.warning('正在上传中，请稍候...');
          return;
        } else {
          message.warning('请上传背景图片');
          return;
        }
      } else {
        message.warning('请上传背景图片');
        return;
      }
    };
    
    // Confirm overall selection
    const confirm = () => {
      emit('confirm');
      emit('update:visible', false);
    };
    
    const handleClose = () => {
      emit('update:visible', false);
    };
    
    const handleCloseUpload = () => {
      showUploadBackground.value = false;
    };
    
    return {
      backgroundPresets,
      selectedBackground,
      showUploadBackground,
      customBackgroundList,
      previewVisible,
      previewImage,
      previewTitle,
      loading,
      selectBackground,
      handleBackgroundPreview,
      handleBackgroundChange,
      beforeUploadBackground,
      customBackgroundRequest,
      confirmCustomBackground,
      confirm,
      handleClose,
      handleCloseUpload
    };
  }
});
</script>

<style scoped>
.bg-card {
  transition: transform 0.3s, box-shadow 0.3s;
}

.bg-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.bg-card-selected {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style> 