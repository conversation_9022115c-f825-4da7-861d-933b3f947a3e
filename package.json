{"name": "sapientia", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "api": "node service/index.js", "build": "vite build", "preview": "vite preview", "deploy": "yarn build && gh-pages -d dist -b pages -r https://gitee.com/stepui/stepin-template.git", "deploy:github": "yarn build --mode github && gh-pages -d dist -b master -r **************:stepui/stepui.github.io.git", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:serve": "vitepress serve docs", "docs:preview": "vitepress preview docs", "docs:deploy": "vitepress build docs && gh-pages -d docs/.vitepress/dist -b main -r https://gitee.com/stepui/stepin-template-docs.git"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@vueuse/core": "^10.1.0", "ali-oss": "^6.17.1", "ant-design-vue": "^3.2.15", "axios": "^0.21.1", "clipboard": "^2.0.8", "dayjs": "^1.11.6", "default-passive-events": "^2.0.0", "echarts": "^5.3.3", "enquire.js": "^2.1.6", "highlight.js": "^11.7.0", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "markdown-it": "^13.0.1", "nprogress": "^0.2.0", "pinia": "^2.0.33", "qrcode-vue3": "^1.6.8", "qs": "^6.10.1", "spark-md5": "^3.0.2", "splitpanes": "^3.1.5", "stepin": "2.1.40-beta", "uuid": "^9.0.0", "vue": "3.3.4", "vue-router": "^4.1.6", "xlsx": "^0.18.5"}, "devDependencies": {"@stagewise/toolbar-vue": "^0.6.0", "@tailwindcss/container-queries": "^0.1.0", "@types/js-cookie": "^3.0.2", "@types/lodash": "^4.14.191", "@types/mockjs": "^1.0.3", "@types/node": "^14.14.37", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.6", "@vitejs/plugin-vue": "^4.1.0", "autoprefixer": "^10.4.7", "babel-preset-es2015": "^6.24.1", "babel-register": "^6.26.0", "body-parser": "^1.20.1", "crypto-js": "^4.1.1", "gh-pages": "^3.1.0", "gulp": "^4.0.2", "gulp-clean": "^0.4.0", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "less": "^4.1.3", "mockjs": "^1.1.0", "postcss": "^8.4.14", "prettier": "^2.8.7", "sass": "^1.58.3", "tailwindcss": "^3.2.4", "typescript": "^4.6.4", "unplugin-vue-components": "^0.24.1", "vite": "^4.2.1", "vite-plugin-html": "^3.2.0", "vitepress": "^1.0.0-alpha.61", "vue-tsc": "^1.0.13"}}