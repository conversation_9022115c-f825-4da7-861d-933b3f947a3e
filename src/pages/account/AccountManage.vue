<template>
  <a-modal
    :key="Math.random() * *********000000000"
    :visible="showImportUserModalVisible"
    title="批量导入"
    width="1200px"
    style="top: 0px;"
    :footer="null"
    @cancel="setImportUserModalVisible(false)"
  >
    <div>
      <a-steps :current="importUserCurrent">
        <a-step v-for="item in importUserSteps" :key="item.title" :title="item.title" />
      </a-steps>
      <div v-if="importUserCurrent === 0">
        <a-upload-dragger 
          style="margin-top: 16px;" 
          accept=".xls,.xlsx"
          :showUploadList="false"
          :multiple="false"
          :before-upload="handleImportUserBeforeUpload"
        >
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">将Excel文件拖到此处或点击上传</p>
          <p class="ant-upload-hint">单次导入的文件大小应小于10MB</p>
        </a-upload-dragger>
        <a target="blank" href="https://res.isapientia.com/tpl/%E8%B4%A6%E5%8F%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF0408.xlsx">下载Excel数据模板</a>
      </div>
      <div v-if="importUserCurrent === 1">
        <a-table 
          style="margin-top: 16px;"
          bordered
          :data-source="importUserTableData" 
          :columns="importUserTableColumns"
          :pagination="false"
          size="small"
          :scroll="{x: 1200, y: 400}"
        >
        <template #title>
          <div class="text-subtext text-sm flex justify-between items-center">
            <span style="font-size:14px;">共 {{ importUserTableData.length }} 条记录</span>
          </div>
        </template>
        <template #bodyCell="{ column, text, record }">
          <!-- <template v-if="['username', 'email', 'nickname'].includes(column.dataIndex)"> -->
          <template v-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <span v-if="editableData[record.key]">
                <a-typography-link @click="save(record.key)">保存</a-typography-link>
                &nbsp;
                <a-typography-link @click="cancel(record.key)">取消</a-typography-link>
              </span>
              <span v-else>
                <a-typography-link @click="edit(record.key)">修改</a-typography-link>
                &nbsp;
                <a-popconfirm title="确认要删除吗?" @confirm="remove(record.key)">
                  <a-typography-link style="color: red;">删除</a-typography-link>
                </a-popconfirm>
              </span>
            </div>
          </template>
          <template v-else>
            <div>
              <a-input
                v-if="column.dataIndex != 'key' && editableData[record.key]"
                v-model:value="editableData[record.key][column.dataIndex]"
                style="margin: -5px 0"
              />
              <template v-else>
                {{ text }}
              </template>
            </div>
          </template>
        </template>
        </a-table>
      </div>
      <div v-if="importUserCurrent === 2">
        <a-result
          status="success"
          title="导入结果"
          :sub-title="`总共${importUserTableData.length}条记录，成功导入${importUserSuccessCount}条，失败${importUserFailedResult.length}条`"
        >
          <template #extra>
            <a-button type="primary" @click="setImportUserModalVisible(true)">继续导入</a-button>
            <a-button @click="setImportUserModalVisible(false)">关闭</a-button>
          </template>
          <div v-if="importUserFailedResult.length > 0" style="max-height: 200px; overflow: auto;">
            <p style="font-size: 16px">
              <strong>导入失败记录的原因:</strong>
            </p>
            <template v-for="(failed_reason, i) in importUserFailedResult" :key="i">
              <p>
                <close-circle-outlined :style="{ color: 'red' }" />
                {{ failed_reason }}
              </p>
            </template>
          </div>
        </a-result>
      </div>
      <div class="steps-action">
        <a-button v-if="importUserCurrent === 0" 
          type="primary" 
          :disabled="importUserTableData.length === 0"
          @click="importUserNext">下一步</a-button>
        <div v-if="importUserCurrent === 1" >
          <a-input v-model:value="init_password" 
            :max-length="100"
            style="width: 160px;"
            placeholder="请设置初始密码"
          />
          <a-button type="link" @click="genInitPassword">生成</a-button>
          <a-button 
            type="primary" 
            :loading="loading"
            :disabled="importUserTableData.length === 0 || init_password === ''"
            @click="importUser">导入</a-button>  
          <a-button style="margin-left: 8px" @click="importUserPrev">重新上传</a-button>
        </div>
      </div>
    </div>
  </a-modal>
  <div class="authority">
    <template>
      <a-drawer 
        :title="`${formData.id>0?'修改账号信息':'新增账号'}`" 
        :closable="true" 
        size="large" 
        :visible="showCreateAccount" 
        :destroyOnClose="true"
        @close="changeShowCreateAccountVisible(false)"
      >
        <template #extra>
          <a-button style="margin-right: 8px" @click="changeShowCreateAccountVisible(false)">取消</a-button>
          <a-button type="primary" @click="adminAddAccount" :loading="loading">保存</a-button>
        </template>
        <a-form ref="form" :model="formData" :labelCol="{ span: 3 }" :wrapperCol="{ span: 12, offset: 0 }">
          <a-form-item required name="username" label="账号"
            :rules="[{ required: true, message: '请输入账号' }]"
          >
            <a-input v-model:value="formData.username" show-count placeholder="请输入账号" :maxlength="20" :disabled="formData.id>0"/>
          </a-form-item>
          <a-form-item required name="email" label="电子邮箱" 
            :rules="[{ type: 'email', message: '电子邮箱格式不正确' }, { required: true, message: '请输入电子邮箱' }]"
          >
            <a-input v-model:value="formData.email" show-count placeholder="请输入邮箱" :maxlength="100"/>
          </a-form-item>
          <a-form-item required name="nickname" label="姓名" :maxlength="20"
            :rules="[{ required: true, message: '请输入姓名' }]"
          >
            <a-input v-model:value="formData.nickname" show-count placeholder="请输入姓名" :maxlength="20"/>
          </a-form-item>
          <a-form-item required name="password" label="密码" :maxlength="128"
            :rules="[{ required: true, message: '请输入密码' }]" v-if="formData.id===0"
          >
            <a-input v-model:value="formData.password" placeholder="请输入密码" />
            <a-button type="link" size="small" @click="genPassword">自动生成密码并复制账号信息</a-button>
          </a-form-item>
          <!-- <a-form-item name="student_no" label="学号">
            <a-input v-model:value="formData.student_no" show-count placeholder="请输入学号" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="student_card_no" label="校园卡号">
            <a-input v-model:value="formData.student_card_no" show-count placeholder="请输入校园卡号" :maxlength="100"/>
          </a-form-item> -->
          <a-form-item name="gender" label="性别">
            <a-radio-group v-model:value="formData.gender">
              <a-radio value="男">男</a-radio>
              <a-radio value="女">女</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item name="department" label="院系">
            <a-input v-model:value="formData.department" show-count placeholder="请输入院系" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="class_name" label="班级">
            <a-input v-model:value="formData.class_name" show-count placeholder="请输入班级" :maxlength="100"/>
          </a-form-item>
          <!-- <a-form-item name="semester" label="学期">
            <a-input v-model:value="formData.semester" show-count placeholder="请输入学期" :maxlength="100"/>
          </a-form-item> -->
          <!-- <a-form-item name="course" label="课程">
            <a-input v-model:value="formData.course" show-count placeholder="请输入课程名称" :maxlength="100"/>
          </a-form-item> -->
          <a-form-item
            v-for="(semester, index) in formData.semesters"
            :key="semester.id"
            v-bind="index === 0 ? formItemLayout : {}"
            :label="index === 0 ? '学期' : ' '"
            :colon="index === 0"
            :name="['semesters', index, 'name']"
          >
            <div class="flex justify-between items-center">
              <a-input
                v-model:value="semester.name"
                placeholder="请输入学期"
                show-count
                :maxlength="100"
              />
              <MinusCircleOutlined
                style="margin-left: 8px;"
                class="dynamic-delete-button"
                @click="removeSemester(semester)"
              />
            </div>
          </a-form-item>
          <a-form-item label=" " :colon="false">
            <a-button type="dashed" style="width: 40%" @click="addSemesters">
              <PlusOutlined />
              添加学期
            </a-button>
          </a-form-item>
          <a-form-item
            v-for="(course, index) in formData.courses"
            :key="course.id"
            v-bind="index === 0 ? formItemLayout : {}"
            :label="index === 0 ? '课程' : ' '"
            :colon="index === 0"
            :name="['courses', index, 'name']"
          >
            <div class="flex justify-between items-center">
              <a-input
                v-model:value="course.name"
                placeholder="请输入课程名称"
                show-count
                :maxlength="100"
              />
              <MinusCircleOutlined
                style="margin-left: 8px;"
                class="dynamic-delete-button"
                @click="removeCourse(course, false)"
              />
            </div>
          </a-form-item>
          <a-form-item label=" " :colon="false">
            <a-button type="dashed" style="width: 40%" @click="addCourses(false)">
              <PlusOutlined />
              添加课程
            </a-button>
          </a-form-item>

          <a-form-item name="student_type" label="学生类型">
            <a-input v-model:value="formData.student_type" show-count placeholder="请输入学生类型" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="mobile" label="联系方式">
            <a-input v-model:value="formData.mobile" show-count placeholder="请输入联系方式" :maxlength="50"/>
          </a-form-item>
          <a-form-item name="nation" label="国籍">
            <a-input v-model:value="formData.nation" show-count placeholder="请输入国籍" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="partner" label="单位">
            <a-input v-model:value="formData.partner" show-count placeholder="请输入单位" :maxlength="200"/>
          </a-form-item>
          <a-form-item name="usage_limit" label="可用量">
            <a-input-number @change="handleUsageLimitChange" min="0" max="2147483647" v-model:value="formData.usage_limit" placeholder="请输入可用量" addon-after="条"></a-input-number>
            <a-checkbox @change="handleUsageNoLimitChange" style="margin: 4px;" v-model:checked="formData.usage_no_limit">不限制</a-checkbox>
          </a-form-item>
          <a-form-item name="expire_date" label="有效期限">
            <a-date-picker @change="handleExpireDateChange" format="YYYY-MM-DD" style="width: 217px" :disabled-date="disabledExpireDate" placeholder="请选择账号的到期日期" v-model:value="formData.expire_date" :showToday="false" />
            <a-checkbox @change="handleExpireDateNoLimitChange" style="margin: 4px;" v-model:checked="formData.expire_date_no_limit">不限制</a-checkbox>
          </a-form-item>
          <a-form-item name="model_group_id" label="模型权限">
            <a-select
              style="width: 217px"
              allowClear
              placeholder="请选择模型分组"
              v-model:value="formData.model_group_id"
              @change="handleModelGroupChange"
              :options="modelGroupOptions"
            ></a-select>
            <a-checkbox @change="handleModelNoLimitChange" style="margin: 4px;" v-model:checked="formData.model_no_limit">不限制</a-checkbox>
          </a-form-item>
          <a-form-item name="remark" label="备注">
            <a-textarea v-model:value="formData.remark" placeholder="备注" :rows="4" :maxlength=3000 show-count/>
          </a-form-item>
        </a-form>
      </a-drawer>
    </template>
    <template>
      <a-drawer 
        title="批量修改账号信息" 
        :closable="true" 
        size="large" 
        :visible="showBatchModifyAccount" 
        :destroyOnClose="true"
        @close="changeShowBatchModifyAccountVisible(false)"
      >
        <template #extra>
          <a-button style="margin-right: 8px" @click="changeShowBatchModifyAccountVisible(false)">取消</a-button>
          <a-button type="primary" @click="adminBatchModifyAccount" :loading="loading">保存</a-button>
        </template>
        <a-form :model="formDataBatchModify" :labelCol="{ span: 3 }" :wrapperCol="{ span: 12, offset: 0 }">
          <a-form-item name="password" label="密码" :maxlength="128">
            <a-input v-model:value="formDataBatchModify.password" placeholder="请输入密码" />
            <a-button type="link" size="small" @click="genPasswordModify">自动生成密码并复制</a-button>
          </a-form-item>
          <a-form-item name="gender" label="性别">
            <a-radio-group v-model:value="formDataBatchModify.gender">
              <a-radio value="男">男</a-radio>
              <a-radio value="女">女</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item name="department" label="院系">
            <a-input v-model:value="formDataBatchModify.department" show-count placeholder="请输入院系" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="class_name" label="班级">
            <a-input v-model:value="formDataBatchModify.class_name" show-count placeholder="请输入班级" :maxlength="100"/>
          </a-form-item>
          <!-- <a-form-item name="semester" label="学期">
            <a-input v-model:value="formDataBatchModify.semester" show-count placeholder="请输入学期" :maxlength="100"/>
          </a-form-item> -->

          <a-form-item label="学期" :colon="true">
            <a-radio-group style="margin-left: 10px;"
              v-model:value="formDataBatchModify.semesters_opt">
              <a-radio :value="1">追加</a-radio>
              <a-radio :value="2">修改</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item name="src_course" label=" " :colon="false" v-if="formDataBatchModify.semesters_opt === 2">
            <a-input v-model:value="formDataBatchModify.src_semester" show-count placeholder="请输入原学期" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="target_course" label=" " :colon="false">
            <a-input v-model:value="formDataBatchModify.target_semester" show-count :placeholder='formDataBatchModify.semesters_opt === 1?"请输入追加学期":"请输入修改学期"' :maxlength="100"/> 
          </a-form-item>

          <a-form-item label="课程" :colon="true">
            <a-radio-group style="margin-left: 10px;"
              v-model:value="formDataBatchModify.courses_opt">
              <a-radio :value="1">追加</a-radio>
              <a-radio :value="2">修改</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item name="src_course" label=" " :colon="false" v-if="formDataBatchModify.courses_opt === 2">
            <a-input v-model:value="formDataBatchModify.src_course" show-count placeholder="请输入原课程" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="target_course" label=" " :colon="false">
            <a-input v-model:value="formDataBatchModify.target_course" show-count :placeholder='formDataBatchModify.courses_opt === 1?"请输入追加课程":"请输入修改课程"' :maxlength="100"/> 
          </a-form-item>

          <a-form-item name="student_type" label="学生类型">
            <a-input v-model:value="formDataBatchModify.student_type" show-count placeholder="请输入学生类型" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="nation" label="国籍">
            <a-input v-model:value="formDataBatchModify.nation" show-count placeholder="请输入国籍" :maxlength="100"/>
          </a-form-item>
          <a-form-item name="partner" label="单位">
            <a-input v-model:value="formDataBatchModify.partner" show-count placeholder="请输入单位" :maxlength="200"/>
          </a-form-item>
          <a-form-item name="usage_limit" label="可用量">
            <a-input-number min="0" max="2147483647" v-model:value="formDataBatchModify.usage_limit" placeholder="请输入可用量" addon-after="条"></a-input-number>
            <a-checkbox style="margin: 4px;" v-model:checked="formDataBatchModify.usage_no_limit">不限制</a-checkbox>
          </a-form-item>
          <a-form-item name="expire_date" label="有效期限">
            <a-date-picker format="YYYY-MM-DD" style="width: 217px" :disabled-date="disabledExpireDate" placeholder="请选择账号的到期日期" v-model:value="formDataBatchModify.expire_date" :showToday="false" />
            <a-checkbox style="margin: 4px;" v-model:checked="formDataBatchModify.expire_date_no_limit">不限制</a-checkbox>
          </a-form-item>
          <a-form-item name="model_group_id" label="模型权限">
            <a-select
              style="width: 217px"
              allowClear
              placeholder="请选择模型分组"
              v-model:value="formDataBatchModify.model_group_id"
              :options="modelGroupOptions"
            ></a-select>
            <a-checkbox style="margin: 4px;" v-model:checked="formDataBatchModify.model_no_limit">不限制</a-checkbox>
          </a-form-item>
          <a-form-item name="remark" label="备注">
            <a-textarea v-model:value="formDataBatchModify.remark" placeholder="备注" :rows="4" :maxlength=3000 show-count/>
          </a-form-item>
        </a-form>
      </a-drawer>
    </template>
    <template>
      <a-drawer 
        title="重置密码" 
        :closable="true" 
        size="large" 
        :visible="showAccountPasswordReset" 
        :destroyOnClose="true"
        @close="changeShowAccountPasswordResetVisible(false, null)"
      >
        <template #extra>
          <a-button style="margin-right: 8px" @click="changeShowAccountPasswordResetVisible(false, null)">取消</a-button>
          <a-button type="primary" @click="adminResetPassword">保存</a-button>
        </template>
        <a-form ref="formReset" :model="formDataPassword" :labelCol="{ span: 3 }" :wrapperCol="{ span: 8, offset: 0 }">
          <a-form-item required name="username" label="账号">
            <a-input v-model:value="formDataPassword.username" placeholder="请输入账号" :maxlength="20" disabled/>
          </a-form-item>
          <a-form-item required name="password" label="新密码" :maxlength="128"
            :rules="[{ required: true, message: '请输入新密码' }]"
          >
            <a-input v-model:value="formDataPassword.password" placeholder="请输入新密码" />
            <a-button type="link" size="small" @click="genResetPassword">自动生成密码并复制账号信息</a-button>
          </a-form-item>
        </a-form>
      </a-drawer>
    </template>
    <template>
      <a-drawer 
        title="查看密码修改记录" 
        :closable="true" 
        size="large" 
        :visible="showAccountPasswordChangeRecord" 
        :destroyOnClose="true"
        @close="changeShowAccountPasswordRecordVisible(false, null)"
      >
        <template #extra>
          <a-button style="margin-right: 8px" @click="changeShowAccountPasswordRecordVisible(false, null)">取消</a-button>
        </template>
        <a-table :columns="passwordTableColumns" :dataSource="passwordRecords" :pagination="false">
          <template #bodyCell="{ text, record, index, column }">
            <template v-if="column.dataIndex === 'account'">
              {{ record.opt_account.username }}
            </template>
            <template v-else-if="column.dataIndex === 'password'">
              {{ record.opt_account.is_superuser?record.change_data.password:"******" }}
            </template>
            <template v-else-if="column.dataIndex === 'create_time'">
              {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
            </template>
            <template v-else-if="column.dataIndex === 'remark'">
              {{ !record.change_data?'自己修改':record.change_data.type === 'reset'?'重置密码':record.change_data.type === 'batch_modify'?'批量修改': '-' }}
            </template>
          </template>
        </a-table>
      </a-drawer>
    </template>
    <template>
      <a-drawer 
        title="导出账号" 
        :closable="true" 
        size="large" 
        :visible="showExportAccountVisible" 
        :destroyOnClose="true"
        @close="showExportAccountVisible = false"
      >
        <template #extra>
          <span style="margin-right:10px;width:200px;">筛选<font color="#1377FF">“{{ keyword?keyword:"全部" }}”</font> 共计：<font color="#1377FF">{{ accountPage.count }}</font> 条</span>
          <a-button style="margin-right: 8px" @click="showExportAccountVisible = false">取消</a-button>
          <a-button type="primary" @click="exportData">导出</a-button>
        </template>
        <div style="margin-left: 30px;">
          <a-checkbox v-model:checked="exportResetPassword">重置账号密码并导出密码列</a-checkbox>
        </div>
      </a-drawer>
    </template>
    <template>
      <a-drawer 
        title="账号属性字段扩展" 
        :closable="true" 
        size="large" 
        :visible="showExtendColumnConfig" 
        :destroyOnClose="true"
        @close="changeShowAccountPasswordResetVisible(false, null)"
      >
        <template #extra>
          <a-button style="margin-right: 8px" @click="changeShowAccountPasswordResetVisible(false, null)">取消</a-button>
        </template>
        <p>
          <a-alert
            :message="`扩展字段`"
            style="background-color: #e6f4ff; border: 1px solid #91caff; margin-bottom: 10px;"
          />
        </p>
        <p>
          <a-input
            ref="inputRef"
            v-model:value="state.inputValue"
            placeholder="输入字段名, 回车保存"
            show-count
            :maxlength="10"
            :style="{ width: '340px' }"
            :disabled="state.tags.length >= state.max_column_count"
            @blur="handleInputConfirm"
            @keyup.enter="handleInputConfirm"
          />
        </p>
        <p>
          <span>已新增：</span><span style="color: #1890ff;">{{state.tags.length}}/{{state.max_column_count}}</span>
        </p>
        <p>
          <span></span>
          <template v-for="(tag, index) in state.tags" :key="tag">
            <a-tag :closable="true" @close="handleClose(tag)">
              {{ tag }}
            </a-tag>
          </template>
        </p>
      </a-drawer>
    </template>
    <template>
      <ModelGroupManage 
        :modelGroupDrawer="modelGroupDrawer" 
        :groupModelTableList="groupModelTableList"
        @setModelGroupVisible="setModelGroupVisible"
        @getModelGroupTableList="getModelGroupTableList"
      />
    </template>
    <a-table 
      :row-selection="rowSelection"
      :columns="columns.filter(item=> item.key === 'operation' || columnSetting.includes(item.key))" 
      :dataSource="accountPage.results" 
      :pagination="false"
      :scroll="{ x: 1200}"
      @change="handleTableChange"
    >
      <template #title>
        <div class="flex justify-between">
          <h1>账号管理</h1>
        </div>
        <div class="text-subtext text-sm flex justify-between items-center">
          <span style="font-size:14px;">
            主账号可以管理子账号
          </span>
          <div>
            <a-button style="margin-right: 5px;" type="primary" @click="setModelGroupVisible(true)">
              <SettingOutlined class="text-lg"/>模型分组
            </a-button>
            <a-button style="margin-right: 5px;" type="primary" @click="changeShowCreateAccountVisible(true)">
              <PlusOutlined class="text-lg" />新增账号
            </a-button>
            <a-button style="margin-right: 5px;" type="default" @click="setImportUserModalVisible(true)">
              <ImportOutlined class="text-lg"/>批量导入</a-button>
            <a-input-search
              v-model:value="keyword"
              placeholder="多个关键字用空格分隔"
              style="width: 250px;"
              allowClear
              @search="onSearch"
              @change="onSearchChange"
            />
            <a-button style="margin-left: 5px;" type="primary" @click="viewExportDrawer"
              :disabled="accountPage.count === 0"
              ><ExportOutlined class="text-lg" />导出</a-button>
          </div>
        </div>
        <div class="text-subtext text-sm flex items-center" v-if="stateSelectedRowKeys.length > 0">
          <a-space wrap>
            <span style="font-size:14px;">已选择: <span style="color:#ff9640;">{{ stateSelectedRowKeys.length }}</span>条记录</span>
            <a-button size="small" type="primary" ghost @click="changeShowBatchModifyAccountVisible(true)">批量修改</a-button>
            <a-popconfirm placement="topLeft" title="确认批量启用吗？" @confirm="batchDisableAccountRecord(false, true)" ok-text="确认" cancel-text="取消">
              <a-button size="small" type="primary" ghost>批量启用</a-button>
            </a-popconfirm>
            <a-popconfirm placement="topLeft" title="确认批量禁用吗？" @confirm="batchDisableAccountRecord(false, false)" ok-text="确认" cancel-text="取消">
              <a-button size="small" danger type="primary" ghost>批量禁用</a-button>
            </a-popconfirm>
            <a-popconfirm placement="topLeft" title="确认批量删除吗？" @confirm="batchDisableAccountRecord(true, false)" ok-text="确认" cancel-text="取消">
              <a-button size="small" danger type="primary" ghost>批量删除</a-button>
            </a-popconfirm>
            <a-popconfirm placement="topLeft" title="确认批量清空吗？" @confirm="adminBatchClearAccount()" ok-text="确认" cancel-text="取消">
              <a-button size="small" danger type="primary" ghost>批量清空</a-button>
            </a-popconfirm>
            <a-popconfirm placement="topLeft" title="确认批量迁移吗？" @confirm="adminBatchMigrateAccount()" ok-text="确认" cancel-text="取消">
              <a-button size="small" danger type="primary" ghost>批量迁移</a-button>
            </a-popconfirm>
          </a-space>
        </div>
      </template>
      <template #headerCell="{ column }">
        <!-- <template v-if="column.key === 'username'">
          <span style="color: #1890ff">账号</span>
        </template>
        <template v-else-if="column.key === 'nickname'">
          <span style="color: #1890ff">姓名</span>
        </template> -->
        <template v-if="column.key === 'extend'">
          其他信息
          <a-popover title="" trigger="hover">
            <template #content>
              Excel导入的账号其他属性信息
            </template>
            <QuestionCircleOutlined />
          </a-popover>
        </template>
        <template v-if="column.key === 'model_group_id'">
          模型权限
          <a-popover title="" trigger="hover" placement="right">
            <template #content>
              管理子账号在使用“对话”及“工作流”时的可用模型
            </template>
            <QuestionCircleOutlined />
          </a-popover>
        </template>
        <template v-else-if="column.key === 'operation'">
          操作
          <a-popover title="" trigger="hover" placement="leftTop">
            <template #content>
              <h4> 
                <a-checkbox
                  v-model:checked="columnCheckAll"
                  :indeterminate="columnIndeterminate"
                  @change="onCheckAllChange"
                >列展示</a-checkbox>
              </h4>
              <a-checkbox-group v-model:value="columnSetting" @change="handleColumnSetting">
                <template v-for="item in columns.filter(item => !['operation', 'username'].includes(item.dataIndex))">
                  <a-row>
                    <a-col :span="24">
                      <a-checkbox :value="item.key">{{ item.title }}</a-checkbox>
                    </a-col>
                  </a-row>
                </template>
              </a-checkbox-group>
            </template>
            <SettingOutlined />
          </a-popover>
          <a-popover title="" trigger="hover" placement="leftTop">
            <template #content>
              <div v-if="runningTask.length > 0">
                <div class="flex justify-between items-center">
                  <a-typography-title :level="5">有{{runningTask.length}}个任务正在处理，大概需要{{runningTask.length * 2}}分钟左右。</a-typography-title>  
                  <sync-outlined @click="refreshRunningTaskList" style="font-size: 16px;margin-bottom: 4px;"/>
                </div>
                <a-space direction="vertical">
                  <template v-for="(task, i) in runningTask" :key="task.task_id">
                    <a-typography-text>{{ i + 1 }}.{{ task.task_name }}</a-typography-text>
                  </template>
                </a-space>
              </div>
            </template>
            <a-badge dot style="margin-left: 6px;cursor: pointer;" v-if="runningTask.length > 0">
              <ClockCircleOutlined />
            </a-badge>
          </a-popover>
        </template>
      </template>
      <!-- <template
        #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
      >
        <div style="padding: 8px" v-if="column.dataIndex === 'username'">
          <a-input
            ref="searchInput"
            placeholder="账号"
            :value="selectedKeys[0]"
            style="width: 188px; margin-bottom: 8px; display: block"
            @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
            @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
          />
          <a-button
            type="primary"
            size="small"
            style="width: 90px; margin-right: 8px"
            @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
          >
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
          <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
            重置
          </a-button>
        </div>
        <div style="padding: 8px" v-if="column.dataIndex === 'nickname'">
          <a-input
            ref="searchNicknameInput"
            placeholder="姓名"
            :value="selectedKeys[0]"
            style="width: 188px; margin-bottom: 8px; display: block"
            @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
            @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
          />
          <a-button
            type="primary"
            size="small"
            style="width: 90px; margin-right: 8px"
            @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
          >
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
          <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
            重置
          </a-button>
        </div>
      </template> -->
      <!-- <template #customFilterIcon="{ filtered }">
        <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
      </template> -->
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'operation'">
          <div v-if="!record.is_superuser">
            <a @click="changeAccountRecord(record)">修改</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a style="color:#0ea5e9;" @click="fetchChatRecord(record)">对话记录</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a style="color:#0ea5e9;" @click="changeShowAccountPasswordResetVisible(true, record)">重置密码</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a style="color:#0ea5e9;" @click="changeShowAccountPasswordRecordVisible(true, record)">密码修改记录</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm placement="topLeft" title="确认要再次启用吗？" @confirm="active(record, false, true)" v-if="!record.is_active" ok-text="确认" cancel-text="取消">
                      <a>启用</a>
                    </a-popconfirm>
                    <a-popconfirm placement="topLeft" title="确认禁用吗？" @confirm="active(record, false, false)" v-if="record.is_active" ok-text="确认" cancel-text="取消">
                      <a style="color: red;">禁用</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm placement="topLeft" title="确认删除吗？" @confirm="active(record, true, false)" ok-text="确认" cancel-text="取消">
                      <a style="color: red;">删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm placement="topLeft" title="确认清空吗？" @confirm="adminBatchClearAccountList([record.id])" ok-text="确认" cancel-text="取消">
                      <a style="color: red;">清空</a>
                    </a-popconfirm>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm placement="topLeft" title="确认迁移吗？" @confirm="adminBatchMigrateAccountList([record.id])" ok-text="确认" cancel-text="取消">
                      <a style="color: red;">迁移</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </template>
              <a>
                更多
                <down-outlined />
              </a>
            </a-dropdown>
          </div>
        </template>
        <template v-else-if="['username', 'nickname'].includes(column.dataIndex)">
          <div v-html="text?searchResultHighlight(text):''" />
        </template>
        <template v-else-if="['student_no'].includes(column.dataIndex)">
          <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width">
            <template #content>
              <div v-html="fixStudentNo(record)?searchResultHighlight(fixStudentNo(record)):''" />
            </template>
            <div v-html="fixStudentNo(record)?(fixStudentNo(record).length > 6?searchResultHighlight(fixStudentNo(record).slice(0, 6)) + '...':searchResultHighlight(fixStudentNo(record))):''" v-if="keyword" />
            {{ keyword?"":fixStudentNo(record) }}
          </a-popover>
        </template>
        <template v-else-if="['email', 'department', 'class_name', 'mobile', 'student_type', 
        'nation',  'remark', 'semester'].includes(column.dataIndex)">
          <a-popover title="" trigger="hover" overlay-class-name="limit-popover-width">
            <template #content>
              <div v-html="text?searchResultHighlight(text):''" />
            </template>
            <div v-html="text?searchResultHighlight(text):''" v-if="keyword"/>
            {{ keyword?"":text }}
          </a-popover>
        </template>
        <template v-else-if="column.dataIndex === 'courses'">
          <div style="display: flex;
              flex-direction: column; /* 设置为垂直排列 */
              align-items: flex-start; /* 左对齐 */
              gap: 8px; /* 设置标签之间的间距 */">
            <a-tag :color="getCourseColor(course)" v-for="course in record.courses">
              {{ course }}
            </a-tag>
            <!-- <a-tag color="#1377FF">#f50</a-tag> -->
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'semesters'">
          <div style="display: flex;
              flex-direction: column; /* 设置为垂直排列 */
              align-items: flex-start; /* 左对齐 */
              gap: 8px; /* 设置标签之间的间距 */">
            <a-tag :color="getCourseColor(semester)" v-for="semester in record.semesters">
              {{ semester }}
            </a-tag>
            <!-- <a-tag color="#1377FF">#f50</a-tag> -->
          </div>
        </template>
        <template v-else-if="['model_group_id'].includes(column.dataIndex)">
          {{ record.model_group_id?groupModelTableList.filter(item => item.id === record.model_group_id)[0].name: "不限制" }}
        </template>
        <template v-else-if="['extend'].includes(column.dataIndex)">
          <span v-if="text">
            <span v-for="item in Object.entries(text)">{{ item[0] + ": " + item[1]}}<br/></span>
          </span>
          <span v-else>-</span>
        </template>
        <!-- <template v-else-if="column.dataIndex === 'gender'">
          {{ record.extend && Object.keys(record.extend).includes('性别')?record.extend['性别']:"" }}
        </template>
        <template v-else-if="column.dataIndex === 'department'">
          {{ record.extend && Object.keys(record.extend).includes('院系')?record.extend['院系']:"" }}
        </template>
        <template v-else-if="column.dataIndex === 'class'">
          {{ record.extend && Object.keys(record.extend).includes('班级')?record.extend['班级']:"" }}
        </template>
        <template v-else-if="column.dataIndex === 'student_type'">
          {{ record.extend && Object.keys(record.extend).includes('学生类型')?record.extend['学生类型']:"" }}
        </template>
        <template v-else-if="column.dataIndex === 'mobile'">
          {{ record.extend && Object.keys(record.extend).includes('联系方式')?record.extend['联系方式']:"" }}
        </template>
        <template v-else-if="column.dataIndex === 'nation'">
          {{ record.extend && Object.keys(record.extend).includes('国籍')?record.extend['国籍']:"" }}
        </template> -->
        <template v-else-if="column.dataIndex === 'usage_limit'">
          <span>{{ text && text > 0?(text-record.actual_usage) + '条': '不限制' }}</span>
          <br/>
          <span v-if="record.actual_usage > 0" style="color:#ff9640;font-size: small;">已使用: {{ record.actual_usage }} 条</span>
          <span v-else style="color:#ff9640;font-size: small;">暂未使用</span>
        </template>
        <template v-else-if="column.dataIndex === 'last_login'">
          {{ text?dayjs(text).format('YYYY-MM-DD'):'-' }}
        </template>
        <template v-else-if="column.dataIndex === 'expire_date'">
          {{ text?dayjs(text).format('YYYY-MM-DD'):'不限制' }}
        </template>
        <template v-else-if="column.dataIndex === 'create_time'">
          {{ text?dayjs(text).format('YYYY-MM-DD'):'-' }}
        </template>
        <template v-else-if="column.dataIndex === 'is_active'">
          <span v-if="record.is_superuser">主账号</span>
          <span v-else-if="record.delete_flag" style="color:red;">已删除</span>
          <span v-else-if="record.expire_flag" style="color:red;">已过期</span>
          <span v-else-if="text" style="color: green;">已启用</span>
          <span v-else>未启用</span>
        </template>
      </template>
    </a-table>
    <div style="margin-top: 10px;float: right;">
      <a-pagination
        size="small" 
        v-model:current="accountPage.page_number" 
        v-model:pageSize="accountPage.page_size"
        :page-size-options="pageSizeOptions"
        :total="accountPage.count" 
        show-less-items 
        show-quick-jumper
        :show-size-changer="true"
        @change="onShowSizeChange"
      >
        <template #buildOptionText="props">
          <span v-if="props.value !== '*********'">{{ props.value }} / page</span>
          <span v-else> 全部 </span>
        </template>
      </a-pagination>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, computed, watch, reactive, onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import { 
  getAccountExport, 
  getAccountPage, 
  addAccount, 
  disableAccount, 
  batchDisableAccount, 
  resetAccountPassword, 
  importAccount, 
  batchModifyAccount, 
  batchClearAccount, 
  batchMigrateAccount, 
  getRunningTaskList, 
  getChangeAccountPasswordRecords, 
  getModelGroupList, 
} from "@/api/account";
import { message } from 'ant-design-vue';
import { useClipboard } from '@vueuse/core'
import { importExcelData } from '@/utils/importExcel'
import { cloneDeep } from 'lodash-es';
import type { TableProps } from 'ant-design-vue';
import * as XLSX from 'xlsx'
import { useRouter } from 'vue-router';
import ModelGroupManage from './ModelGroupManage.vue';
import { v4 as uuidv4 } from 'uuid';

const { copy } = useClipboard({ legacy: true })

const pageSizeOptions = ref<string[]>(['*********', '10', '50', '100', '200']);
const accountPage = ref({});
const inputRef = ref();
const router = useRouter();

const modelGroupDrawer = ref(false);
const showBatchModifyAccount = ref(false);
const showAccountPasswordChangeRecord = ref(false);

const importUserCurrent = ref<number>(0);
const importUserSuccessCount = ref<number>(0);
const importUserFailedResult = ref([]);
const importUserTableData = ref([]);
const init_password = ref("");

const columnCheckAll = ref(true);
const columnIndeterminate = ref(false);
const columnSetting = ref(["username"]);
const keyword = ref("");
const runningTask = ref([]);
const showExportAccountVisible = ref(false);
const exportResetPassword = ref(false);
const passwordRecords = ref([]);

const formItemLayout = {
  // labelCol: {
  //   xs: { span: 24 },
  //   sm: { span: 4 },
  // },
  // wrapperCol: {
  //   xs: { span: 24 },
  //   sm: { span: 20 },
  // },
};

const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 12, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};

const defaultPageSize = ref(
  localStorage.getItem('AccountPageSize')? JSON.parse(localStorage.getItem('AccountPageSize')).pageSize : *********
)

const searchResultHighlight = (content) => {
  if(!keyword.value){
    return content;
  }
  const keywords = keyword.value.split(" ")
  keywords.forEach((item) => {
    if(item){
      const reg = new RegExp(`(${item})`, "ig");
      content = content.replace(
        reg,
        `<span style="color:#1377FF">$1</span>`
      );
    }
  });
  // const reg = new RegExp(`(${keyword.value})`, "ig");
  // return content.replace(
  //   reg,
  //   `<span style="color:#1377FF">$1</span>`
  // );
  return content;
}

function fixStudentNo(record){
  if (record.student_no && record.student_card_no){
    return record.student_no + "/" + record.student_card_no
  }else if (record.student_no){
    return record.student_no
  }else if (record.student_card_no){
    return record.student_card_no
  }
  return "";
}

function getCourseColor(course){
  // 如果 keyword.value 为空，直接返回蓝色
  if (!keyword.value) {
      return "blue";
  }
  // 将 keyword.value 按空格分割成关键词数组
  const keywords = keyword.value.split(" ");
  // 使用 some 方法检查是否有关键词存在于 course 中
  const existKeyword = keywords.some((item) => item && course.includes(item));
  // 如果存在关键词，返回黄色，否则返回蓝色
  return existKeyword ? "#1377FF" : "blue";
}

const onSearchChange = (v: string) => {
  console.log('change use value', v);
  onSearch('')
};

const onSearch = (searchValue: string) => {
  console.log('use value', searchValue);
  console.log('or use value', keyword.value);
  refreshPage({keyword: keyword.value})
};

const onCheckAllChange = (e) => {
  const tmp = e.target.checked ? columns.map((item) => item.key) : ["username"];
  columnIndeterminate.value = !e.target.checked;
  // handleColumnSetting(tmp);
  columnSetting.value = tmp;
  localStorage.setItem('AccountSettingColumnKeys', JSON.stringify(tmp));
};

const handleColumnSetting = (selectColumnKeys: any) => {
  localStorage.setItem('AccountSettingColumnKeys', JSON.stringify(selectColumnKeys));
  // columnSetting.value = selectColumnKeys;
  if(!columnSetting.value.includes("username")){
    columnSetting.value.push("username")
  }
  selectColumnKeys.forEach(element => {
    if (!columnSetting.value.includes(element)){
      columnSetting.value.push(element);
    }
  });
  console.log(columnSetting.value);
};

const disabledExpireDate = (current: Dayjs) => {
  return current && current < dayjs().endOf('day');
};

const importUserNext = () => {
  importUserCurrent.value++;
};
const importUserPrev = () => {
  importUserCurrent.value--;
};

const importUser = () =>{
  loading.value = true;
  importAccount({accounts: importUserTableData.value, 
    init_password: init_password.value}).then((res) => {
    const { code, data } = res;
    if (code === 0){
      const { success, failed } = data;
      importUserSuccessCount.value = success;
      importUserFailedResult.value = failed;
      importUserNext();
    }
  }).finally(() => {
    loading.value = false;
  });
}

const editableData: UnwrapRef<Record<string, DataItem>> = reactive({});

const edit = (key: string) => {
  editableData[key] = cloneDeep(importUserTableData.value.filter(item => key === item.key)[0]);
};

const remove = (rownumber: string) => {
  const idx = importUserTableData.value.findIndex(({ key }) => key !== undefined && key === rownumber);
  importUserTableData.value.splice(idx, 1)
}

const save = (key: string) => {
  Object.assign(importUserTableData.value.filter(item => key === item.key)[0], editableData[key]);
  delete editableData[key];
};

const cancel = (key: string) => {
  delete editableData[key];
};

const importUserSteps = [
  {
    title: '上传文件',
    content: '上传文件',
  },
  {
    title: '解析结果',
    content: '解析结果',
  },
  {
    title: '导入结果',
    content: '导入结果',
  },
];

const showImportUserModalVisible = ref(false);
const showCreateAccount = ref(false);
const showAccountPasswordReset = ref(false);
const showExtendColumnConfig = ref(false);

const handleImportUserBeforeUpload = (file) => {
  // 数据处理excel=>json
  importExcelData(file).then((res) => {
    let head = {
      '账号': 'username',
      '学号': 'student_no',
      '校园卡号': 'student_card_no',
      '电子邮箱': 'email',
      '姓名': 'nickname',
      '性别': 'gender',
      '邮箱': 'email_compatible',
      '院系': 'department',
      '班级': 'class_name',
      '学期': 'semester',
      '课程': 'courses',
      '学生类型': 'student_type',
      '联系方式': 'mobile',
      '国籍': 'nation',
      '单位': 'partner',
      '可用量': 'usage_limit',
      '用量限制': 'jr_usage_limit',
      '有效期限': 'expire_date',
      '备注': 'remark',
    }
    let extendColumns = [];
    const arr = res.map(item => {
      const obj = {}
      console.log(item);
      obj['key'] = item["__rowNum__"] + 1
      for (const k in item) {
        if(k in head){
          obj[head[k]] = String(item[k])
        }else{
          extendColumns.push(k);
          obj[k] = String(item[k])
        }
      } 
      if (!("username" in obj)) {
        if ("student_no" in obj){
          obj["username"] = obj["student_no"]
        }else if("student_card_no" in obj){
          obj["username"] = obj["student_card_no"]
        }
      }
      if (!("email" in obj) && "email_compatible" in obj) {
        obj["email"] = obj["email_compatible"]
      }
      if (!("usage_limit" in obj) && "jr_usage_limit" in obj){
        obj["usage_limit"] = obj["jr_usage_limit"]
      }
      return obj;
    });
    console.log(arr);
    if (arr.length === 0){
      message.error("当前文件没有用于导入的数据");
    }else{
      if (extendColumns.length > 0){
        extendColumns = Array.from(new Set(extendColumns));
      }
      let tmp = [
          {
            title: '行号',
            width: 70,
            dataIndex: 'key',
            key: 'key',
            fixed: 'left',
          },
          {
            title: '账号',
            dataIndex: 'username',
            key: 'username',
            fixed: 'left',
          },
          {
            title: '电子邮箱',
            dataIndex: 'email',
            key: 'email',
            fixed: 'left',
          },
          {
            title: '姓名',
            dataIndex: 'nickname',
            key: 'nickname',
            fixed: 'left',
          },
          // {
          //   title: '学号',
          //   dataIndex: 'student_no',
          //   key: 'student_no',
          // },
          // {
          //   title: '校园卡号',
          //   dataIndex: 'student_card_no',
          //   key: 'student_card_no',
          // },
          {
            title: '性别',
            dataIndex: 'gender',
            key: 'gender',
          },
          {
            title: '院系',
            dataIndex: 'department',
            key: 'department',
          },
          {
            title: '班级',
            dataIndex: 'class_name',
            key: 'class_name',
          },
          {
            title: '学期',
            dataIndex: 'semesters',
            key: 'semesters',
          },
          {
            title: '课程',
            dataIndex: 'courses',
            key: 'courses',
          },
          {
            title: '学生类型',
            dataIndex: 'student_type',
            key: 'student_type',
          },
          {
            title: '联系方式',
            dataIndex: 'mobile',
            key: 'mobile',
          },
          {
            title: '国籍',
            dataIndex: 'nation',
            key: 'nation',
          },
          {
            title: '单位',
            dataIndex: 'partner',
            key: 'partner',
          },
          {
            title: '可用量',
            dataIndex: 'usage_limit',
            key: 'usage_limit',
          },
          {
            title: '有效期限',
            dataIndex: 'expire_date',
            key: 'expire_date',
          },
          {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
          },
      ]
      if (extendColumns.length > 0){
        extendColumns.forEach(item => {
          tmp.push(
              {
                title: item,
                dataIndex: item,
                key: item,
              },
          )
        })
      }
      tmp.push(
        {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            fixed: 'right',
          },
      )
      importUserTableColumns.value = tmp;
      importUserTableData.value = arr;
      importUserNext();
    }
  });
  return false;
};

function viewExportDrawer(){
  showExportAccountVisible.value = true;
  exportResetPassword.value = false;
}

const exportData = () => {
  const params = {
    keyword: keyword.value,
    reset_password: exportResetPassword.value
  }
  getAccountExport(params).then((response) => {
    console.log(response);
    const { data } = response;
    let head = {
      username: '账号',
      email: '电子邮箱',
      nickname: '姓名',
      gender: '性别',
      department: '院系',
      class_name: '班级',
      semesters: '学期',
      courses: '课程',
      student_type: '学生类型',
      mobile: '联系方式',
      nation: '国籍',
      partner: '单位',
      is_active: '账号状态',
      usage_limit: '可用量',
      expire_date: '有效期限',
      create_time: '创建时间',
      last_login: '最后登录时间',
      remark: '备注',
    }
    if(exportResetPassword.value){
      head["password"] = '重置密码';
    }
    const results = data.map(item => {
      const obj = {}
      for (const k in item) {
        if (head[k]) {
          if (["create_time"].includes(k)){
            obj[head[k]] = dayjs(item[k]).format('YYYY-MM-DD HH:mm')
          }else if (k === "semesters"){
            obj[head[k]] = item[k]?.join('\n')
          }else if (k === "courses"){
            obj[head[k]] = item[k]?.join('\n')
          }else if (k === "is_active"){
            obj[head[k]] = item[k]?"已启用":"已禁用"
          }else{
            obj[head[k]] = item[k]
          }
        } 
      }
      return obj
    })
    // 创建工作表
    const dataExcel = XLSX.utils.json_to_sheet(results)
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    // 将工作表放入工作簿中
    XLSX.utils.book_append_sheet(wb, dataExcel, '账号')
    // 生成文件并下载
    XLSX.writeFile(wb, `账号${dayjs().format('YYYY-MM-DD HH:mm')}.xlsx`)
    
    showExportAccountVisible.value = false;
  }).catch((e) => {
    console.error(e);
  }).finally(() => {
  });
}

// 列表当前页更改
const handleTableChange = async (page, filters, sorter) => {
  console.log(page);
  refreshPage({page_size: page.pageSize, page_number: page.current, keyword: keyword.value})
};

const onShowSizeChange = (current: number, pageSize: number) => {
  console.log(current, pageSize);
  localStorage.setItem('AccountPageSize', JSON.stringify({pageSize: pageSize}));
  refreshPage({page_size: pageSize, page_number: current, keyword: keyword.value})
};

function changeShowBatchModifyAccountVisible(visible){
  showBatchModifyAccount.value = visible
  if(visible === false){
    formDataBatchModify.account_ids = []
    formDataBatchModify.password = ""
    formDataBatchModify.gender = ""
    formDataBatchModify.semester = ""
    formDataBatchModify.semesters_opt = 1
    formDataBatchModify.src_semester = ""
    formDataBatchModify.target_semester = ""
    formDataBatchModify.courses_opt = 1
    formDataBatchModify.src_course = ""
    formDataBatchModify.target_course = ""
    formDataBatchModify.department = ""
    formDataBatchModify.class_name = ""
    formDataBatchModify.student_type = ""
    formDataBatchModify.nation = ""
    formDataBatchModify.usage_limit = null
    formDataBatchModify.expire_date = null
    formDataBatchModify.remark = ""
    formDataBatchModify.partner = ""
    formDataBatchModify.model_group_id = null
  }
}

function changeShowCreateAccountVisible(visible){
  showCreateAccount.value = visible
  if(visible === false){
    formData.id = 0
    formData.username = ""
    formData.student_no = ""
    formData.student_card_no = ""
    formData.email = ""
    formData.nickname = ""
    formData.password = ""
    formData.gender = ""
    formData.semester = ""
    formData.semesters = [{id: uuidv4(), name:""}]
    formData.courses = [{id: uuidv4(), name:""}]
    formData.department = ""
    formData.class_name = ""
    formData.student_type = ""
    formData.mobile = ""
    formData.nation = ""
    formData.usage_limit = null
    formData.usage_no_limit = false
    formData.expire_date = null
    formData.expire_date_no_limit = false
    formData.model_no_limit = false
    formData.remark = ""
    formData.partner = ""
    formData.model_group_id = null
  }
}

function setImportUserModalVisible(visible){
  showImportUserModalVisible.value = visible;
  importUserCurrent.value = 0;
  importUserTableData.value = [];
  init_password.value = "";
  if (visible === false){
    refreshPage({keyword: keyword.value})
  }
}

function setModelGroupVisible(visible){
  modelGroupDrawer.value = visible;
}

function fetchChatRecord(record){
  router.push(`/myapp/chat/record?creator=${record.id}&username=${record.username}`);
}

function changeShowAccountPasswordResetVisible(visible, data){
  if (visible === true){
    formDataPassword.username = data.username
    formDataPassword.id = data.id
  }else{
    formDataPassword.username = ""
    formDataPassword.id = 0
  }
  showAccountPasswordReset.value = visible
}

function changeShowAccountPasswordRecordVisible(visible, record){
  if (visible){
    getPasswordRecords(record.id)
  }
  showAccountPasswordChangeRecord.value = visible
}

function getSiteUrl() {
  const url = import.meta.env.VITE_API_URL;
  return url.split("/api")[0]
}

function genPassword() {
  var chars = "0123456789abcdefghijklmnopqrstuvwxyz!@#$%^&*()ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  var passwordLength = 12;
  var password = "";
  for (var i = 0; i <= passwordLength; i++) {
    var randomNumber = Math.floor(Math.random() * chars.length);
    password += chars.substring(randomNumber, randomNumber +1);
  }
  formData.password = password;

  const loginUrl = getSiteUrl();
  const text = "登录地址：" + loginUrl + "\n" + "登录账号：" + formData.username + "\n" + "初始密码：" + password + "\n\n" + "当前为系统生成的初始密码，可登陆后修改密码使用。"
  copy(text)
  message.success('已复制')
}

function genPasswordModify() {
  var chars = "0123456789abcdefghijklmnopqrstuvwxyz!@#$%^&*()ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  var passwordLength = 12;
  var password = "";
  for (var i = 0; i <= passwordLength; i++) {
    var randomNumber = Math.floor(Math.random() * chars.length);
    password += chars.substring(randomNumber, randomNumber +1);
  }
  formDataBatchModify.password = password;
  const text = "生成的密码为：" + password
  copy(text)
  message.success('已复制')
}

function genResetPassword() {
  var chars = "0123456789abcdefghijklmnopqrstuvwxyz!@#$%^&*()ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  var passwordLength = 12;
  var password = "";
  for (var i = 0; i <= passwordLength; i++) {
    var randomNumber = Math.floor(Math.random() * chars.length);
    password += chars.substring(randomNumber, randomNumber +1);
  }
  formDataPassword.password = password;
  const loginUrl = getSiteUrl();
  const text = "登录地址："+ loginUrl + "\n" + "登录账号：" + formDataPassword.username + "\n" + "初始密码：" + password + "\n\n" + "当前为系统生成的初始密码，可登陆后修改密码使用。"
  copy(text)
  message.success('已复制')
}

function genInitPassword() {
  var chars = "0123456789abcdefghijklmnopqrstuvwxyz!@#$%^&*()ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  var passwordLength = 12;
  var password = "";
  for (var i = 0; i <= passwordLength; i++) {
    var randomNumber = Math.floor(Math.random() * chars.length);
    password += chars.substring(randomNumber, randomNumber +1);
  }
  init_password.value = password;
  const text = "初始密码：" + password
  copy(text)
  message.success('已复制')
}

const loading = ref(false);
const form = ref<FormInstance>();
const formReset = ref<FormInstance>();

const showInput = (index) => {
  console.log(index)
  state.inputVisible = true;
  state.inputValue = state.tags[index];
  nextTick(() => {
    inputRef.value.focus();
  });
};

const handleClose = (removedTag: string) => {
  const tags = state.tags.filter(tag => tag !== removedTag);
  console.log(tags);
  state.tags = tags;
};

const handleInputConfirm = () => {
  const inputValue = state.inputValue;
  let tags = state.tags;
  if (inputValue && tags.indexOf(inputValue) === -1) {
    tags = [...tags, inputValue];
  }
  console.log(tags);
  Object.assign(state, {
    tags,
    inputVisible: false,
    inputValue: '',
  });
};


const state = reactive({
  searchText: '',
  searchedColumn: '',
  max_column_count: 10,
  tags: [],
  inputVisible: false,
  inputValue: '',
});

function handleSearch(selectedKeys, confirm, dataIndex){
  console.log(selectedKeys, confirm, dataIndex)
  state.searchText = selectedKeys[0];
  state.searchedColumn = dataIndex;
  refreshPage({[dataIndex]: state.searchText})
}

const handleReset = clearFilters => {
  clearFilters({ confirm: true });
  state.searchText = '';
};

function adminBatchModifyAccount(){
  if(stateSelectedRowKeys.value.length === 0){
     message.error("请选择要批量操作的记录")
     return
  }
  loading.value = true;
  formDataBatchModify.account_ids = stateSelectedRowKeys.value.map((item) => {
    return parseInt(item);
  });
  formDataBatchModify.expire_date = formDataBatchModify.expire_date?formDataBatchModify.expire_date.format("YYYY-MM-DD"):null;
  batchModifyAccount(formDataBatchModify).then((res) => {
    const { code } = res;
    if (code === 0){
      message.success("批量修改成功");
      changeShowBatchModifyAccountVisible(false);
      refreshPage({keyword: keyword.value});
      stateSelectedRowKeysDict.value = [];
    }
  }).finally(() => {
    loading.value = false;
  }); 
}

function adminBatchClearAccountList(account_ids){
  if(account_ids.length === 0){
     message.error("请选择要批量操作的记录")
     return
  }
  loading.value = true;
  batchClearAccount({account_ids}).then((res) => {
    const { code } = res;
    if (code === 0){
      message.success("批量清空任务已创建成功，清空账号大概需要几分钟左右。");
      stateSelectedRowKeysDict.value = [];
    }
    refreshRunningTaskList();
  }).finally(() => {
    loading.value = false;
  }); 
}

function adminBatchClearAccount(){
  if(stateSelectedRowKeys.value.length === 0){
     message.error("请选择要批量操作的记录")
     return
  }
  loading.value = true;
  const account_ids = stateSelectedRowKeys.value.map((item) => {
    return parseInt(item);
  });
  adminBatchClearAccountList(account_ids);
}

function adminBatchMigrateAccountList(account_ids){
  if(account_ids.length === 0){
     message.error("请选择要批量操作的记录")
     return
  }
  loading.value = true;
  batchMigrateAccount({account_ids}).then((res) => {
    const { code } = res;
    if (code === 0){
      message.success("批量迁移任务已创建成功，清空账号大概需要几分钟左右。");
      stateSelectedRowKeysDict.value = [];
    }
    refreshRunningTaskList();
  }).finally(() => {
    loading.value = false;
  });
}

function adminBatchMigrateAccount(){
  if(stateSelectedRowKeys.value.length === 0){
     message.error("请选择要批量操作的记录")
     return
  }
  loading.value = true;
  const account_ids = stateSelectedRowKeys.value.map((item) => {
    return parseInt(item);
  });
  adminBatchMigrateAccountList(account_ids);
}

// 处理 InputNumber 变化事件
const handleUsageLimitChange = (value) => {
  console.log('输入值:', value);
  if (value && value > 0){
    formData.usage_no_limit = false;
  }
};

const handleUsageNoLimitChange = (e) => {
  console.log('复选框状态:', e.target.checked);
  if (e.target.checked){
    formData.usage_limit = null;
  }
}

const handleExpireDateChange = (value) => {
  console.log('输入值:', value);
  if (value){
    formData.expire_date_no_limit = false;
  }
}

const handleModelGroupChange = (value) => {
  console.log('输入值:', value);
  if (value){
    formData.model_no_limit = false;
  }
}

const handleExpireDateNoLimitChange = (e) => {
  console.log('复选框状态:', e.target.checked);
  if (e.target.checked){
    formData.expire_date = null;
  }
}

const handleModelNoLimitChange = (e) => {
  console.log('复选框状态:', e.target.checked);
  if (e.target.checked){
    formData.model_group_id = null;
  }
}

function adminAddAccount(){
  formData.gender = formData.gender?formData.gender:""
  formData.student_no = formData.student_no?formData.student_no:""
  formData.student_card_no = formData.student_card_no?formData.student_card_no:""
  formData.department = formData.department?formData.department:""
  formData.semester = formData.semester?formData.semester:""
  formData.course = formData.course?formData.course:""
  formData.class_name = formData.class_name?formData.class_name:""
  formData.student_type = formData.student_type?formData.student_type:""
  formData.mobile = formData.mobile?formData.mobile:""
  formData.nation = formData.nation?formData.nation:""
  formData.usage_limit = formData.usage_limit?formData.usage_limit:0;
  formData.expire_date = formData.expire_date?dayjs(formData.expire_date).format('YYYY-MM-DD'):null;
  formData.remark = formData.remark?formData.remark:"";
  formData.partner = formData.partner?formData.partner:"";
  formData.model_group_id = formData.model_group_id?formData.model_group_id:0;
  loading.value = true;
  form.value
    ?.validate()
    .then(() => {
      addAccount(formData).then((response) => {
        console.log(response);
        const {code} = response;
        if (code === 0){
          message.success(`保存成功`);
          changeShowCreateAccountVisible(false);
          form.value?.resetFields();
          refreshPage({keyword: keyword.value});
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

function adminResetPassword(){
  loading.value = true;
  formReset.value
    ?.validate()
    .then(() => {
      resetAccountPassword(formDataPassword).then((response) => {
        console.log(response);
        const {code} = response;
        if (code === 0){
          message.success(`保存成功`);
          changeShowAccountPasswordResetVisible(false, null);
          formReset.value?.resetFields();
        }
      }).catch((e) => {
        console.error(e);
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

const formData = reactive<any>({
  id: 0,
  username: "",
  email: "",
  nickname: "",
  password: "",
  student_no: "",
  student_card_no: "",
  gender: "",
  semester: "",
  course: "",
  semesters: [
    {id: uuidv4(), name: ""}
  ],
  courses: [
    {id: uuidv4(), name: ""}
  ],
  department: "",
  class_name: "",
  student_type: "",
  mobile: "",
  nation: "",
  usage_limit: null,
  usage_no_limit: false,
  expire_date: null,
  expire_date_no_limit: false,
  model_no_limit: false,
  remark: "",
  partner: "",
  model_group_id: null,
});

const formDataBatchModify = reactive<any>({
  account_ids: [],
  password: "",
  gender: "",
  semester: "",
  semesters_opt: 1,
  src_semester: "",
  target_semester: "",
  courses_opt: 1,
  src_course: "",
  target_course: "",
  department: "",
  class_name: "",
  student_type: "",
  nation: "",
  usage_limit: null,
  usage_no_limit: false,
  expire_date: null,
  expire_date_no_limit: false,
  model_no_limit: false,
  remark: "",
  partner: "",
  model_group_id: null,
});

const formDataPassword = reactive<any>(
  {
    id: 0,
    username: "",
    password: "",
  }
);

const importUserTableColumns = ref([]);

const passwordTableColumns = [
    {
      title: '修改人',
      dataIndex: 'account',
      key: 'account',
      width: 150,
      fixed: 'left',
    }, 
    {
      title: '修改时间',
      align: 'center',
      width: 180,
      dataIndex: 'create_time',
      key: 'create_time', 
    },
    {
      title: '密码',
      dataIndex: 'password',
      key: 'password',
    }, 
    {
      title: '备注',
      align: 'center',
      dataIndex: 'remark',
      key: 'remark',
    },
]

const columns = [
    {
      title: '账号',
      dataIndex: 'username',
      key: 'username',
      width: 150,
      fixed: 'left',
    },
    // {
    //   title: '学号/校园卡号',
    //   dataIndex: 'student_no',
    //   key: 'student_no',
    //   width: 130,
    //   ellipsis: true,
    // },
    {
      title: '姓名',
      dataIndex: 'nickname',
      key: 'nickname',
      ellipsis: true,
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      width: 60,
      // filters: [
      //   { text: '男', value: '男' },
      //   { text: '女', value: '女' },
      // ],
      // filteredValue: filtered.name || null,
      ellipsis: true,
    },
    {
      title: '院系',
      dataIndex: 'department',
      key: 'department',
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'class_name',
      key: 'class_name',
      ellipsis: true,
    },
    {
      title: '学期',
      dataIndex: 'semesters',
      key: 'semesters',
      ellipsis: true,
    },
    {
      title: '课程',
      dataIndex: 'courses',
      key: 'courses',
      ellipsis: true,
    },
    {
      title: '学生类型',
      dataIndex: 'student_type',
      key: 'student_type',
      width: 100,
      ellipsis: true,
    },
    {
      title: '联系电话', 
      dataIndex: 'mobile',
      key: 'mobile',
      ellipsis: true,
    },
    {
      title: '电子邮箱',
      dataIndex: 'email',
      key: 'email',
      ellipsis: true,
    },
    {
      title: '国籍',
      dataIndex: 'nation',
      key: 'nation',
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'partner',
      key: 'partner',
      ellipsis: true,
    },
    {
      title: '账号状态',
      width: 100,
      dataIndex: 'is_active',
      key: 'is_active',
      fixed: 'right',
    },
    {
      title: '可用量',
      width: 140,
      dataIndex: 'usage_limit',
      key: 'usage_limit',
      fixed: 'right',
    },
    {
      title: '有效期限',
      width: 100,
      dataIndex: 'expire_date',
      key: 'expire_date',
      fixed: 'right',
    },
    {
      title: '模型权限',
      width: 120,
      dataIndex: 'model_group_id',
      key: 'model_group_id',
      fixed: 'right',
    },
    {
      title: '创建时间',
      align: 'center',
      width: 150,
      dataIndex: 'create_time',
      key: 'create_time',
      fixed: 'right',
    },
    {
      title: '最后登录时间',
      align: 'center',
      width: 150,
      dataIndex: 'last_login',
      key: 'last_login',
      fixed: 'right',
    },
    {
      title: '备注',
      align: 'center',
      dataIndex: 'remark',
      key: 'remark',
      fixed: 'right',
      ellipsis: true,
    },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'operation',
      key: 'operation',
      width: 180,
      fixed: 'right',
    },
];

function refreshPage(params){
  const {page_size} = params;
  if (page_size === undefined){
    params.page_size = defaultPageSize.value;
  }
  const data = getAccountPage(params).then((res) => {
    const { data } = res;
    console.log(data);
    accountPage.value = data;
    return data;
  });
  return data;
}


function getPasswordRecords(account_id){
  const params = {account_id}
  const data = getChangeAccountPasswordRecords(params).then((res) => {
    const { data } = res;
    console.log(data);
    passwordRecords.value = data;
    return data;
  });
  return data;
}


function active(record, delete_flag, is_active){
  disableAccount({account_id: record.id, delete_flag: delete_flag, is_active: is_active}).then((res) => {
    const { code } = res;
    if (code === 0){
      const opt = delete_flag?"删除":is_active?"启用":"禁用"
      message.success(`${opt}成功`);
      refreshPage({keyword: keyword.value});
    }
  });
};
function batchDisableAccountRecord(delete_flag, is_active){
  if(stateSelectedRowKeys.value.length === 0){
     message.error("请选择要批量操作的记录")
     return
  }
  batchDisableAccount({account_ids: stateSelectedRowKeys.value, delete_flag: delete_flag, is_active: is_active}).then((res) => {
    const { code } = res;
    if (code === 0){
      const opt = delete_flag?"批量删除":is_active?"批量启用":"批量禁用"
      message.success(`${opt}成功`);
      refreshPage({keyword: keyword.value});
      stateSelectedRowKeysDict.value = [];
    }
  });
}

function refreshRunningTaskList(){
  getRunningTaskList().then((res) => {
    const { code, data } = res;
    if (code === 0){
      runningTask.value = data;
    }
  });
}

function changeAccountRecord(record){
  formData.id = record.id;
  formData.username = record.username;
  formData.email = record.email;
  formData.nickname = record.nickname;
  formData.student_no = record.student_no;
  formData.student_card_no = record.student_card_no;
  formData.gender = record.gender;
  formData.semester = record.semester;
  formData.course = record.course;
  let semesters;
  if(record.semesters?.length > 0){
    semesters = record.semesters.map(semester => {
      return {id: uuidv4(), name: semester}
    })
  }else{
    semesters = [{id: uuidv4(), name: ""}]
  }
  formData.semesters = semesters;
  let courses;
  if(record.courses?.length > 0){
    courses = record.courses.map(course => {
      return {id: uuidv4(), name: course}
    })
  }else{
    courses = [{id: uuidv4(), name: ""}]
  }
  formData.courses = courses;
  formData.department = record.department;
  formData.class_name = record.class_name;
  formData.student_type = record.student_type;
  formData.mobile = record.mobile;
  formData.nation = record.nation;
  formData.usage_limit = record.usage_limit>0?record.usage_limit-record.actual_usage:null;
  formData.usage_no_limit = record.usage_limit === 0;
  formData.expire_date = record.expire_date?dayjs(record.expire_date, "YYYY-MM-DD"):null;
  formData.expire_date_no_limit = !record.expire_date;
  formData.model_no_limit = !record.model_group_id;
  formData.remark = record.remark;
  formData.partner = record.partner;
  formData.model_group_id = record.model_group_id?`${record.model_group_id}`:null;
  changeShowCreateAccountVisible(true);
}

function removeSemester(item){
  const index = formData.semesters.indexOf(item);
  if (index !== -1) {
    formData.semesters.splice(index, 1);
  }
}

function removeCourse(item, isBatch){
  if (isBatch === true){
    const index = formDataBatchModify.courses.indexOf(item);
    if (index !== -1) {
      formDataBatchModify.courses.splice(index, 1);
    }
  }else{
    const index = formData.courses.indexOf(item);
    if (index !== -1) {
      formData.courses.splice(index, 1);
    }
  }
}

function addSemesters(){
  formData.semesters.push(
    {id: uuidv4(), name: ""}
  )
}

function addCourses(isBatch){
  if(isBatch === true){
    formDataBatchModify.courses.push(
      {id: uuidv4(), name: ""}
    )
  }else{
    formData.courses.push(
      {id: uuidv4(), name: ""}
    )
  }
}

const stateSelectedRowKeysDict = ref({});
const stateSelectedRowKeys = ref([]);

const rowSelection: TableProps['rowSelection'] = {
  selectedRowKeys: stateSelectedRowKeys,
  onChange: (selectedRowKeys: string[], selectedRows: DataType[]) => {
    console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    stateSelectedRowKeysDict.value = {
      ...stateSelectedRowKeysDict.value,
      [accountPage.value.page_number]: selectedRowKeys
    }
  },
  getCheckboxProps: (record: DataType) => ({
    disabled: record.is_superuser, // Column configuration not to be checked
    key: record.key,
  })
};

const groupModelTableList = ref([]);

function getModelGroupTableList(){
  getModelGroupList().then((res) => {
    const { data } = res;
    console.log(data);
    groupModelTableList.value = data;
    return data;
  });
}

const modelGroupOptions = computed(() => {
  const haveModels = groupModelTableList.value.filter(group => group.models.length > 0)
  return haveModels.map(item => {
      return {
        label: `${item.name}`,
        value: `${item.id}`,
      }
    }
  )
});

watch(stateSelectedRowKeysDict, (val) => {
  let result = [];
  for (let key in val) {
    result.push(...val[key]); 
  }
  stateSelectedRowKeys.value = result;
});

onMounted(() => {
  refreshPage({});
  refreshRunningTaskList();
  getModelGroupTableList();

  const tmp = JSON.parse(localStorage.getItem('AccountSettingColumnKeys')) || [];
  if (tmp.length > 0){
    columnSetting.value = tmp
    if(!columnSetting.value.includes("username")){
      columnSetting.value.push("username")
    }
  }else{
    columnSetting.value = ["username", "nickname", "department", "class_name", "mobile", "email", "usage_limit", "expire_date", "remark"]
  }
});
</script>

<style scoped>
.limit-popover-width {
  max-width: 50%;
  word-wrap: break-word;
}

.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 16px;
}

.steps-action {
  margin-top: 24px;
  text-align: center;
}

[data-theme='dark'] .steps-content {
  background-color: #2f2f2f;
  border: 1px dashed #404040;
}
</style>