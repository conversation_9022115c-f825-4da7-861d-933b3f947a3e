import { AxiosRequestConfig, AxiosResponse } from 'axios';
import createHttp from '@/utils/axiosHttp';
import { isResponse } from '@/types';
import NProgress from 'nprogress';
import { Modal, message } from 'ant-design-vue';

const http = createHttp({
  timeout: 10000,
  baseURL: 'api',
  withCredentials: true,
  xsrfCookieName: 'Authorization',
  xsrfHeaderName: 'Authorization',
});

const isAxiosResponse = (obj: any): obj is AxiosResponse => {
  return typeof obj === 'object' && obj.status && obj.statusText && obj.headers && obj.config;
};

// progress 进度条 -- 开启
http.interceptors.request.use((req: AxiosRequestConfig) => {
  // Skip progress indicator if X-No-Progress header is set
  if (req.headers && req.headers['X-No-Progress'] === 'true') {
    return req;
  }
  
  if (!NProgress.isStarted()) {
    NProgress.start();
  }
  return req;
});

// 解析响应结果
http.interceptors.response.use(
  (rep: AxiosResponse<String>) => {
    const { data } = rep;
    if (isResponse(data)) {
      return data.code === 0 ? data : Promise.reject(data);
    }
    return Promise.reject({ message: rep.statusText, code: rep.status, data });
  },
  (error) => {
    if (error.response && error.response.status === 401 
      && error.response.data.status !== 'not_authenticated') {
      tokenError();
    }else if (error.response && error.response.status !== 200){
      const {message} = error.response.data;
      tipErrorMsg(message);
    }else if (error.response && isAxiosResponse(error.response)) {
      return Promise.reject({
        message: error.response.statusText,
        code: error.response.status,
        data: error.response.data,
      });
    }
    return Promise.reject(error);
  }
);

// progress 进度条 -- 关闭
http.interceptors.response.use(
  (rep) => {
    if (NProgress.isStarted()) {
      NProgress.done();
    }
    return rep;
  },
  (error) => {
    if (NProgress.isStarted()) {
      NProgress.done();
    }
    return error;
  }
);

const tokenError = () => {
  Modal.error({
    title: '登录凭证失效',
    content: '账号登录当前凭证已失效，需要重新登录',
    okText: '重新登录',
    onOk() {
      http.removeAuthorization();
      window.location.assign(window.location.origin + '/#/login')
    },
  });
};

const tipErrorMsg = (msg) => {
  message.error(msg)
};

export default http;
