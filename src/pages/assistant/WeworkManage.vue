<template>
  <a-table 
    size="small"
    :columns="columns"
    :dataSource="weworkGroupAssistantPage.results"
    :pagination="{ pageSize: weworkGroupAssistantPage.page_size, current: weworkGroupAssistantPage.page_number, total: weworkGroupAssistantPage.count }"
    >
    <template #title>
      <div class="text-subtext text-sm flex justify-between items-center">
        <span style="font-size:14px;">绑定企业微信群助手账号</span>
        <a-button style="margin-right: 5px;" type="primary" @click="chagegeQwAccountDialog(true)">
          <PlusOutlined class="text-lg" />绑定账号
        </a-button>
      </div>
    </template>
    <template #headerCell="{ column }">
      <template v-if="column.key === 'secret_key'">
        秘钥
        <a-popover title="" trigger="hover" placement="right">
          <template #content>
            企微群助手账号与平台的通信凭证
          </template>
          <QuestionCircleOutlined />
        </a-popover>
      </template>
    </template>
    <template #bodyCell="{ text, record, index, column }">
      <template v-if="column.dataIndex === 'create_time'">
        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
      <template v-else-if="column.dataIndex === 'secret_key'">
        {{ isSecretKeyVisible[record.id]?text:text.substring(1, 3)+"******************************" }}
        <a-button type="link" @click="changeSecretKeyVisible(record)">{{ !isSecretKeyVisible[record.id] ? "显示" : "隐藏" }}</a-button>
      </template>
      <template v-else-if="column.dataIndex === 'status'">
        <span v-if="record.status === '在线'">
          <a-badge color="lime" :text="record.status"/>
        </span>
        <span v-else-if="['离线', '退出登录'].includes(record.status)">
          <a-badge color="red" :text="record.status"/>
        </span>
        <span v-else>{{ text }}</span>
      </template>
      <template v-else-if="column.dataIndex === 'last_answer_time'">
        {{ text?dayjs(text).format('YYYY-MM-DD HH:mm'):'-' }}
      </template>
      <template v-else-if="column.dataIndex === 'operation'">
        <a style="font-weight: normal;" @click="downloadSoft(record)">下载</a>
      </template>
    </template>
  </a-table>
  <a-modal :okButtonProps="{ loading }" v-model:visible="showForm" title="绑定企微群助手账号"
    @cancel="chagegeQwAccountDialog(false)"
    width="1200px"
    style="top: 0px;"
    :footer="null"
  >
    <div>
      <a-steps :current="current">
        <a-step v-for="item in steps" :key="item.title" :title="item.title" />
      </a-steps>
      <div class="steps-content">
        {{ steps[current].content }}
        <div v-if="current === 0" style="margin-top: 8px;">
          <p>请先下载指定版本的企业微信，版本: 4.0.8.6027，文件大小: 338MB</p>
          <p>
            <a :href="weworkDownloadUrl" target="_blank">点击下载</a> &nbsp;
            <a @click="copyDownloadUrl(weworkDownloadUrl)">复制下载地址</a>
          </p>
        </div>
        <div v-if="current === 1" style="margin-top: 8px;">
          <a-form ref="form" :model="formData" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16, offset: 1 }">
            <a-form-item required name="account" label="企业微信账号"
              :rules="[{ required: true, message: '请输入企业微信账号' }]"
            >
              <a-input 
                v-model:value="formData.account" 
                placeholder="请输入企业微信账号(手机号)" 
                show-count 
                :maxlength="50"
              />
            </a-form-item>
          </a-form>
        </div>
        <div v-if="current === 2" style="margin-top: 8px;">
          <p>下载企微群助手.zip，版本: V1，文件大小: 31.1MB</p>
          <p>
            <a :href="weworkAssistantInstallPkgDownloadUrl" target="_blank">点击下载</a> &nbsp;
            <a @click="copyDownloadUrl(weworkAssistantInstallPkgDownloadUrl)">复制下载地址</a>
          </p>
        </div>
      </div>
      <div class="steps-action">
        <a-button v-if="current > 0" @click="prev" style="margin-right: 8px">上一步</a-button>
        <a-button v-if="current < steps.length - 1" type="primary" @click="next">下一步</a-button>
        <a-button
          v-if="current == steps.length - 1"
          type="primary"
          @click="bindComplete"
        >
          完成
        </a-button>
      </div>
    </div>
  </a-modal>
  <a-drawer 
    title="软件下载"
    :closable="true" 
    size="large" 
    :visible="downloadSoftDrawer" 
    @close="downloadSoftDrawer = false"
  >
    <div class="steps-content">
      <div style="margin-top: 8px;">
        <p>下载企微群助手.zip，版本: V1，文件大小: 31.1MB</p>
        <p>
          <a :href="weworkAssistantInstallPkgDownloadUrl" target="_blank">点击下载</a> &nbsp;
          <a @click="copyDownloadUrl(weworkAssistantInstallPkgDownloadUrl)">复制下载地址</a>
        </p>
      </div>
    </div>
    <div class="steps-content">
      <div style="margin-top: 8px;">
        <p>指定版本的企业微信，版本: 4.0.8.6027，文件大小: 338MB</p>
        <p>
          <a :href="weworkDownloadUrl" target="_blank">点击下载</a> &nbsp;
          <a @click="copyDownloadUrl(weworkDownloadUrl)">复制下载地址</a>
        </p>
      </div>
    </div>
  </a-drawer>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { FormInstance } from 'ant-design-vue';
import { groupAssistantPage, addGroupAssistant } from "@/api/group_assistant";
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { useClipboard } from '@vueuse/core'


const { copy } = useClipboard({ legacy: true })

const weworkDownloadUrl = ref("https://dldir1.qq.com/wework/work_weixin/WeCom_4.0.8.6027.exe");
const weworkAssistantInstallPkgDownloadUrl = ref(null);

const loading = ref(false);
const showForm = ref(false);
const downloadSoftDrawer = ref(false);

const columns = [
  {
    title: '企业微信账号',
    dataIndex: 'account',
    key: 'account',
  }, 
  {
    title: '姓名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
  },
  {
    title: '已完成回答',
    key: 'answer_count',
    dataIndex: 'answer_count',
    sorter: {
      compare: (a, b) => a.answer_count - b.answer_count,
      multiple: 1,
    },
  },
  {
    title: '最近回答时间',
    key: 'last_answer_time',
    dataIndex: 'last_answer_time',
  },
  {
    title: '创建时间',
    key: 'create_time',
    dataIndex: 'create_time',
  },
  {
    title: '秘钥',
    dataIndex: 'secret_key',
    key: 'secret_key',
    width: 350,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 100,
    fixed: 'right',
  },
];

const current = ref<number>(0);

const next = () => {
  console.log(current.value);
  if(current.value === 1){
    submit()
  }else{
    current.value++;
  }
};

const bindComplete = () => {
  message.success('恭喜您，已完成企微账号绑定！');
  chagegeQwAccountDialog(false);
  getGroupAssistantPage();
}

const prev = () => {
  current.value--;
};

const steps = [
  {
    title: '登录企微账号',
    content: '在Windows系统上下载安装指定版本的企业微信',
  },
  {
    title: '填写企微账号',
    content: '填写刚刚登录的企微账号',
  },
  {
    title: '运行企微群助手',
    content: '在Windows系统上下载安装企微群助手',
  },
];

const weworkGroupAssistantPage = ref([]);

function chagegeQwAccountDialog(visible){
  if(visible === false){
    formData.value = {
      account: null,
    }
  }
  showForm.value = visible
}

const formData = ref({
  account: null,
});

const form = ref<FormInstance>();

function submit() {
  loading.value = true;
  form.value
    ?.validate()
    .then(() => {
      addGroupAssistant(formData.value).then((res) => {
        const { code, data } = res;
        console.log(data);
        // getGroupAssistantPage();
        if(code === 0){
          const { install_pkg_url } = data;
          console.log(install_pkg_url);
          weworkAssistantInstallPkgDownloadUrl.value = install_pkg_url;
          current.value++;
        }
      });
      // chagegeQwAccountDialog(false);
    })
    .finally(() => {
      loading.value = false;
    });
}

function getGroupAssistantPage(){
  const data = groupAssistantPage().then((res) => {
    const { data } = res;
    console.log(data);
    weworkGroupAssistantPage.value = data;
    return data;
  });
  return data;
}

function downloadSoft(record){
  downloadSoftDrawer.value = true;
  weworkAssistantInstallPkgDownloadUrl.value = record.install_pkg_url;
}

const isSecretKeyVisible = ref({});

const changeSecretKeyVisible = (record) => {
  isSecretKeyVisible.value[record.id] = !isSecretKeyVisible.value[record.id];
}

async function copyDownloadUrl(url){
  await copy(url)
  message.success("已复制")
}

onMounted(() => {
  getGroupAssistantPage();
})
</script>
<style scoped>
.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 60px;
}

.steps-action {
  margin-top: 24px;
  text-align: center;
}

[data-theme='dark'] .steps-content {
  background-color: #2f2f2f;
  border: 1px dashed #404040;
}
</style>