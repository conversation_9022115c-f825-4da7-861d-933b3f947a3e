<template>
  <div
    style="width: 100%; height: 400px"
    class="line-chart"
    ref="container"
  ></div>
</template>
<script lang="ts" setup>
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import type { EChartsType } from 'echarts';
  import * as echarts from 'echarts';
  import dayjs from 'dayjs';
  import { formatThousand } from '@/utils/formatter';

  let chart: EChartsType | null = null;
  const container = ref<HTMLElement>();

  const props = defineProps({
    list: Array,
  });

  function resize() {
    chart?.resize();
  }

  onMounted(() => {
    let totalCallCountLineList = [];
    props.list.forEach(({ date, call_count }) => {
      totalCallCountLineList.push([dayjs(date).format('YYYY/MM/DD'), call_count]);
    });

    let totalTokensLineList = [];
    props.list.forEach(({ date, total_tokens_sum }) => {
      totalTokensLineList.push([dayjs(date).format('YYYY/MM/DD'), total_tokens_sum]);
    });
    chart = echarts.init(container.value!);
    chart.setOption({
      color: ['#005af9', '#985af9',],
      grid: [
        {
          top: 80,
          left: 42,
          right: 36,
          bottom: 20,
        },
      ],
      xAxis: [
        {
          name: '日期',
          nameTextStyle: { color: 'rgba(0 , 0, 0, 0)' },
          type: 'category',
          // axisTick: { show: false },
          // axisLine: { show: false },
          boundaryGap: 0,
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          name: 'Token用量 (k)',
          nameTextStyle: { color: 'rgba(0 , 0, 0, 0)' },
          type: 'value',
          // axisTick: { show: false },
          // axisLine: { show: false },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              width: 1,
              color: 'rgba(0, 0, 0, 0.15)',
            },
          },
          emphasis: {
            focus: 'series'
          },
          axisLabel: {
            formatter: function (value, index){
              return formatThousand(value, 0)
            }
          },
        },
        { 
          name: '对话次数',
          nameTextStyle: { color: 'rgba(0 , 0, 0, 0)' },
          type: 'value',
          nameLocation: 'start',
          alignTicks: true,
          // axisTick: { show: false },
          // axisLine: { show: false },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              width: 1,
              color: 'rgba(0, 0, 0, 0.15)',
            },
          },
          axisLabel: {
            formatter: function (value, index){
              return formatThousand(value/1000, 0)
            }
          },
        },
      ],
      legend: {
        show: true,
        right: '10',
        top: 0,
        orient: 'vertical',
      },
      tooltip: {
        show: true,
        trigger: 'axis',
      },
      series: [
        {
          name: '对话次数',
          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
          },
          data: totalCallCountLineList,
        },
        {
          yAxisIndex: 1,
          name: 'Token用量 (k)',
          type: 'line',
          smooth: true,
          width: 4,
          lineStyle: {
            width: 3,
          },
          tooltip: {
            valueFormatter: value => formatThousand(value/1000, 0)
          },
          data: totalTokensLineList,
        },
      ],
    });
    window.addEventListener('resize', resize);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', resize);
  });
</script>
