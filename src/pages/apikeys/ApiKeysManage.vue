<template>
  <div class="authority">
    <a-table :columns="columns" :dataSource="apikeyList" :pagination="false" :scroll="{x: 1200}">
      <template #title>
        <div class="flex justify-between">
          <h1>API keys</h1>
        </div>
        <div>
          <a-alert
            message="下面列出了您的API密钥。请注意，不要与他人共享您的API密钥，也不要在浏览器或其他客户端代码中公开它。"
            type="warning"
            style="background-color: #fffbe6; border: 1px solid #ffe58f;"
          />
        </div>
      </template>
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'operation'">
          <!-- <a-button class="text-xs" type="link" size="small" @click="run(record)">体验</a-button> -->
          <!-- <a-button class="text-xs ml-base" type="primary" size="small" @click="edit(record)">编辑</a-button> -->
          <a @click="edit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm placement="topLeft" title="确认删除？" @confirm="remove(record)" ok-text="确认" cancel-text="取消">
            <!-- <a-button class="text-xs ml-base" v-auth:delete danger size="small">删除</a-button> -->
            <a style="color: red;">删除</a>
          </a-popconfirm>
        </template>
        <template v-else-if="column.dataIndex === 'api_key'">
          {{ isApiKeyVisible[record.id]?text:text.substring(1, 3)+"******************************" }}
          <a-button type="link" @click="changeApiKeyVisible(record)">{{ !isApiKeyVisible[record.id] ? "显示" : "隐藏" }}</a-button>
        </template>
        <template v-else-if="column.dataIndex === 'create_time'">
          {{ text?dayjs(text).format('YYYY-MM-DD'):'-' }}
        </template>
      </template>
    </a-table>
    <div style="margin-top: 15px;">
      <a-button type="primary" @click="add">
        <template #icon>
          <PlusOutlined />
        </template>
        创建secret key
      </a-button>
    </div>
    <a-modal :okButtonProps="{ loading }" width="540px" v-model:visible="showForm" :title="title" @ok="submit" 
      ok-text="确认" cancel-text="取消"
    >
      <a-form ref="form" :model="formData" :labelCol="{ span: 5 }" :wrapperCol="{ span: 16, offset: 1 }">
        <a-form-item required name="name" label="名称"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input v-model:value="formData.name" placeholder="请输入名称" show-count :maxlength="50" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  // @ts-ignore
  import { reactive, ref, onMounted } from 'vue';
  import { FormInstance } from 'ant-design-vue';
  import { useApikeyStore, ApiKeyProps } from '@/store/apikey';
  import { storeToRefs } from 'pinia';
  import dayjs from 'dayjs';
  import { useAuthStore } from '@/plugins';


  const { useAuth } = useAuthStore();

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: 'KEY',
      dataIndex: 'api_key',
    },
    {
      title: '创建日期',
      dataIndex: 'create_time',
    },
    {
      title: '最后使用日期',
      dataIndex: 'last_used',
    },
    {
      width:120,
      title: '操作',
      align: 'center',
      dataIndex: 'operation',
      fixed: "right",
    },
  ];

  const showForm = ref(false);

  const formData = reactive<ApiKeyProps>({
    id: undefined,
    name: '',
    title: undefined,
  });

  const form = ref<FormInstance>();

  const loading = ref(false);
  const isApiKeyVisible = ref({});

  const changeApiKeyVisible = (record) => {
      isApiKeyVisible.value[record.id] = !isApiKeyVisible.value[record.id];
  }

  function submit() {
    loading.value = true;
    form.value
      ?.validate()
      .then(() => {
        formData.id ? updateApikey(formData) : addApikey(formData);
        showForm.value = false;       
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const title = ref('创建secret key');

  const run = (record) => {
    const href = "/t/chat?api_key=" + record.api_key;
    window.open(href, '_blank')
  }

  const edit = useAuth('edit', function (record: ApiKeyProps) {
    form.value?.resetFields();
    formData.id = record.id;
    formData.name = record.name;
    showForm.value = true;
    title.value = '编辑';
  });

  function add() {
    form.value?.resetFields();
    formData.id = undefined;
    formData.name = '';
    formData.title = undefined;
    showForm.value = true;
    title.value = '创建secret key';
  }

  const remove = useAuth('delete', function (record: ApiKeyProps) {
    removeApikey(record.id!);
  }); 

  const apikeyStore = useApikeyStore();

  const { getApikeyList, addApikey, updateApikey, removeApikey } = apikeyStore;
  const { apikeyList } = storeToRefs(apikeyStore);

  onMounted(() => {
    getApikeyList();
    apikeyList.value.forEach(({ id }) => {
      isApiKeyVisible.value[id] = false;
    });
  });
</script>