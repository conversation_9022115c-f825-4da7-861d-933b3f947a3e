import { Response } from '@/types';
import http from "@/store/http";

// Get all team projects
export const getTeamProjects = (params) => 
  http.request<any, Response<any>>("/team/manage", 'GET', params);

// Get team project details by ID
export const getTeamDetail = (params) => 
  http.request<any, Response<any>>("/team/detail", 'GET', params);

// Create a new team project
export const createTeamProject = (params) => 
  http.request<any, Response<any>>("/team/manage", 'POST_JSON', params);

// Update a team project
export const updateTeamProject = (params) => 
  http.request<any, Response<any>>("/team/manage", 'POST_JSON', params);

// Delete a team project
export const deleteTeamProject = (params) => 
  http.request<any, Response<any>>("/team/manage", 'DELETE', params);

// Get team members
export const getTeamMembers = (params) => 
  http.request<any, Response<any>>("/account/manage", 'GET', params);

// Add team members
export const addTeamMembers = (params) => 
  http.request<any, Response<any>>("/team/account", 'POST_JSON', params);

// Delete team members
export const deleteTeamMembers = (params) => 
  http.request<any, Response<any>>("/team/account", 'DELETE', params);

// Get team workflows
export const getTeamWorkflows = (params) => 
  http.request<any, Response<any>>("/app/release", 'GET', params, { headers: { 'X-No-Progress': 'true' } });

// Create a team workflow
export const createTeamWorkflow = (params) => 
  http.request<any, Response<any>>("/app/release", 'POST_JSON', params);

//Delete a team workflow
export const deleteTeamWorkflow = (params) => 
  http.request<any, Response<any>>("/app/release", 'DELETE', params); 

// Get team knowledge bases
export const getTeamKnowledgeBases = (params) => 
  http.request<any, Response<any>>("/resource/knowledge_base", 'GET', params);

// Create a team knowledge base
export const createTeamKnowledgeBase = (params) => 
  http.request<any, Response<any>>("/resource/knowledge_base", 'POST_JSON', params);

// Delete a team knowledge base
export const deleteTeamKnowledgeBase = (params) => 
  http.request<any, Response<any>>("/resource/knowledge_base", 'DELETE', params);

// Get team list for selection
export const getTeamList = (params) => 
  http.request<any, Response<any>>("/team/list", 'GET', params); 